_products:
  resource: "products/products.yml"
  prefix: /products/

_products_v2:
  resource: "products_v2/product.yml"
  prefix: /products-v2/

_categories:
  resource: "categories.yml"
  prefix: /categories/

_suppliers:
  resource: "suppliers.yml"
  prefix: /suppliers/

_manufacturers:
  resource: "manufacturer.yml"
  prefix: /brands

_monitoring:
  resource: "monitoring.yml"
  prefix: /monitoring

_catalog_price_rules:
  resource: "catalog_price_rule.yml"
  prefix: /catalog-price-rules

_features:
  resource: "features.yml"
  prefix: /features

_attributes:
  resource: "attribute.yml"
  prefix: /attribute-groups/{attributeGroupId}/attributes/
  requirements:
    attributeGroupId: \d+

_attribute_group:
  resource: "attribute_group.yml"
  prefix: /attribute-groups/

_cart_rule:
  resource: "cart_rule.yml"
  prefix: /cart-rules

_ass_cart_rule:
  resource: "ass_cart_rule.yml"
  prefix: /ass_cart_rules

_material:
  resource: "material.yml"
  prefix: /material/

_sites:
  resource: "sites.yml"
  prefix: /sites

_comments:
  resource: "comment.yml"
  prefix: /comments

_import_comments:
  resource: "import_comment.yml"
  prefix: /import_comments

_email_templates:
  resource: "email_templates.yml"
  prefix: /email_templates

_bridesmaid_product_images:
  resource: "bridesmaid_product_image.yml"
  prefix: /bridesmaid_product_image

_product_manages:
  resource: "product_manages/product_manage.yml"
  prefix: /product_manage

_category_products:
  resource: "category_product.yml"
  prefix: /category_product

_search_sets:
  resource: "search_set.yml"
  prefix: /search_set

_search_weights:
  resource: "search_weight.yml"
  prefix: /search_weight

_clear_search_redis:
  resource: "clear_search_redis.yml"
  prefix: /clear_search_redis

_attribute_color_group:
  resource: "attribute_color_group.yml"
  prefix: /attribute_color_group