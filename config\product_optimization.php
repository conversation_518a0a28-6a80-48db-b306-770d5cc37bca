<?php
/**
 * 产品变体优化配置文件
 * 用于处理大量变体时的性能优化设置
 */

return [
    // 变体处理配置
    'variants' => [
        // 最大变体数量，超过此数量将使用简化处理
        'max_variants' => 5000,
        
        // 简化处理时的变体数量限制
        'simplified_limit' => 1000,
        
        // 批处理大小
        'batch_size' => 500,
        
        // 垃圾回收频率（每处理多少批后执行一次）
        'gc_frequency' => 2,
    ],
    
    // 内存和时间限制
    'limits' => [
        // 内存限制
        'memory_limit' => '512M',
        
        // 执行时间限制（秒）
        'max_execution_time' => 300,
    ],
    
    // 缓存配置
    'cache' => [
        // 是否启用属性信息缓存
        'enable_attribute_cache' => true,
        
        // 缓存过期时间（秒）
        'cache_ttl' => 3600,
        
        // 缓存键前缀
        'cache_prefix' => 'product_attributes_',
    ],
    
    // 数据库优化
    'database' => [
        // 是否使用批量查询
        'use_batch_queries' => true,
        
        // 批量查询的最大ID数量
        'max_batch_ids' => 1000,
    ],
    
    // 图片处理优化
    'images' => [
        // 是否预处理图片链接
        'preprocess_links' => true,
        
        // 是否缓存图片链接
        'cache_image_links' => true,
    ],
    
    // 日志配置
    'logging' => [
        // 是否记录性能日志
        'enable_performance_log' => true,
        
        // 是否记录大量变体的产品
        'log_large_variants' => true,
        
        // 大量变体的阈值
        'large_variants_threshold' => 10000,
    ],
];
