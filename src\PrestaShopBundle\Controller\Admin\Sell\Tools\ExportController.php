<?php

declare(strict_types=1);

namespace PrestaShopBundle\Controller\Admin\Sell\Tools;

use Db;
use Exception;
use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Product;
use Shop;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tools;
use SimpleXMLElement;
use Context;

class ExportController extends FrameworkBundleAdminController
{
    public function __construct()
    {
        parent::__construct();
    }
    public function indexAction()
    {
        return $this->render('@PrestaShop/Admin/Sell/Tools/Export/index.html.twig');
    }

    /**
     * 导出评论数据
     */
    public function exportReviewAction(Request $request): Response
    {
        set_time_limit(0);
        ini_set('display_errors', 'on');
        ini_set('memory_limit', -1);
        try {
            $format = Tools::getValue('export_format', 'xml');
            // dd($format);
            $filename = $this->exportComments($format);
        } catch (Exception $e) {
            $this->getContext()->smarty->assign([
                'success' => false,
                'error' => $e->getMessage(),
            ]);
        }
        return $this->redirectToRoute('admin_exports_index');
    }

    public function exportComments($format = 'xml')
    {
        $filename = _PS_ROOT_DIR_ . '/upload/export/review_export.' . $format;
        if (!is_dir(_PS_ROOT_DIR_ . '/upload/export')) {
            mkdir(_PS_ROOT_DIR_ . '/upload/export', 0755, true);
        }

        // 查询评论数据
        $sql = '
            SELECT 
                c.id_product_comment,
                c.id_product,
                c.id_customer,
                ci.path AS image_path,
                cg.grade,
                cu.firstname AS customer_firstname,
                cu.lastname AS customer_lastname,
                p.id_product AS product_id,
                p.supplier_reference AS sku,
                pl.name AS product_name,
                CONCAT(su.physical_uri, "index.php?id_product=", p.id_product, "&controller=product") AS product_link,
                c.grade,
                c.title,
                c.content,
                c.color,
                c.size,
                c.point_num,
                c.validate,
                c.date_add
            FROM `' . _DB_PREFIX_ . 'bon_comment` c
            LEFT JOIN `' . _DB_PREFIX_ . 'bon_comment_image` ci ON c.id_product_comment = ci.id_product_comment AND ci.deleted = 0
            LEFT JOIN `' . _DB_PREFIX_ . 'bon_comment_grade` cg ON c.id_product_comment = cg.id_product_comment
            LEFT JOIN `' . _DB_PREFIX_ . 'customer` cu ON c.id_customer = cu.id_customer
            LEFT JOIN `' . _DB_PREFIX_ . 'product` p ON c.id_product = p.id_product
            LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl 
                ON c.id_product = pl.id_product 
                AND pl.id_lang = ' . (int) $this->getContext()->language->id . '
            LEFT JOIN `' . _DB_PREFIX_ . 'shop_url` su ON p.id_shop_default = su.id_shop
            WHERE c.deleted = 0 AND c.validate = 1 
            GROUP BY c.id_product_comment
        ';
        $comments = Db::getInstance()->executeS($sql);
        if (empty($comments)) {
            throw new Exception('No comments available for export.');
        }
        // 根据格式生成文件
        if ($format === 'csv') {
            $this->generateCSV($comments, $filename);
        } elseif ($format === 'xml') {
            $this->generateXML($comments, $filename);
        }

        return $filename;
    }

    private function generateCSV($data, $filename)
    {
        $fp = fopen($filename, 'w');

        // 写入表头
        fputcsv($fp, [
            'Comment ID', 'Name', 'Review Timestamp', 'Title', 
            'Content', 'Review URL', 'Image URL', 'Overall',
            'Min', 'Max', 'MPN', 'SKU', 'Brand', 'Product Name', 'Product URL'
        ]);

        $site_name = \Configuration::get('PS_SHOP_NAME');
        $base_url = Context::getContext()->shop->getBaseURL();
        $image_path = $base_url. 'modules/boncomments/img/';
        // 写入数据
        foreach ($data as $row) {
            $product = new Product($row['id_product']); // 根据产品 ID 获取产品对象
            $product_url = $product->getLink(); // 获取产品的 URL
            
            fputcsv($fp, [
                $row['id_product_comment'], 
                htmlspecialchars($row['customer_firstname'] . ' ' . $row['customer_lastname']),
                $row['date_add'], 
                htmlspecialchars((string)$row['title']),
                htmlspecialchars((string)$row['content']),
                htmlspecialchars((string)$product_url),
                $row['image_path'] ? $image_path . htmlspecialchars((string)$row['image_path']) : '',
                $row['grade'],
                '1', // Min
                '5', // Max
                htmlspecialchars('mpn'.$row['sku']),
                htmlspecialchars((string)$row['sku']),
                htmlspecialchars($site_name),
                htmlspecialchars((string)$row['product_name']),
                htmlspecialchars((string)$product_url)
            ]);
        }

        fclose($fp);
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . basename($filename) . '"');

        readfile($filename);
        //清除生成的文件
        unlink($filename);
        exit;
    }

    private function generateXML($data, $filename)
    {
        $xml = new SimpleXMLElement('<reviews/>');
        $site_name = \Configuration::get('PS_SHOP_NAME');
        $base_url = Context::getContext()->shop->getBaseURL();
        $favicon_url = $base_url  .'img/'. \Configuration::get('PS_LOGO');
        $image_path = $base_url. 'modules/boncomments/img/';
        // dd($data);
        $xml = new SimpleXMLElement('<feed xsi:noNamespaceSchemaLocation="http://www.google.com/shopping/reviews/schema/product/2.3/product_reviews.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:vc="http://www.w3.org/2007/XMLSchema-versioning"/>');

        $publisher = $xml->addChild('publisher');
        $publisher->addChild('version',  '2.3');
        $publisher->addChild('name', htmlspecialchars($site_name));
        $publisher->addChild('favicon', htmlspecialchars($favicon_url));

        $reviews = $xml->addChild('reviews');
        foreach ($data as $row) {
            $product = new Product($row['id_product']); // 根据产品 ID 获取产品对象
            $product_url = $product->getLink(); // 获取产品的 URL
            $review = $reviews->addChild('review');
            // $review->addChild('version',  '2.3');
            // $review->addChild('name',  htmlspecialchars($site_name));
            // $review->addChild('favicon',  htmlspecialchars($favicon_url));
            $review->addChild('review_id',  (string)$row['id_product_comment']);
            $name = $review->addChild('reviewer');
            $name->addChild('name',  htmlspecialchars($row['customer_firstname'] . ' ' . $row['customer_lastname']), 'is_anonymous="true"');
            // $review->addChild('name',  htmlspecialchars($row['customer_firstname'] . ' ' . $row['customer_lastname']));
            $review->addChild('review_timestamp',  (string)$row['date_add']);
            $review->addChild('title',  htmlspecialchars((string)$row['title']));
            $review->addChild('content',  htmlspecialchars((string)$row['content']));
            $review->addChild('review_url',  htmlspecialchars((string)$product_url), 'type="group"');

            $image = $review->addChild('reviewer_images');
            $re_image = $image->addChild('reviewer_image');
            if($row['image_path']){
                
                $re_image->addChild('url',  $image_path .htmlspecialchars((string)$row['image_path']));
            }else{
                $re_image->addChild('url',  '');
            }
            $rating = $review->addChild('ratings');
            $rating->addChild('overall',  (string)$row['grade'], 'min="1" max="5"');
            // $review->addChild('min',  '1');
            // $review->addChild('max',  '5');
            $product = $review->addChild('products');
            $products = $product->addChild('product');
            $pro_ids = $products->addChild('product_ids');

            $mpns = $pro_ids->addChild('mpns');
            $mpns->addChild('mpn',  htmlspecialchars('mpn'.$row['sku']));

            $skus = $pro_ids->addChild('skus');
            $skus->addChild('sku',  htmlspecialchars((string)$row['sku']));

            $brand = $products->addChild('brands');
            $brand->addChild('brand',  htmlspecialchars($site_name));

            $products->addChild('product_name',  htmlspecialchars((string)$row['product_name']));
            $products->addChild('product_url',  htmlspecialchars((string)$product_url));
            // $review->addChild('product_id',  (string)$row['id_product']);
            // $review->addChild('customer_id',  (string)$row['id_customer']);
            // $review->addChild('color',  htmlspecialchars((string)$row['color']));
            // $review->addChild('size',  htmlspecialchars((string)$row['size']));            
        }
        // print_r($xml->asXML());exit;

        // $xml->asXML($filename);
        header('Content-Type: application/xml');
        header('Content-Disposition: attachment; filename="reviewFeed.xml"');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // 输出XML内容
        echo $xml->asXML();
        exit;
    }

    public function exportImageNameLabelNewAction()
    {
        set_time_limit(0);
        ini_set('display_errors', 'on');
        ini_set('memory_limit', -1);
        //获取变体信息color
        $id_lang = Context::getContext()->language->id;

        $colors = Db::getInstance()->executeS('
            SELECT 
                a.id_attribute, 
                al.name
            FROM 
                '._DB_PREFIX_.'attribute a
            INNER JOIN 
                '._DB_PREFIX_.'attribute_lang al 
                ON a.id_attribute = al.id_attribute
            INNER JOIN 
                '._DB_PREFIX_.'attribute_group ag 
                ON a.id_attribute_group = ag.id_attribute_group
            INNER JOIN 
                '._DB_PREFIX_.'attribute_group_lang agl 
                ON ag.id_attribute_group = agl.id_attribute_group
            WHERE 
                agl.name = "Color" 
                AND al.id_lang = '.(int)$id_lang
        );
        $attributeColors = array();
        foreach ($colors as $color) {   // 颜色
            $attributeColors[$color['id_attribute']]=$color['name'];
        }
        //获取产品属性-图片
        $colorsStr = implode("\",\"", array_keys($attributeColors));
        //获取变体对应图片
        $productAttributeImageRaw = Db::getInstance()->executeS('
            SELECT 
                id_product_attribute, id_image
            FROM '._DB_PREFIX_.'product_attribute_image'
        );
        $productAttributeImage = [];
        foreach ($productAttributeImageRaw as $row) {   // 图片
            $productAttributeImage[$row['id_product_attribute']] = $row['id_image'];
        }
        //获取图片信息
        $imagesRaw = Db::getInstance()->executeS('
            SELECT 
                id_image, type
            FROM '._DB_PREFIX_.'image
        ');
        $Images = [];
        foreach ($imagesRaw as $row) {
            $type = $row['type'];
            $numberStr = (string)$row['id_image'];
            $splitNumber = implode('/', str_split($numberStr));
            $Images[$row['id_image']] = '/p/' . $splitNumber . '/' . $row['id_image'] . '.' . $type;
        }
        //获取是否是正常产品shop
        $shop_ids = Db::getInstance()->executeS('
            SELECT 
                id_shop,id_product
            FROM '._DB_PREFIX_.'product_shop
            WHERE active = 1
        ');
        $shopIdsArr = [];
        foreach ($shop_ids as $row) {
            $shopIdsArr[$row['id_product']] = $row['id_shop'];
        }

        //获取产品对应变体
        $product_attributes = Db::getInstance()->executeS('
            SELECT 
                p.id_product,p.supplier_reference AS SKU, pa.id_product_attribute,pac.id_attribute
            FROM '._DB_PREFIX_.'product p
            LEFT JOIN '._DB_PREFIX_.'product_attribute pa 
                ON p.id_product = pa.id_product
            LEFT JOIN '._DB_PREFIX_.'product_attribute_combination pac 
                ON pa.id_product_attribute = pac.id_product_attribute
            WHERE p.active = 1 
                AND pac.id_attribute IN ("'.$colorsStr.'") 
        ');
        // AND p.supplier_reference = "GLHT0068" limit 100000
        $colorLabel = [];
        foreach ($product_attributes as $product_attribute) {
            // $data = [];
            // if(isset($colorLabel[$product_attribute['SKU']]) && isset($productAttributeImage[$product_attribute['id_product_attribute']]) && isset($Images[$productAttributeImage[$product_attribute['id_product_attribute']]])){
            //     $colorLabel[$product_attribute['SKU']]['label:image'] .= $Images[$productAttributeImage[$product_attribute['id_product_attribute']]].":".$attributeColors[$product_attribute['id_attribute']].";";
            // }else{
            //     $data['SKU'] = $product_attribute['SKU'];
            //     if(isset($productAttributeImage[$product_attribute['id_product_attribute']]) && isset($Images[$productAttributeImage[$product_attribute['id_product_attribute']]])){  //有图片
            //         $image = $Images[$productAttributeImage[$product_attribute['id_product_attribute']]];
            //         $color = $attributeColors[$product_attribute['id_attribute']];
            //         if(isset($shopIdsArr[$product_attribute['id_product']])){
            //             $data['status'] = '1';
            //         }else{
            //             $data['status'] = '2';
            //         }
            //         $data['label:image'] = $image.":".$color.";";
            //         $colorLabel[$product_attribute['SKU']] = $data;
            //     }
            // }
            $data = [];
            $sku = $product_attribute['SKU'];
            $id_product_attribute = $product_attribute['id_product_attribute'];
            $id_attribute = $product_attribute['id_attribute'];

            if (!isset($colorLabel[$sku])) {
                $colorLabel[$sku] = [
                    'SKU' => $sku,
                    'label:image' => '',
                    'status' => isset($shopIdsArr[$product_attribute['id_product']]) ? '1' : '2',
                    'images_seen' => [] 
                ];
            }
            if (isset($productAttributeImage[$id_product_attribute]) && isset($Images[$productAttributeImage[$id_product_attribute]])) {
                $image = $Images[$productAttributeImage[$id_product_attribute]];
                $color = $attributeColors[$id_attribute];
                $uniqueKey = $image . ":" . $color;

                // 确保唯一性，防止重复
                if (!in_array($uniqueKey, $colorLabel[$sku]['images_seen'], true)) {
                    $colorLabel[$sku]['images_seen'][] = $uniqueKey;
                }
            }
        }
        $format = Tools::getValue('export_format', 'xml');
        $filename = _PS_ROOT_DIR_ . '/upload/export/ImageNameLabelNew.' . $format;
        if (!is_dir(_PS_ROOT_DIR_ . '/upload/export')) {
            mkdir(_PS_ROOT_DIR_ . '/upload/export', 0755, true);
        }
        if($format == 'csv'){
            $this->ImageNameLabelNewCSV($colorLabel, $filename);
        }else{
            $this->ImageNameLabelNewXML($colorLabel, $filename);
        }
        return $this->redirectToRoute('admin_exports_index');
    }

    private function ImageNameLabelNewCSV($data, $filename)
    {
        if (ob_get_length()) {
            ob_end_clean();
        }
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
        $fp = fopen($filename, 'w');
        // 写入表头
        fputcsv($fp, [
            'sku', 'label:image', 'status:1=enabled:2=disabled'
        ]);

        // 写入数据
        foreach ($data as $row) {
            if (!empty($row['images_seen'])) { 
                $row['label:image'] = implode(';', $row['images_seen']) . ';';
                fputcsv($fp, [
                    $row['SKU'],
                    strip_tags($row['label:image']),
                    $row['status']
                ]);
            }
        }
        fclose($fp);
        readfile($filename);
        //清除生成的文件
        unlink($filename);
        exit;
    }

    private function ImageNameLabelNewXML($data, $filename)
    {
        $xml = new SimpleXMLElement('<reviews/>');
        $site_name = \Configuration::get('PS_SHOP_NAME');
        $base_url = Context::getContext()->shop->getBaseURL();
        $favicon_url = $base_url  .'img/'. \Configuration::get('PS_LOGO');
        $image_path = $base_url. 'modules/boncomments/img/';
        // dd($data);
        $xml = new SimpleXMLElement('<feed xsi:noNamespaceSchemaLocation="http://www.google.com/shopping/reviews/schema/product/2.3/product_reviews.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:vc="http://www.w3.org/2007/XMLSchema-versioning"/>');

        $publisher = $xml->addChild('publisher');
        $publisher->addChild('version',  '2.3');
        $publisher->addChild('name', htmlspecialchars($site_name));
        $publisher->addChild('favicon', htmlspecialchars($favicon_url));

        foreach ($data as $row) {
            $review = $xml->addChild('review');
            $review->addChild('product_id',  (string)$row['id_product']);

            $product_image->addChild('image_label',  htmlspecialchars($row['label:image']));

            $review->addChild('comment_id',  (string)$row['id_product_comment']);
            $review->addChild('name',  htmlspecialchars($row['customer_firstname'] . ' ' . $row['customer_lastname']));
            $review->addChild('review_timestamp',  (string)$row['date_add']);
       
        }

        // $xml->asXML($filename);
        header('Content-Type: application/xml');
        header('Content-Disposition: attachment; filename="reviewFeed.xml"');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // 输出XML内容
        echo $xml->asXML();
        exit;
    }

    public function exportImageNameNewAction()
    {
        set_time_limit(0);
        ini_set('display_errors', 'on');
        ini_set('memory_limit', -1);

        $image_collection = Db::getInstance()->executeS('
            SELECT 
                p.supplier_reference AS sku, 
                i.name AS image_name,
                i.type
            FROM 
                '._DB_PREFIX_.'product p
            INNER JOIN 
                '._DB_PREFIX_.'image i 
                ON p.id_product = i.id_product
            WHERE 
                p.active = 1'
        );
     
        $imageName = array_reduce($image_collection, function($carry, $item) {
            $sku = $item['sku'];
            if (!isset($carry[$sku])) {
                $carry[$sku] = array(
                    'sku' => $sku,
                    'image_name' =>$item['image_name'].'.jpg', // 默认使用jpg格式
                );
            } else {
                $carry[$sku]['image_name'] .= ',' . $item['image_name'].'.jpg';
            }
            return $carry;
        }, array());
        // print_r($imageName);exit;
        $format = Tools::getValue('export_format', 'xml');
        $filename = _PS_ROOT_DIR_ . '/upload/export/ImageNameNew.' . $format;
        if (!is_dir(_PS_ROOT_DIR_ . '/upload/export')) {
            mkdir(_PS_ROOT_DIR_ . '/upload/export', 0755, true);
        }
        if($format == 'csv'){
            $this->ImageNameNewCSV($imageName, $filename);
        }else{
            $this->ImageNameNewXML($imageName, $filename);
        }
        return $this->redirectToRoute('admin_exports_index');
    }

    private function ImageNameNewCSV($data, $filename)
    {
        if (ob_get_length()) {
            ob_end_clean();
        }
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . basename($filename) . '"');
        $fp = fopen($filename, 'w');
        // 写入表头
        fputcsv($fp, [
            'sku', 'image_name'
        ]);
        // 写入数据
        foreach ($data as $row) {
            if (!empty($row['image_name'])) { 
                fputcsv($fp, [
                    $row['sku'],
                    strip_tags($row['image_name']),
                ]);
            }
        }
        fclose($fp);
        readfile($filename);
        //清除生成的文件
        unlink($filename);
        exit;
    }

    private function ImageNameNewXML($data, $filename)
    {
        $xml = new SimpleXMLElement('<reviews/>');
        $site_name = \Configuration::get('PS_SHOP_NAME');
        $base_url = Context::getContext()->shop->getBaseURL();
        $favicon_url = $base_url  .'img/'. \Configuration::get('PS_LOGO');
        $image_path = $base_url. 'modules/boncomments/img/';
        // dd($data);
        $xml = new SimpleXMLElement('<feed xsi:noNamespaceSchemaLocation="http://www.google.com/shopping/reviews/schema/product/2.3/product_reviews.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:vc="http://www.w3.org/2007/XMLSchema-versioning"/>');

        $publisher = $xml->addChild('publisher');
        $publisher->addChild('version',  '2.3');
        $publisher->addChild('name', htmlspecialchars($site_name));
        $publisher->addChild('favicon', htmlspecialchars($favicon_url));

        foreach ($data as $row) {
            $review = $xml->addChild('review');
            $review->addChild('sku',  (string)$row['sku']);

            $product_image->addChild('image_name',  htmlspecialchars($row['image_name']));

        }

        // $xml->asXML($filename);
        header('Content-Type: application/xml');
        header('Content-Disposition: attachment; filename="reviewFeed.xml"');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');

        // 输出XML内容
        echo $xml->asXML();
        exit;
    }
}