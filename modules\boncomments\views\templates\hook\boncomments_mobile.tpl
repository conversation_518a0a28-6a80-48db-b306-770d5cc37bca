{*
* 2007-2021 PrestaShop
*
* NOTICE OF LICENSE 评论展示
*
* This source file is subject to the Academic Free License (AFL 3.0)
* that is bundled with this package in the file LICENSE.txt.
* It is also available through the world-wide-web at this URL:
* http://opensource.org/licenses/afl-3.0.php
* If you did not receive a copy of the license and are unable to
* obtain it through the world-wide-web, please send an email
* to <EMAIL> so we can send you a copy immediately.
*
* DISCLAIMER
*
* Do not edit or add to this file if you wish to upgrade PrestaShop to newer
* versions in the future. If you wish to customize PrestaShop for your
* needs please refer to http://www.prestashop.com for more information.
*
* <AUTHOR> SA <<EMAIL>>
    * @copyright 2007-2021 PrestaShop SA
    * @license http://opensource.org/licenses/afl-3.0.php Academic Free License (AFL 3.0)
    * International Registered Trademark & Property of PrestaShop SA
    *
    *}

<script type="text/javascript">
    var productcomments_controller_url = '{$productcomments_controller_url nofilter}';
    var confirm_report_message = '{l s='Are you sure that you want to report this comment ? ' mod='boncomments' js=1}';
    var secure_key = '{$secure_key}';
    var productcomments_url_rewrite = '{$productcomments_url_rewriting_activated}';
    var productcomment_added = '{l s='Your comment has been added!' mod='boncomments' js=1}';
    var productcomment_added_moderation = '{l s='Your comment has been submitted and will be available once approved by a moderator.' mod='boncomments' js=1}';
    var productcomment_title = '{l s='New comment ' mod='boncomments' js=1}';
    var productcomment_ok = '{l s='OK ' mod='boncomments' js=1}';
    var moderation_active = '{ $moderation_active }';
</script>

<div id="productCommentsBlock">
    <div class="tabs" style="margin-top: 0;padding-top:0;">
        <div id="new_comment_form_ok" class="alert" style="display:flex;padding:5px 25px;margin:0;">{*  *}
            <div class="average-left">
                <div class="ratings">
                    <div class="rating-box" style="border-right:1px solid #ccc;margin-right: 16px;padding-right: 16px;">
                        <div class="rating2">
                            <div class="rating1" style="width: {($productAverageTotal / 5)*100}%;"></div>
                        </div>
                        <span class="rating-number rating-numberMobile">{$productAverageTotal}</span>
                    </div>
                    <span class="amount"
                        style="font-size: 14px;font-weight:400;margin-bottom: 5px;">{l s='%s Grand Total' sprintf=[$nbComments] mod='boncomments'}</span>
                </div>
                <div style="clear:both"></div>
            </div>
            <div class="review-filter">
                <div class="filter_newest btn-review active" title="newest">
                    <p>{l s="Avis les plus récents..." mod='boncomments'}</p>
                </div>
                <div class="filter_photo btn-review" title="photo">
                    <p>{l s="Photos" mod='boncomments'}</p>
                </div>
                <div class="filter_Rating btn-review" title="rating">
                    <p>{l s="Notation" mod='boncomments'}</p>
                </div>
            </div>
        </div>
        {* 评论 *}
        <div id="product_comments_block_tab" class="product_comments_block_tabPhone">
            {if !$comments}
                <span class="no-reviews">{l s='Ce produit n\'a pas de commentaires!' mod='boncomments'}</span>
            {else}
                {foreach from=$comments key=key item=item}
                    <div class="{$key}" style="display:none">{* 三个分类，全部 有图 最新 *}
                        {foreach from=$item.comments item=comment}
                            {if $comment.content}
                                <div class="comment clearfix jscommentiterm" data-name="{$comment.customer_name}"
                                    data-url="{$comment.product_url}"
                                    data-text="{$comment.content|escape:'html':'UTF-8'|nl2br nofilter}"
                                    data-img="{$comment.product_image}">
                                    <div class="comment_author">
                                        <div class="commentItem" style="position: relative;">
                                            {* 星级 评论人 属国*}
                                            <div class="customer_photo">{* 头像 *}
                                            </div>
                                            <span class="icon-savedui">
                                                <svg t="1693548825988" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                                    xmlns="http://www.w3.org/2000/svg" p-id="4829" width="16" height="16">
                                                    <path
                                                        d="M882.432387 204.696792c18.921552-21.157736 51.260205-22.705863 72.245926-3.440283 20.813707 19.26558 22.361834 52.120275 3.440283 73.278011L435.711742 856.458256a50.656435 50.656435 0 0 1-74.48211 1.204099l-295.348228-307.905258c-19.609609-20.469679-19.26558-53.324374 1.032085-73.278011 20.297665-19.953637 52.636318-19.437594 72.245926 1.032085l257.505124 268.514026L882.432387 204.696792z m0 0"
                                                        p-id="4830"></path>
                                                </svg>
                                            </span>
                                            <div class="detailInfo">
                                                <strong style="font-size: 15px;"
                                                    class="customerName">{$comment.customer_name|escape:'html':'UTF-8'}</strong>
                                                <label style="font-size: 13px;text-align: left;margin-bottom: 0px;">{* 认证 *}
                                                    {l s="Vérifié" mod='boncomments'}
                                                    {* 国家 *}
                                                    {if $comment.country_iso}
                                                        <svg t="1693538422217" class="icon icon-city" viewBox="0 0 1024 1024" version="1.1"
                                                            xmlns="http://www.w3.org/2000/svg" p-id="4094" width="16" height="16">
                                                            <path
                                                                d="M115.264 422.656C112.576 241.984 223.488 80.64 388.352 25.216c185.024-62.336 367.616-5.568 482.24 149.504 104.128 141.056 105.728 348.032-0.896 489.408-78.528 103.872-161.28 204.608-244.352 304.64-62.72 75.264-130.688 72.64-192.64-4.352-76.096-94.656-153.152-188.48-226.816-284.992C148.672 604.352 112.64 519.808 115.264 422.656zM168.448 422.592C161.28 492.864 189.184 563.776 233.92 624.384c79.552 108.224 165.568 211.904 250.112 316.48 33.472 41.6 69.568 32.512 99.84-4.608 81.728-101.248 166.976-200 241.984-305.728 99.52-140.608 92.48-307.776-7.872-437.12-94.848-122.048-257.152-170.624-403.392-120.64C266.432 123.456 168.448 259.072 168.448 422.592z"
                                                                fill="#272636" p-id="4095"></path>
                                                            <path
                                                                d="M351.552 416.96c0-101.824 73.792-178.688 172.032-179.2 108.032-0.512 190.144 75.648 190.784 176.96 0.64 102.912-83.136 186.688-184.96 185.344C427.584 598.4 351.744 520.384 351.552 416.96zM532.352 290.688C461.696 289.856 407.296 342.72 405.376 413.888c-1.92 73.344 53.696 131.328 126.592 131.84 69.504 0.384 126.144-54.912 127.424-124.672C660.864 346.624 607.104 291.52 532.352 290.688z"
                                                                fill="#272636" p-id="4096"></path>
                                                        </svg>
                                                        {$comment.country_iso}
                                                    {/if}
                                                </label>
                                            </div>
                                        </div>

                                        {* 属性 *}
                                        {if $comment.attributes.color || $comment.attributes.size}
                                            <div class="colerSize" style="display: flex;justify-content: space-between;">
                                                {if $comment.attributes.color}
                                                    <span style="margin-top: 0;">
                                                        <label
                                                            style="text-transform: capitalize;font-size: 13px;margin-right: 5px;">Color:</label>
                                                        <label style="margin-right: 10px;font-size: 13px;">{$comment.attributes.color}</label>
                                                    </span>
                                                {/if}
                                                {if $comment.attributes.size}
                                                    <span style="margin-top: 0;">
                                                        <label
                                                            style="text-transform: capitalize;font-size: 13px;margin-right: 5px;">Size:</label>
                                                        <label style="margin-right: 10px;font-size: 13px;">{$comment.attributes.size}</label>
                                                    </span>
                                                {/if}
                                            </div>
                                        {/if}

                                        <div style="display: flex;margin-top: 10px;margin-bottom: 5px;">
                                            {* 星级 *}
                                            <div class="star_content clearfix">
                                                {section name="i" start=0 loop=5 step=1}
                                                    {if $comment.grade le $smarty.section.i.index}
                                                        <div class="xinxingNoLight"></div>
                                                    {else}
                                                        <div class="xinxingLight"></div>
                                                    {/if}
                                                {/section}
                                            </div>

                                            {* 是否合适 *}
                                            {if $comment.is_fit !== ''}
                                                <div class="fit isSuitable">
                                                    <span class="text"><span
                                                            style="margin-right: 5px;">{l s="Ajuster" mod='boncomments'}:</span>{if $comment.is_fit}{l s="Oui" mod='boncomments'}{else}{l s="Non" mod='boncomments'}{/if}</span>
                                                </div>
                                            {/if}
                                        </div>

                                    </div>

                                    <div class="comment_biaoti">
                                        <label style="font-weight: 600;font-size: 15px;">{$comment.title}</label>
                                    </div>

                                    <div id="comment_details-{$comment.id_product_comment}" class="comment_details">{* style="background: tan;width: 100%;" 评论内容 *}
                                        {* <h4 class="title_block"></h4> *}
                                        <p style="font-size: 13px;line-height: 16px;margin-bottom: 10px;">
                                            {$comment.content|escape:'html':'UTF-8'|nl2br nofilter}</p>
                                        {if _PS_COUNTRY_FANYI_}
                                            <div class="translate">
                                                <a href="javascript:void(0);" onclick="reviewln({$comment.id_product_comment});"
                                                    style="color: #FF7176;">
                                                    <svg t="1683602829356" class="translate-icon" viewBox="0 0 1024 1024" version="1.1"
                                                        xmlns="http://www.w3.org/2000/svg" p-id="3967" width="12" height="12">
                                                        <path
                                                            d="M99.119848 428.002088h828.014506a43.350032 43.350032 0 0 0 43.350032-43.350031 43.248882 43.248882 0 0 0-16.451337-33.957525 43.465632 43.465632 0 0 0-9.219107-8.756707L635.50424 124.869766a43.350032 43.350032 0 1 0-49.809186 70.964002l207.270952 145.461032H99.119848a43.350032 43.350032 0 0 0 0 86.707288zM927.134354 601.07709H99.119848a43.350032 43.350032 0 0 0-43.350032 43.350032 43.205532 43.205532 0 0 0 16.451337 33.9503c2.564877 3.272927 5.635504 6.242405 9.219107 8.756706l309.309701 217.075284a43.313907 43.313907 0 0 0 60.386594-10.584632 43.350032 43.350032 0 0 0-10.577407-60.386595L233.288196 687.777154h693.846158a43.350032 43.350032 0 0 0 0-86.700064z"
                                                            fill="#FF7176" p-id="3968">
                                                        </path>
                                                    </svg>
                                                    {l s='Traduire' mod='boncomments'}
                                                </a>
                                            </div>
                                            <div class="translate-box">
                                                <div class="translate-close" onclick="closereviewln({$comment.id_product_comment});"></div>
                                                <div class="translate-content reviewdata-{$comment.id_product_comment}"></div>
                                                <div class="translate-footer">
                                                    <span class="comment-target-language-name">{l s='néerlandais' mod='boncomments'}</span>
                                                    <div class="translate-by">
                                                        <span class="translate-by-google">{l s='Traduit par' mod='boncomments'}</span>
                                                        <img class="translate-google-img"
                                                            src="/themes/ztheme/assets/images/translate_google.svg">
                                                    </div>
                                                </div>
                                            </div>
                                        {/if}
                                        {*  评论图片 *}
                                        {if $comment.images}
                                            <div class="comment_img" style="margin: 10px 0;">
                                                {foreach from=$comment.images item=image}
                                                    <img class="comment-modal-img" data-bgsrc="{$image.image_path}"
                                                        src="{$image.image_thumb_path}" alt=""
                                                        style="width: 72px;height: 96px;margin-right: 5px;">
                                                {/foreach}
                                            </div>
                                        {/if}
                                        <div class="comment_author_infos" style="line-height: 1;">
                                            <span class="text"
                                                style="font-size: 13px;font-weight: 700;">{l s="Date d'achat :" mod='boncomments'}</span>
                                            <em>{$comment.date_add|date_format}</em>
                                        </div>
                                        <ul>{* 底部图标 *}
                                            {if !$logged}
                                                <div class="review_sharelinks">
                                                    <div class="review_useful">
                                                        <a class="icon_useful" id="useful_link" style="font-weight: normal;">
                                                            <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                                                xmlns="http://www.w3.org/2000/svg" width="15px" height="15px">
                                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                                    d="M7.94.94A1.5 1.5 0 0 1 10.5 2a20.774 20.774 0 0 1-.384 4H14.5A1.5 1.5 0 0 1 16 7.5v.066l-1.845 6.9-.094.095A1.5 1.5 0 0 1 13 15H9c-.32 0-.685-.078-1.038-.174-.357-.097-.743-.226-1.112-.349l-.008-.003c-.378-.126-.74-.246-1.067-.335C5.44 14.047 5.18 14 5 14v.941l-5 .625V6h5v.788c.913-.4 1.524-1.357 1.926-2.418A10.169 10.169 0 0 0 7.5 1.973 1.5 1.5 0 0 1 7.94.939ZM8 2l.498.045v.006l-.002.013a4.507 4.507 0 0 1-.026.217 11.166 11.166 0 0 1-.609 2.443C7.396 5.951 6.541 7.404 5 7.851V13c.32 0 .685.078 1.038.174.357.097.743.226 1.112.349l.008.003c.378.126.74.246 1.067.335.335.092.594.139.775.139h4a.5.5 0 0 0 .265-.076l1.732-6.479A.5.5 0 0 0 14.5 7H8.874l.138-.61c.326-1.44.49-2.913.488-4.39a.5.5 0 0 0-1 0v.023l-.002.022L8 2ZM4 7H1v7.434l3-.375V7Zm-1.5 5.75a.25.25 0 1 0 0-.5.25.25 0 0 0 0 .5Zm-.75-.25a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0Z">
                                                                </path>
                                                            </svg>
                                                            <span style="font-weight: normal;">Utile</span>
                                                        </a>
                                                    </div>
                                                    <div class="review_share">
                                                        <a href="https://www.facebook.com/sharer/sharer.php?u={$product.url}"
                                                            style="font-weight: normal;">
                                                            <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                                                xmlns="http://www.w3.org/2000/svg" width="14px" height="14px">
                                                                <path fill-rule="evenodd" clip-rule="evenodd"
                                                                    d="M13 1a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm-3 2a3 3 0 1 1 .583 1.778L5.867 7.115a3 3 0 0 1 0 1.77l4.716 2.337a3 3 0 1 1-.45.893L5.417 9.778a3 3 0 1 1 0-3.556l4.716-2.337A3.002 3.002 0 0 1 10 3ZM1 8a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm10 5a2 2 0 1 1 4 0 2 2 0 0 1-4 0Z">
                                                                </path>
                                                            </svg>
                                                            <span style="font-weight: normal;">Partager</span>
                                                        </a>
                                                    </div>
                                                </div>
                                                {* 为了发表评论请先登录 *}
                                                <div class="modal fade" id="modal-logged" tabindex="-1" role="dialog"
                                                    aria-labelledby="modal-logged" aria-hidden="true">
                                                    <div class="modal-dialog" role="document">
                                                        <div class="modal-content">
                                                            <div class="modal-body">
                                                                <span>{l s='Pour écrire un commentaire s\'il vous plaît connectez-vous.' mod='boncomments'}</span>
                                                            </div>
                                                            <div class="modal-footer">
                                                                <button type="button" class="popup-close" data-dismiss="modal"></button>
                                                                <button type="button" class="btn btn-primary">
                                                                    <a href="{$link->getPageLink('my-account', true)}">
                                                                        {l s='Connexion' mod='boncomments'}
                                                                    </a>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            {/if}

                                            {if $logged}
                                                {if !$comment.customer_advice}{* 问题变量 *}
                                                    <div class="review_sharelinks">
                                                        <div class="review_useful">
                                                            <a class="icon_useful on">
                                                                <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                                                    xmlns="http://www.w3.org/2000/svg" width="15px" height="15px">
                                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                                        d="M7.94.94A1.5 1.5 0 0 1 10.5 2a20.774 20.774 0 0 1-.384 4H14.5A1.5 1.5 0 0 1 16 7.5v.066l-1.845 6.9-.094.095A1.5 1.5 0 0 1 13 15H9c-.32 0-.685-.078-1.038-.174-.357-.097-.743-.226-1.112-.349l-.008-.003c-.378-.126-.74-.246-1.067-.335C5.44 14.047 5.18 14 5 14v.941l-5 .625V6h5v.788c.913-.4 1.524-1.357 1.926-2.418A10.169 10.169 0 0 0 7.5 1.973 1.5 1.5 0 0 1 7.94.939ZM8 2l.498.045v.006l-.002.013a4.507 4.507 0 0 1-.026.217 11.166 11.166 0 0 1-.609 2.443C7.396 5.951 6.541 7.404 5 7.851V13c.32 0 .685.078 1.038.174.357.097.743.226 1.112.349l.008.003c.378.126.74.246 1.067.335.335.092.594.139.775.139h4a.5.5 0 0 0 .265-.076l1.732-6.479A.5.5 0 0 0 14.5 7H8.874l.138-.61c.326-1.44.49-2.913.488-4.39a.5.5 0 0 0-1 0v.023l-.002.022L8 2ZM4 7H1v7.434l3-.375V7Zm-1.5 5.75a.25.25 0 1 0 0-.5.25.25 0 0 0 0 .5Zm-.75-.25a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0Z">
                                                                    </path>
                                                                </svg>
                                                                <span>Utile</span>
                                                            </a>
                                                        </div>
                                                        <div class="review_share">
                                                            <a href="https://www.facebook.com/sharer/sharer.php?u={$product.url}">
                                                                <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                                                    xmlns="http://www.w3.org/2000/svg" width="14px" height="14px">
                                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                                        d="M13 1a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm-3 2a3 3 0 1 1 .583 1.778L5.867 7.115a3 3 0 0 1 0 1.77l4.716 2.337a3 3 0 1 1-.45.893L5.417 9.778a3 3 0 1 1 0-3.556l4.716-2.337A3.002 3.002 0 0 1 10 3ZM1 8a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm10 5a2 2 0 1 1 4 0 2 2 0 0 1-4 0Z">
                                                                    </path>
                                                                </svg>
                                                                <span>Partager</span>
                                                            </a>
                                                        </div>
                                                    </div>
                                                {/if}
                                            {/if}
                                        </ul>
                                        {hook::exec('displayProductComment', $comment) nofilter}
                                    </div>
                                    <br />
                                </div>
                            {/if}
                        {/foreach}
                        {if count($item) > 1}
                            <div class="review-list-moreMobile on" key={$key} id="jsAllbtn"
                                data-immersive-translate-walked="fb373d8b-416e-4def-a318-9aa1aacc8519"
                                data-immersive-translate-paragraph="1" onclick="allBtnClickPhone(this)">
                                {l s="Voir Tout" mod='boncomments'}</div>
                        {/if}
                    </div>
                {/foreach}
            {/if}
        </div>
    </div>
</div>
{* 图片弹窗 *}
<div id="showReviewtanchuang">
    <div id="details_comment_modal" class="modal mymodal-pc-js" style="top: -50px;">
        <div class="modal-content" style="">
            <span id="close" class="dt-close">×</span>
            {*<span class="left-button-review left-button"></span>
            <span class="right-button-review right-button"></span> *}
            <div class="details-comment-img-modal" style="width: 100%;height:100%">
                <div class="phone" style="flex-direction: column;">
                    <div class="left" style="width: 100%;">
                        <div class="left-img-top dc-left-img-top">
                            {* 大图 *}
                        </div>

                        <div class="left-img-bottom dc-left-img-bottom" style="display: none;">{* 小图 *}</div>
                    </div>
                    <div class="right" style="display: flex;padding:15px;width: 100%;height: auto;">
                        <div class="tc-pr-img right-img">
                            <img class="product-comment-img" style="width:80px;height:120px" src="{* 商品图 *}" alt="">
                        </div>
                        <div style="display: flex;flex-direction: column;padding-left: 10px;">
                            <div class="tc-share">
                                <a href="" target="_blank" class="fac share-icon facebook-share-icon">Facebook</a>
                                <a href="" title="Pinterest" class="pin share-icon pinterest-share-icon"
                                    target="_blank">Pinterest</a>
                                <a href="" class="goo share-icon googleplus-share-icon" title="Google+"
                                    target="_blank">Google+</a>
                            </div>
                        </div>
                    </div>
                    <span class="tc-auther" id="writer_name"
                        style="padding-left: 15px;padding-right:15px">{* name *}</span>
                    <div class="tc-content" id="write_content"
                        style="padding-left: 15px;padding-right:15px;max-height:136px;overflow:auto">{* text *}</div>
                </div>
            </div>
        </div>
    </div>
</div>
{* 评论弹窗 *}
<div id="comment_all-for-mobile-modal"
    style="display:none;background: #fbfbf3;width:100%;height: 100vh;position: fixed;top:0;z-index:999">
    <div class="box-review-title"
        style="display: flex;height: 50px; border-bottom: 1px solid #e0e0e7; font-size: 16px; justify-content: center; align-items: center; position: relative; font-family: LoraMedium;">
        <div class="review-close" style="position: absolute;left: 4px;padding: 5px 10px;">
            <svg t="1627709534364" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg"
                p-id="2522" width="16" height="16">
                <path d="M736.256 1024 224.256 512 736.256 0 799.744 63.744 351.488 512 799.744 960.512Z" p-id="2523"
                    fill="#000000"></path>
            </svg>
        </div>Reviews{* 评论数量 *}{l s='( %s )' sprintf=[$nbComments] mod='boncomments'}
    </div>
    <div class="review-filter commentType">
        <div class="filter_newest btn-review active" title="newest">
            <p>{l s="Avis les plus récents..." mod='boncomments'}</p>
        </div>
        <div class="filter_photo btn-review" title="photo">
            <p>{l s="Photos" mod='boncomments'}</p>
        </div>
        <div class="filter_Rating btn-review" title="rating">
            <p>{l s="Notation" mod='boncomments'}</p>
        </div>
    </div>
    {foreach from=$comments key=key item=item}
        <div class="{$key} comment-class" style="display:none;overflow-y: auto;height:100%;padding-bottom:200px">
            {* 三个分类，全部 有图 最新 *}
            {foreach from=$item.comments item=comment}
                {if $comment.content}
                    <div class="comment clearfix jscommentiterm commentMore"
                        style="margin: 0 15px 10px !important; padding: 10px; border: 1px solid #ccc; border-radius: 10px;background: #fff;"
                        data-name="{$comment.customer_name}" data-url="{$comment.product_url}"
                        data-text="{$comment.content|escape:'html':'UTF-8'|nl2br nofilter}" data-img="{$comment.product_image}">
                        <div class="comment_author">
                            <div class="commentItem" style="position: relative;">
                                {* 星级 评论人 属国*}
                                <div class="customer_photo">{* 头像 *}
                                </div>
                                <span class="icon-savedui">
                                    <svg t="1693548825988" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                        xmlns="http://www.w3.org/2000/svg" p-id="4829" width="16" height="16">
                                        <path
                                            d="M882.432387 204.696792c18.921552-21.157736 51.260205-22.705863 72.245926-3.440283 20.813707 19.26558 22.361834 52.120275 3.440283 73.278011L435.711742 856.458256a50.656435 50.656435 0 0 1-74.48211 1.204099l-295.348228-307.905258c-19.609609-20.469679-19.26558-53.324374 1.032085-73.278011 20.297665-19.953637 52.636318-19.437594 72.245926 1.032085l257.505124 268.514026L882.432387 204.696792z m0 0"
                                            p-id="4830"></path>
                                    </svg>
                                </span>
                                <div class="detailInfo">
                                    <strong style="font-size: 15px;"
                                        class="commentName">{$comment.customer_name|escape:'html':'UTF-8'}</strong>
                                    <label style="font-size: 13px;text-align: left;margin-top: 2px;margin-bottom: 0px;">{* 认证 *}
                                        {l s="Vérifié" mod='boncomments'}
                                        {* 国家 *}
                                        {if $comment.country_iso}
                                            <svg t="1693538422217" class="icon icon-city" viewBox="0 0 1024 1024" version="1.1"
                                                xmlns="http://www.w3.org/2000/svg" p-id="4094" width="16" height="16">
                                                <path
                                                    d="M115.264 422.656C112.576 241.984 223.488 80.64 388.352 25.216c185.024-62.336 367.616-5.568 482.24 149.504 104.128 141.056 105.728 348.032-0.896 489.408-78.528 103.872-161.28 204.608-244.352 304.64-62.72 75.264-130.688 72.64-192.64-4.352-76.096-94.656-153.152-188.48-226.816-284.992C148.672 604.352 112.64 519.808 115.264 422.656zM168.448 422.592C161.28 492.864 189.184 563.776 233.92 624.384c79.552 108.224 165.568 211.904 250.112 316.48 33.472 41.6 69.568 32.512 99.84-4.608 81.728-101.248 166.976-200 241.984-305.728 99.52-140.608 92.48-307.776-7.872-437.12-94.848-122.048-257.152-170.624-403.392-120.64C266.432 123.456 168.448 259.072 168.448 422.592z"
                                                    fill="#272636" p-id="4095"></path>
                                                <path
                                                    d="M351.552 416.96c0-101.824 73.792-178.688 172.032-179.2 108.032-0.512 190.144 75.648 190.784 176.96 0.64 102.912-83.136 186.688-184.96 185.344C427.584 598.4 351.744 520.384 351.552 416.96zM532.352 290.688C461.696 289.856 407.296 342.72 405.376 413.888c-1.92 73.344 53.696 131.328 126.592 131.84 69.504 0.384 126.144-54.912 127.424-124.672C660.864 346.624 607.104 291.52 532.352 290.688z"
                                                    fill="#272636" p-id="4096"></path>
                                            </svg>
                                            {$comment.country_iso}
                                        {/if}
                                    </label>
                                </div>
                            </div>

                            {* 属性 *}
                            {if $comment.attributes.color || $comment.attributes.size}
                                <div class="colerSize"
                                    style="display: flex;justify-content: space-between;background: #faf4f4;border-radius: 5px;align-items: center;line-height:1.5;border-radius: 5px;margin-top: 10px;height: 40px;padding-left: 10px;">
                                    {if $comment.attributes.color}
                                        <span style="margin-top: 0;">
                                            <label
                                                style="text-transform: capitalize;font-size: 13px;margin-right: 5px;margin-bottom: 0;">Couleur:</label>
                                            <label
                                                style="margin-right: 10px;font-size: 13px;margin-bottom: 0;">{$comment.attributes.color}</label>
                                        </span>
                                    {/if}
                                    {if $comment.attributes.size}
                                        <span style="margin-top: 0;">
                                            <label
                                                style="text-transform: capitalize;font-size: 13px;margin-right: 5px;margin-bottom: 0;">Taille:</label>
                                            <label
                                                style="margin-right: 10px;font-size: 13px;margin-bottom: 0;">{$comment.attributes.size}</label>
                                        </span>
                                    {/if}
                                </div>
                            {/if}

                            <div style="margin: 10px 0;position: relative;line-height: 1;margin-bottom: 5px;">
                                {* 星级 *}
                                <div class="star_content clearfix" style="display: inline-flex;">
                                    {section name="i" start=0 loop=5 step=1}
                                        {if $comment.grade le $smarty.section.i.index}
                                            <div class="xinxingNoLight" style="margin-right: 0;"></div>
                                        {else}
                                            <div class="xinxingLight" style="margin-right: 0;"></div>
                                        {/if}
                                    {/section}
                                </div>

                                {* 是否合适 *}
                                {if $comment.is_fit !== ''}
                                    <div class="fit" style="margin-left: 10px;display: inline-block;font-size: 13px;position: absolute;
                            top: 2px;">
                                        <span class="text"><span
                                                style="margin-right: 5px;">{l s="Ajuster" mod='boncomments'}:</span>{if $comment.is_fit}{l s="Oui" mod='boncomments'}{else}{l s="Non" mod='boncomments'}{/if}</span>
                                    </div>
                                {/if}
                            </div>


                        </div>

                        <div class="comment_biaoti">
                            <label style="font-weight: 600;font-size: 15px;line-height: 1;">{$comment.title}</label>
                        </div>

                        <div id="comment_details-{$comment.id_product_comment}" class="comment_details">{* style="background: tan;width: 100%;" 评论内容 *}
                            {* <h4 class="title_block"></h4> *}
                            <p style="font-size: 13px;margin-bottom: 10px;">{$comment.content|escape:'html':'UTF-8'|nl2br nofilter}
                            </p>
                            {if _PS_COUNTRY_FANYI_}
                                <div class="translate">
                                    <a href="javascript:void(0);" onclick="reviewln({$comment.id_product_comment});"
                                        style="color: #FF7176;">
                                        <svg t="1683602829356" class="translate-icon" viewBox="0 0 1024 1024" version="1.1"
                                            xmlns="http://www.w3.org/2000/svg" p-id="3967" width="12" height="12">
                                            <path
                                                d="M99.119848 428.002088h828.014506a43.350032 43.350032 0 0 0 43.350032-43.350031 43.248882 43.248882 0 0 0-16.451337-33.957525 43.465632 43.465632 0 0 0-9.219107-8.756707L635.50424 124.869766a43.350032 43.350032 0 1 0-49.809186 70.964002l207.270952 145.461032H99.119848a43.350032 43.350032 0 0 0 0 86.707288zM927.134354 601.07709H99.119848a43.350032 43.350032 0 0 0-43.350032 43.350032 43.205532 43.205532 0 0 0 16.451337 33.9503c2.564877 3.272927 5.635504 6.242405 9.219107 8.756706l309.309701 217.075284a43.313907 43.313907 0 0 0 60.386594-10.584632 43.350032 43.350032 0 0 0-10.577407-60.386595L233.288196 687.777154h693.846158a43.350032 43.350032 0 0 0 0-86.700064z"
                                                fill="#FF7176" p-id="3968">
                                            </path>
                                        </svg>
                                        {l s='Traduire' mod='boncomments'}
                                    </a>
                                </div>
                                <div class="translate-box">
                                    <div class="translate-close" onclick="closereviewln({$comment.id_product_comment});"></div>
                                    <div class="translate-content reviewdata-{$comment.id_product_comment}"></div>
                                    <div class="translate-footer">
                                        <span class="comment-target-language-name">{l s='néerlandais' mod='boncomments'}</span>
                                        <div class="translate-by">
                                            <span class="translate-by-google">{l s='Traduit par' mod='boncomments'}</span>
                                            <img class="translate-google-img" src="/themes/ztheme/assets/images/translate_google.svg">
                                        </div>
                                    </div>
                                </div>
                            {/if}
                            {*  评论图片 *}
                            {if $comment.images}
                                <div class="comment_img" style="margin: 10px 0;">
                                    {foreach from=$comment.images item=image}
                                        <img class="comment-modal-img" data-bgsrc="{$image.image_path}" src="{$image.image_thumb_path}"
                                            alt="" style="width: 72px;height: 96px;margin-right: 5px;">
                                    {/foreach}
                                </div>
                            {/if}
                            <div class="comment_author_infos" style="line-height: 1;">
                                <span class="text"
                                    style="font-size: 13px;font-weight: bold;">{l s="Date d'achat :" mod='boncomments'}</span>
                                <em style="font-size: 13px;font-style: unset;">{$comment.date_add|date_format}</em>
                            </div>
                            <ul>{* 底部图标 *}
                                {if !$logged}
                                    <div class="review_sharelinks">
                                        <div class="review_useful">
                                            <a class="icon_useful on">
                                                <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                                    xmlns="http://www.w3.org/2000/svg" width="15px" height="15px">
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                        d="M7.94.94A1.5 1.5 0 0 1 10.5 2a20.774 20.774 0 0 1-.384 4H14.5A1.5 1.5 0 0 1 16 7.5v.066l-1.845 6.9-.094.095A1.5 1.5 0 0 1 13 15H9c-.32 0-.685-.078-1.038-.174-.357-.097-.743-.226-1.112-.349l-.008-.003c-.378-.126-.74-.246-1.067-.335C5.44 14.047 5.18 14 5 14v.941l-5 .625V6h5v.788c.913-.4 1.524-1.357 1.926-2.418A10.169 10.169 0 0 0 7.5 1.973 1.5 1.5 0 0 1 7.94.939ZM8 2l.498.045v.006l-.002.013a4.507 4.507 0 0 1-.026.217 11.166 11.166 0 0 1-.609 2.443C7.396 5.951 6.541 7.404 5 7.851V13c.32 0 .685.078 1.038.174.357.097.743.226 1.112.349l.008.003c.378.126.74.246 1.067.335.335.092.594.139.775.139h4a.5.5 0 0 0 .265-.076l1.732-6.479A.5.5 0 0 0 14.5 7H8.874l.138-.61c.326-1.44.49-2.913.488-4.39a.5.5 0 0 0-1 0v.023l-.002.022L8 2ZM4 7H1v7.434l3-.375V7Zm-1.5 5.75a.25.25 0 1 0 0-.5.25.25 0 0 0 0 .5Zm-.75-.25a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0Z">
                                                    </path>
                                                </svg>
                                                <span>Utile</span>
                                            </a>
                                        </div>
                                        <div class="review_share">
                                            <a href="https://www.facebook.com/sharer/sharer.php?u={$product.url}">
                                                <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                                    xmlns="http://www.w3.org/2000/svg" width="14px" height="14px">
                                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                                        d="M13 1a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm-3 2a3 3 0 1 1 .583 1.778L5.867 7.115a3 3 0 0 1 0 1.77l4.716 2.337a3 3 0 1 1-.45.893L5.417 9.778a3 3 0 1 1 0-3.556l4.716-2.337A3.002 3.002 0 0 1 10 3ZM1 8a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm10 5a2 2 0 1 1 4 0 2 2 0 0 1-4 0Z">
                                                    </path>
                                                </svg>
                                                <span>Partager</span>
                                            </a>
                                        </div>
                                    </div>
                                {/if}
                                {if $logged}
                                    {if !$comment.customer_advice}{* 问题变量 *}
                                        <div class="review_sharelinks">
                                            <div class="review_useful">
                                                <a class="icon_useful on">
                                                    <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                                        xmlns="http://www.w3.org/2000/svg" width="15px" height="15px">
                                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                                            d="M7.94.94A1.5 1.5 0 0 1 10.5 2a20.774 20.774 0 0 1-.384 4H14.5A1.5 1.5 0 0 1 16 7.5v.066l-1.845 6.9-.094.095A1.5 1.5 0 0 1 13 15H9c-.32 0-.685-.078-1.038-.174-.357-.097-.743-.226-1.112-.349l-.008-.003c-.378-.126-.74-.246-1.067-.335C5.44 14.047 5.18 14 5 14v.941l-5 .625V6h5v.788c.913-.4 1.524-1.357 1.926-2.418A10.169 10.169 0 0 0 7.5 1.973 1.5 1.5 0 0 1 7.94.939ZM8 2l.498.045v.006l-.002.013a4.507 4.507 0 0 1-.026.217 11.166 11.166 0 0 1-.609 2.443C7.396 5.951 6.541 7.404 5 7.851V13c.32 0 .685.078 1.038.174.357.097.743.226 1.112.349l.008.003c.378.126.74.246 1.067.335.335.092.594.139.775.139h4a.5.5 0 0 0 .265-.076l1.732-6.479A.5.5 0 0 0 14.5 7H8.874l.138-.61c.326-1.44.49-2.913.488-4.39a.5.5 0 0 0-1 0v.023l-.002.022L8 2ZM4 7H1v7.434l3-.375V7Zm-1.5 5.75a.25.25 0 1 0 0-.5.25.25 0 0 0 0 .5Zm-.75-.25a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0Z">
                                                        </path>
                                                    </svg>
                                                    <span>Utile</span>
                                                </a>
                                            </div>
                                            <div class="review_share">
                                                <a href="https://www.facebook.com/sharer/sharer.php?u={$product.url}">
                                                    <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                                        xmlns="http://www.w3.org/2000/svg" width="14px" height="14px">
                                                        <path fill-rule="evenodd" clip-rule="evenodd"
                                                            d="M13 1a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm-3 2a3 3 0 1 1 .583 1.778L5.867 7.115a3 3 0 0 1 0 1.77l4.716 2.337a3 3 0 1 1-.45.893L5.417 9.778a3 3 0 1 1 0-3.556l4.716-2.337A3.002 3.002 0 0 1 10 3ZM1 8a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm10 5a2 2 0 1 1 4 0 2 2 0 0 1-4 0Z">
                                                        </path>
                                                    </svg>
                                                    <span>Partager</span>
                                                </a>
                                            </div>
                                        </div>
                                    {/if}
                                {/if}
                            </ul>
                            {hook::exec('displayProductComment', $comment) nofilter}
                        </div>
                    </div>
                {/if}
            {/foreach}
        </div>
    {/foreach}

</div>
<script type="text/javascript">
    document.addEventListener('DOMContentLoaded', () => {
        const commentItem = document.querySelectorAll('.commentItem')
        commentItem.forEach(item => {
            /* console.log(item);
            console.log($(item).find(".customer_photo"));
            console.log($(item).find("strong").text().substring(0, 1)); */
            //$(item).find(".customer_photo").text($(item).find(".customerName").text().substring(0, 1));
            $(item).find(".customer_photo").text($(item).find("strong").text().substring(0, 1));
        })
        /* 获取每一项的姓名第一字符 */
        const commentMoreList = document.querySelectorAll(".commentMore")
        commentMoreList.forEach(item => {
            $(item).find(".customer_photo").text($(item).find("strong").text().substring(0, 1));
            //item.children[0].children[0].children[0].innerText = item.children[0].children[0].children[1].children[0].innerText.substring(0,1)
        })
    })
    {* 设置 all打开的弹窗默认显示第一项*}
    let commentShowListModal = document.querySelector(".comment-class").style.display = 'block';
    let commentShowListP = document.querySelector(".product_comments_block_tabPhone"); /*原始页面 内涵三个分类 对应他的三个children */
    //console.log(commentShowList.children[0]);
    {* 默认展示在产品页下面的第一分类 *}
    commentShowListP.children[0].style.display = "block";
    for (let i = 0; i < commentShowListP.children.length; i++) { //初始化三个小分类页面

        for (let j = 0; j < commentShowListP.children[i].children.length - 1; j++) {
            let length = commentShowListP.children[i].children.length;
            if (j > 0) {
                commentShowListP.children[i].children[j].style.display = "none";
            }
        }
    }
    /* 关闭图片对话框 */
    $('.dt-close').on('click', function() {
        $("#details_comment_modal").css("display", "none");
    })
    {* 关闭all 评论对话框*}
    $('.box-review-title .review-close').on('click', function() {
        $("#comment_all-for-mobile-modal").css("display", "none");
        $('body').css('overflow', '')
    })
    var allBtn = document.querySelectorAll("#jsAllbtn");
    var allBtnClickPhone = function(e) {
        if (e.innerText == "Voir Tout") {
            $("#comment_all-for-mobile-modal").css("display", "block");
            $("body").css('overflow', 'hidden')
        }
    }
    {* all 页面三个评论分类之间的切换 commentTips1是tabs集合 commentModalShowList是内容集合 *}
    let commentTips1 = document.querySelector('.commentType').children;
    let commentModalShowList = document.querySelectorAll(".comment-class")
    for (let i = 0; i < 3; i++) {
        commentTips1[i].addEventListener('click', function() {
            console.log(commentTips1);
            for (let j = 0; j < commentTips1.length; j++) {
                commentTips1[j].classList.remove("active");
            }
            commentTips1[i].classList.add("active");
            commentModalShowList.forEach(function(item) {
                item.style.display = "none";
            });
            commentModalShowList[i].style.display = "block";
        })

    }
</script>
{* 点击评论图片查看详情 *}
<div class="commentImgDetail" style="display: none;">
    <button type="button" class="mfp-close">×</button>
    <div>
        <img class="imgDetail" src="" alt="" style="width: 100%;">
        <div class="left" style="position: absolute;left:10px;top:30vh;" onclick="perv()">
            <svg t="1689845060434" class="icon-down" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="3746" width="30" height="40">
                <path
                    d="M538.288 198.624l-11.312-11.312a16 16 0 0 0-22.64 0L187.312 504.336a16 16 0 0 0 0 22.64L504.336 844a16 16 0 0 0 22.64 0l11.312-11.312a16 16 0 0 0 0-22.624l-294.4-294.4 294.4-294.4a16 16 0 0 0 0-22.64z"
                    fill="#fff" p-id="3747"></path>
            </svg>
        </div>
        <div class="right" style="position: absolute;right:10px;top:30vh" onclick="next()">
            <svg t="1689845060434" class="icon-down" viewBox="0 0 1024 1024" version="1.1"
                xmlns="http://www.w3.org/2000/svg" p-id="3746" width="30" height="40">
                <path
                    d="M538.288 198.624l-11.312-11.312a16 16 0 0 0-22.64 0L187.312 504.336a16 16 0 0 0 0 22.64L504.336 844a16 16 0 0 0 22.64 0l11.312-11.312a16 16 0 0 0 0-22.624l-294.4-294.4 294.4-294.4a16 16 0 0 0 0-22.64z"
                    fill="#fff" p-id="3747"></path>
            </svg>
        </div>
    </div>
    <div style="display: flex;padding-top: 35px;padding-left: 15px;">
        <img class="productImg" src="{$comment.product_image}" alt="">
        <div class="right-share" style="display: flex;">
            <a href="https://www.facebook.com/sharer/sharer.php?u={$comment.product_url}" target="_blank"
                class="share-icon facebook-share-icon">{l s="Facebook"
                                    mod='boncomments'}</a>
            <a href="http://pinterest.com/pin/create/button/?url={$comment.product_url}" title="Pinterest"
                class="share-icon pinterest-share-icon" target="_blank">{l
                                    s="Pinterest" mod='boncomments'}</a>
            <a href="https://plus.google.com/share?url={$comment.product_url}" class="share-icon googleplus-share-icon"
                title="Google+" target="_blank">{l s="Google+"
                                    mod='boncomments'}</a>
        </div>
    </div>
    <div id="authorName"></div>
    <div class="commentContent"></div>
</div>

<script>
    $(".commentImgDetail .mfp-close").on("click", function() {
        $(".commentImgDetail").hide();
        $("body").css('overflow', '')
    })
    {* shouji查看买家秀 *}
    let imgsrcArr, nowimgSrc;
    $(".comment_img img").on("click", function(e) {
        nowimgSrc = $(this).index()
        imgsrcArr = $(this).closest(".comment_img").find("img")
        /* .each(function(){
            $(this).attr("src", $(this).attr("data-bgsrc"));
        }) */
        $(".commentImgDetail .imgDetail").attr("src", $(this).attr("data-bgsrc"));
        $("#authorName")[0].innerText = $(this).parent().parent().siblings('.comment_author').children(
            '.commentItem').children('.detailInfo').children('strong')[0].innerText
        $(".commentContent")[0].innerText = $(this).parent().siblings('p')[0].innerText
        $(".commentImgDetail").show();
        $("body").css('overflow', 'hidden')
    })

    function next() {
        let arrLength = imgsrcArr.length;
        console.log("next", nowimgSrc < arrLength - 1);
        if (arrLength <= 1) {

        } else if (nowimgSrc < arrLength - 1) {
            //console.log(imgsrcArr[0].getAttribute('data-bgsrc'));
            //console.log(imgsrcArr.eq(0).data('bgsrc'));
            const nextSrc = imgsrcArr[nowimgSrc + 1].getAttribute('data-bgsrc')
            $(".commentImgDetail .imgDetail").attr("src", nextSrc);
            nowimgSrc++;
        }
    }

    function perv() {

        let arrLength = imgsrcArr.length;
        if (arrLength <= 1) {

        } else if (nowimgSrc > 0) {
            const pervSrc = imgsrcArr[nowimgSrc - 1].getAttribute('data-bgsrc')
            $(".commentImgDetail .imgDetail").attr("src", pervSrc);
            nowimgSrc--;
        }
    }
    // 点击back to product page图片弹出弹窗详情
    $('.viewImg ul').on('click', 'img', function() {
        $(".commentImgDetail .imgDetail").attr("src", $(this).attr("src"));
        $("#authorName")[0].innerText = $(this).attr("data-name")
        $(".commentContent")[0].innerText = $(this).attr("data-content")
        $(".commentImgDetail").show();
        $("body").css('overflow', 'hidden')
    })

    function reviewln(reviewid) {
        var rdata = jQuery('#comment_details-' + reviewid + ' p').html();
        var translateboxid = jQuery(".reviewdata-" + reviewid).parents(".translate-box")
        if (!jQuery(".reviewdata-" + reviewid).html()) {

            jQuery.ajax({
                type: 'post',
                dataType: "json",
                url: productcomments_controller_url + "?action=fanyi",
                data:{literal} {rdata:rdata} {/literal},
                success: function(ajaxData) {
                    jQuery(".reviewdata-" + reviewid).html(ajaxData['result']['reviewdata']);

                    translateboxid.show();
                    translateboxid.siblings(".translate").hide();
                }
            });

        } else {
            translateboxid.show();
            translateboxid.siblings(".translate").hide();
        }
    }

    function closereviewln(reviewid) {
        jQuery(".reviewdata-" + reviewid).parents(".translate-box").hide();
        jQuery(".reviewdata-" + reviewid).parents(".translate-box").siblings(".translate").show();
    }
</script>

<style>
    .translate {
        margin-bottom: 10px;
    }

    .translate svg {
        vertical-align: text-top;
    }

    .translate-icon {
        position: relative;
        top: 2px;
        /* margin-right: 2px; */
    }

    .translate-box {
        position: relative;
        display: none;
        background-color: #f2f2f2;
        margin-top: 8px;
        margin-bottom: 10px;
    }

    .translate-close {
        position: absolute;
        display: inline-block;
        top: 6px;
        right: 6px;
        width: 16px;
        height: 16px;
        background: rgba(0, 0, 0, 0) url(/themes/ztheme/assets/images/tag.svg) repeat scroll;
    }

    .translate-content {
        margin: 0 10px;
        padding-top: 24px;
        padding-bottom: 4px;
    }

    .translate-footer {
        display: flex;
        justify-content: flex-end;
        /* justify-content: space-between; */
        margin: 0 10px;
        padding-bottom: 16px;
    }

    .comment-target-language-name {
        display: none;
        color: #FF7176;
    }

    .translate-by-google {
        font-size: 12px;
        color: #999999;
    }

    .translate-google-img {
        position: relative;
        /* top: 3px; */
        width: 45px;
    }
</style>