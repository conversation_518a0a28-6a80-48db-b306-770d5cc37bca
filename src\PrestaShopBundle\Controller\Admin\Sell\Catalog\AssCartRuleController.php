<?php
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email

 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * 
 *

 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace PrestaShopBundle\Controller\Admin\Sell\Catalog;

use Exception;
use PrestaShop\PrestaShop\Core\Domain\CartRule\Command\BulkDeleteCartRuleCommand;
use PrestaShop\PrestaShop\Core\Domain\CartRule\Command\BulkToggleCartRuleStatusCommand;
use PrestaShop\PrestaShop\Core\Domain\CartRule\Command\DeleteCartRuleCommand;
use PrestaShop\PrestaShop\Core\Domain\CartRule\Command\ToggleCartRuleStatusCommand;
use PrestaShop\PrestaShop\Core\Domain\CartRule\Exception\BulkDeleteCartRuleException;
use PrestaShop\PrestaShop\Core\Domain\CartRule\Exception\BulkToggleCartRuleException;
use PrestaShop\PrestaShop\Core\Domain\CartRule\Exception\CartRuleException;
use PrestaShop\PrestaShop\Core\Domain\CartRule\Query\GetCartRuleForEditing;
use PrestaShop\PrestaShop\Core\Domain\CartRule\Query\SearchCartRules;
use PrestaShop\PrestaShop\Core\Domain\CartRule\QueryResult\EditableCartRule;
use PrestaShop\PrestaShop\Core\Search\Filters\CartRuleFilters;
use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use PrestaShopBundle\Security\Annotation\AdminSecurity;
use PrestaShopBundle\Security\Annotation\DemoRestricted;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Db;

/**
 * Responsible for Cart rules (a.k.a cart discounts/vouchers) actions in Back Office
 */
class AssCartRuleController extends FrameworkBundleAdminController
{
    /**
     * Displays cart rule listing page.
     *
     * @param Request $request
     * @param CartRuleFilters $cartRuleFilters
     *
     * @return Response
     */
    public function indexAction(){
        $cart_rule = Db::getInstance()->executeS(
            "SELECT code FROM ". _DB_PREFIX_. "cart_rule
            WHERE  active=1 and code !=' '"
        );

        $rule_code= Db::getInstance()->getValue(
            "SELECT value as rule_name FROM ". _DB_PREFIX_. "configuration
            WHERE  name='ASS_CART_RULES'"
        );

        return $this->render('@PrestaShop/Admin/Sell/Catalog/AssCartRule/index.html.twig', [
            'cart_rule' =>$cart_rule,
            'rule_code' => $rule_code,
        ]);
    }

    public function editAction(Request $request): RedirectResponse
    {
        $code = $request->request->get('code');
        $time = date('Y-m-d H:i:s');
        $rule_name = Db::getInstance()->getValue(
            "SELECT name AS rule_name FROM ". _DB_PREFIX_. "configuration
            WHERE  name='ASS_CART_RULES'"
        );

        if($rule_name){
             Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'configuration` SET `value` = "' . $code . '" WHERE `name`  ="ASS_CART_RULES"');
        }else{
            if($code){
                $data[]=[
                    'name' => 'ASS_CART_RULES',
                    'value' => $code,
                    'date_add' =>$time,
                    'date_upd' =>$time,
                ];
                Db::getInstance()->insert('configuration', $data);
            }
        }

        return $this->redirectToRoute('admin_ass_cart_rules_index');
    }
}