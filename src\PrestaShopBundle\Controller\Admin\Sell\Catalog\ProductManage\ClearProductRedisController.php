<?php

namespace PrestaShopBundle\Controller\Admin\Sell\Catalog\ProductManage;

use MyRedis;
use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Symfony\Component\HttpFoundation\Request;
use Tools;

class ClearProductRedisController extends FrameworkBundleAdminController
{
    /** @var array redis key */
    private $product_redis = [
        'feature' => _PS_REDIS_PRODUCT_FEATURE_, // 特性
        'attribute' => _PS_REDIS_PRODUCT_ATTRIBUTE_, // 属性
        'search' => _PS_REDIS_PRODUCT_SEARCH_, // 搜索结果
        'detail' => _PS_REDIS_PRODUCT_DETAIL_, // 详情
        'translate' => _PS_REDIS_TRANSLATE_, // 翻译
        'best seller' => _PS_REDIS_BEST_SELLER_, // 热卖产品'
    ];

    private $other_redis = [
        '404 product' => _PS_REDIS_404_PRODUCT_, // 404页面产品'
        'size chart' => _PS_REDIS_CLOTHING_SIZE_CHART_, // 产品尺码
    ];

    // 首页展示
    public function indexAction(Request $request)
    {
        $redis_keys = array_merge($this->product_redis, $this->other_redis);
        return $this->render('@PrestaShop/Admin/Sell/Catalog/ClearProductRedis/index.html.twig', [
            'redis_keys' => $redis_keys
        ]);
    }

    /**
     * 清空redis缓存
     * @param \Symfony\Component\HttpFoundation\Request $request
     * @return \Symfony\Component\HttpFoundation\JsonResponse
     */
    public function clearAction(Request $request)
    {
        try {
            $prefix = $request->request->get('key');
            // 获取redis连接
            $redis = Tools::getRedis();
            if (in_array($prefix, array_keys($this->product_redis))) {
                // 查找所有以前缀开头的键
                $keys = $redis->keys($prefix . '*');

                // 删除这些键
                if (!empty($keys)) {
                    $redis->del($keys);
                }
            } else {
                $redis->del($prefix);
            }

            return $this->json(['success' => true]);
        } catch (\Exception $e) {
            return $this->json(['error' => $e->getMessage()]);
        }
    }

}