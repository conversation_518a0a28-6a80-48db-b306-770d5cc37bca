<?php
/**
 * 测试修复效果的简单页面
 */

require_once dirname(__FILE__) . '/config/config.inc.php';

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<!DOCTYPE html>\n";
echo "<html><head><title>修复测试</title></head><body>\n";
echo "<h1>产品变体处理修复测试</h1>\n";

if (isset($_GET['product_id'])) {
    $productId = (int)$_GET['product_id'];
    
    echo "<h2>测试产品 ID: {$productId}</h2>\n";
    
    try {
        // 创建产品对象
        $product = new Product($productId, true, Context::getContext()->language->id);
        
        if (!Validate::isLoadedObject($product)) {
            echo "<p style='color: red;'>错误: 无法加载产品</p>\n";
        } else {
            echo "<p style='color: green;'>✓ 产品加载成功</p>\n";
            echo "<p>产品名称: {$product->name}</p>\n";
            
            // 获取属性组
            $start_time = microtime(true);
            $start_memory = memory_get_usage(true);
            
            echo "<h3>开始处理变体...</h3>\n";
            
            $attributes_groups = $product->getAttributesGroups(Context::getContext()->language->id);
            $variant_count = count($attributes_groups);
            
            echo "<p>变体数量: {$variant_count}</p>\n";
            
            if ($variant_count > 0) {
                // 测试数据结构
                echo "<h4>数据结构测试</h4>\n";
                $sample = array_slice($attributes_groups, 0, 3, true);
                
                foreach ($sample as $index => $data) {
                    echo "<p>变体 #{$index}: " . gettype($data);
                    if (is_array($data)) {
                        echo " (键: " . implode(', ', array_keys($data)) . ")";
                    }
                    echo "</p>\n";
                }
                
                // 模拟处理过程
                echo "<h4>模拟处理过程</h4>\n";
                
                // 简单的数据验证
                $valid_count = 0;
                $invalid_count = 0;
                
                foreach ($attributes_groups as $item) {
                    if (is_array($item) && isset($item['id_attribute'])) {
                        $valid_count++;
                    } else {
                        $invalid_count++;
                    }
                }
                
                echo "<p>有效数据: {$valid_count}</p>\n";
                echo "<p>无效数据: {$invalid_count}</p>\n";
                
                if ($invalid_count > 0) {
                    echo "<p style='color: orange;'>⚠ 发现无效数据，需要使用安全处理方法</p>\n";
                } else {
                    echo "<p style='color: green;'>✓ 所有数据结构正常</p>\n";
                }
            }
            
            $end_time = microtime(true);
            $end_memory = memory_get_usage(true);
            
            $execution_time = $end_time - $start_time;
            $memory_used = $end_memory - $start_memory;
            
            echo "<h3>性能统计</h3>\n";
            echo "<p>执行时间: " . round($execution_time, 4) . " 秒</p>\n";
            echo "<p>内存使用: " . round($memory_used / 1024 / 1024, 2) . " MB</p>\n";
            echo "<p>峰值内存: " . round(memory_get_peak_usage(true) / 1024 / 1024, 2) . " MB</p>\n";
            
            // 建议
            echo "<h3>建议</h3>\n";
            if ($variant_count > 5000) {
                echo "<p style='color: orange;'>⚠ 变体数量较多，建议使用优化处理</p>\n";
            }
            if ($execution_time > 1) {
                echo "<p style='color: orange;'>⚠ 处理时间较长，建议优化</p>\n";
            }
            if ($invalid_count > 0) {
                echo "<p style='color: red;'>⚠ 存在数据结构问题，需要修复</p>\n";
            }
            
            if ($variant_count <= 5000 && $execution_time <= 1 && $invalid_count == 0) {
                echo "<p style='color: green;'>✓ 一切正常，无需特殊处理</p>\n";
            }
        }
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>\n";
        echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    }
    
} else {
    echo "<h2>请输入产品ID进行测试</h2>\n";
    echo "<form method='get'>\n";
    echo "<label>产品ID: <input type='number' name='product_id' required></label>\n";
    echo "<input type='submit' value='开始测试'>\n";
    echo "</form>\n";
    
    echo "<h3>使用说明</h3>\n";
    echo "<ul>\n";
    echo "<li>输入产品ID来测试变体处理</li>\n";
    echo "<li>系统会检查数据结构和性能</li>\n";
    echo "<li>如果发现问题会给出相应建议</li>\n";
    echo "</ul>\n";
}

echo "</body></html>\n";
?>
