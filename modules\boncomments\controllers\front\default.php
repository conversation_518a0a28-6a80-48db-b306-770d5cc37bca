<?php
/**
 * 2007-2021 PrestaShop
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Academic Free License (AFL 3.0)
 * that is bundled with this package in the file LICENSE.txt.
 * It is also available through the world-wide-web at this URL:
 * http://opensource.org/licenses/afl-3.0.php
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email
 * to <EMAIL> so we can send you a copy immediately.
 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * needs please refer to http://www.prestashop.com for more information.
 *
 *  <AUTHOR> SA <<EMAIL>>
 *  @copyright  2007-2021 PrestaShop SA
 *  @license    http://opensource.org/licenses/afl-3.0.php  Academic Free License (AFL 3.0)
 *  International Registered Trademark & Property of PrestaShop SA
 */

// Include Module
include_once(dirname(__FILE__) . '/../../boncomments.php');
// Include Models
include_once(dirname(__FILE__) . '/../../BonComment.php');
include_once(dirname(__FILE__) . '/../../BonCommentImage.php');
include_once(dirname(__FILE__) . '/../../BonCommentCriterion.php');
include_once(dirname(__FILE__) . '/../../../../classes/tools/ImportComment.php');
require_once(_PS_MODULE_DIR_ . '../classes/FtpImage.php');

class BonCommentsDefaultModuleFrontController extends ModuleFrontController
{
	public function __construct()
	{
		parent::__construct();

		$this->context = Context::getContext();
	}

	public function initContent()
	{
		parent::initContent();

		if (Tools::isSubmit('action')) {
			switch (Tools::getValue('action')) {
				case 'add_comment':
					$this->ajaxProcessAddComment();
					break;
				case 'report_abuse':
					$this->ajaxProcessReportAbuse();
					break;
				case 'comment_is_usefull':
					$this->ajaxProcessCommentIsUsefull();
					break;
				case 'show_add_comment_form':
					$this->showAddCommentForm();
					break;
				case 'show_comments':
					$this->ajaxShowProductReview();
					break;
				case 'fanyi':
					$this->ajaxReviewFanyi();
					break;
			}
		}
	}

	protected function ajaxProcessAddComment()
	{
		$module_instance = new BonComments();
		
		$result = true;
		$id_guest = 0;
		$id_customer = $this->context->customer->id;
		if (!$id_customer)
			$id_guest = $this->context->cookie->id_guest;

		$errors = array();
		// Validation
		if (!Tools::getValue('g-recaptcha-response')) {
			$errors[] = Tools::translate('Incorrect Validation.');
		}
		
		if (!Validate::isInt(Tools::getValue('id_product')))
			$errors[] = $module_instance->l('Product ID is incorrect', 'default');
		if (!Tools::getValue('title') || !Validate::isGenericName(Tools::getValue('title')))
			$errors[] = $module_instance->l('Title is incorrect', 'default');
		if (!Tools::getValue('content') || !Validate::isMessage(Tools::getValue('content')))
			$errors[] = $module_instance->l('Comment is incorrect', 'default');
		if (!$id_customer && (!Tools::isSubmit('customer_name') || !Tools::getValue('customer_name') || !Validate::isGenericName(Tools::getValue('customer_name'))))
			$errors[] = $module_instance->l('Customer name is incorrect', 'default');
		if (!$this->context->customer->id && !Configuration::get('BON_COMMENTS_ALLOW_GUESTS'))
			$errors[] = $module_instance->l('You must be connected in order to send a comment', 'default');
		if (!count(Tools::getValue('criterion')))
			$errors[] = $module_instance->l('You must give a rating', 'default');

		$product = new Product(Tools::getValue('id_product'));
		if (!$product->id)
			$errors[] = $module_instance->l('Product not found', 'default');


		if (!count($errors)) {
			$customer_comment = BonComment::getByCustomer(Tools::getValue('id_product'), $id_customer, true, $id_guest);
			if (!$customer_comment || ($customer_comment && (strtotime($customer_comment['date_add']) + (int) Configuration::get('BON_COMMENTS_MINIMAL_TIME')) < time())) {
				$comment = new BonComment();
				$comment->content = strip_tags(Tools::getValue('content'));
				$comment->id_product = (int) Tools::getValue('id_product');
				$comment->id_customer = (int) $id_customer;
				$comment->id_guest = $id_guest;
				$comment->is_fit = (int) Tools::getValue('is_fit') ?: 2;
				$comment->id_country = (int) Tools::getValue('id_country') ?: 0;
				$comment->id_shop = Context::getContext()->shop->id;
				$comment->customer_name = Tools::getValue('customer_name');
				if (!$comment->customer_name) {
					$comment->customer_name = pSQL($this->context->customer->firstname . ' ' . $this->context->customer->lastname);
				}
				$comment->title = Tools::getValue('title');
				$comment->grade = 0;
				$comment->validate = 0;
				$comment->color = Tools::getValue('color') ?? '';
				$comment->size = Tools::getValue('size') ?? '';
				$comment->point_num = mt_rand(10, 99);
				$comment->save();

				// 评分
				$grade_sum = 0;
				foreach (Tools::getValue('criterion') as $id_product_comment_criterion => $grade) {
					$grade_sum += (float) $grade;
					$product_comment_criterion = new BonCommentCriterion($id_product_comment_criterion);
					if ($product_comment_criterion->id)
						$product_comment_criterion->addGrade($comment->id, $grade);
				}

				if (count(Tools::getValue('criterion')) >= 1) {
					$comment->grade = $grade_sum / count(Tools::getValue('criterion'));
					// Update Grade average of comment
					$comment->save();
				}

				// 图片
				if ($images = Tools::getValue('images')) {
					// 文件路径
					$date_str = date('YmdHis');
					$dir = dirname(dirname(__DIR__)) . DIRECTORY_SEPARATOR . 'img' . DIRECTORY_SEPARATOR . $date_str . DIRECTORY_SEPARATOR . $comment->id . DIRECTORY_SEPARATOR;
					if (!file_exists($dir)) {
						@mkdir($dir, 0777, true);
					}

					// 设置 FTP 连接
    				// $ftp_conn = FtpImage::ftp_image_login();
					// ftp_pasv($ftp_conn, true);

					foreach ($images as $k => $image) {
						$product_comment_image = new BonCommentImage();
						if (preg_match('/^data:image\/(\w+);base64,/', $image, $type)) {
							// 获取逗号后面的字符串
							$data = substr($image, strpos($image, ',') + 1);
							// 解码Base64字符串
							$data = base64_decode($data);
							// 计算解码后数据的大小（字节）
							$dataSize = strlen($data);

							// 判断是否超过 10MB
							$maxSize = 10 * 1024 * 1024; // 10MB
							if ($dataSize > $maxSize) {
								$errors[] = $module_instance->l('The image size exceeds 10MB', 'default');
							}
							// 设置保存图片的文件名
							$image_name = 'img_' . md5($comment->id . $k . $date_str);

							//新保存文件
							// $remote_folder = '/modules/boncomments/img/'. $date_str . DIRECTORY_SEPARATOR . $comment->id . DIRECTORY_SEPARATOR;
							// $remote_name = $image_name . '.jpg';
							// $original_image = ImageManager::resize_from_string($data, 480, 640, 'jpg');
							// //原图上传
							// if(!FtpImage::ftp_upload_stream($ftp_conn, $remote_name, $remote_folder, $original_image)){
							// 	$result = false;
							// 	$errors[] = $module_instance->l('Comment image upload failed', 'default');
							// }
							// $thumb_name = 'thumb_' . $image_name . '.jpg';
							// $thumb_image = ImageManager::resize_from_string($data, 72, 96);
							// // 缩略图上传
							// if(!FtpImage::ftp_upload_stream($ftp_conn, $thumb_name, $remote_folder, $thumb_image)){
							// 	$result = false;
							// 	$errors[] = $module_instance->l('Comment image upload failed', 'default');
							// }

							// 保存文件
							if (file_put_contents($dir . $image_name . '.jpg', $data)) {
								// 改尺寸
								if (ImageManager::resize($dir . $image_name . '.jpg', $dir . $image_name . '.jpg', 480, 640, 'jpg')) {
									$product_comment_image->id_product_comment = $comment->id;
									$product_comment_image->path = $date_str . DIRECTORY_SEPARATOR . $comment->id . DIRECTORY_SEPARATOR . $image_name . '.jpg';
									// 缩略图
									if (ImageManager::resize($dir . $image_name . '.jpg', $dir . 'thumb_' . $image_name . '.jpg', 72, 96)) {
										$product_comment_image->thumb_path = $date_str . DIRECTORY_SEPARATOR . $comment->id . DIRECTORY_SEPARATOR . 'thumb_' . $image_name . '.jpg';
									}
									$product_comment_image->save();
									//将图片上传到指定FTP上
									$local_path = _PS_MODULE_DIR_ . 'boncomments/img/' . $product_comment_image->path;
									if($product_comment_image->path){
										// 将反斜杠替换为正斜杠，确保路径一致
										$remote_path = $image_name . '.jpg';
										$remote_folder = '/modules/boncomments/img/'.$date_str . DIRECTORY_SEPARATOR . $comment->id . DIRECTORY_SEPARATOR;

										$local_path = str_replace('\\', '/', $local_path);
										$remote_folder = str_replace('\\', '/', $remote_folder);
										FtpImage::ftp_upload($local_path, $remote_path,$remote_folder);
									}
                                    $local_thumb_path = _PS_MODULE_DIR_ . 'boncomments/img/' . $product_comment_image->thumb_path;
									if($product_comment_image->thumb_path){
										// 将反斜杠替换为正斜杠，确保路径一致
										$remote_path = 'thumb_' . $image_name . '.jpg';
										$remote_folder = '/modules/boncomments/img/'.$date_str . DIRECTORY_SEPARATOR . $comment->id . DIRECTORY_SEPARATOR;

										$local_thumb_path = str_replace('\\', '/', $local_thumb_path);
										$remote_folder = str_replace('\\', '/', $remote_folder);
										FtpImage::ftp_upload($local_thumb_path, $remote_path,$remote_folder);
									}
								}
							}
							// $product_comment_image->id_product_comment = $comment->id;
							// $product_comment_image->path = $date_str . DIRECTORY_SEPARATOR . $comment->id . DIRECTORY_SEPARATOR . $image_name . '.jpg';
							// $product_comment_image->thumb_path = $date_str . DIRECTORY_SEPARATOR . $comment->id . DIRECTORY_SEPARATOR . 'thumb_' . $image_name . '.jpg';
							// $product_comment_image->save();
						}
					}
					$comment->has_img = 1;
					$comment->save();
				}
				// 关闭 FTP 连接
				// ftp_close($ftp_conn);

				$result = $module_instance->l('review success', 'default');
				Hook::exec('actionSpawnActionBonComment', array(Tools::getValue('')));
				Tools::clearCache(Context::getContext()->smarty, $this->getTemplatePath('boncomments_reviews.tpl'));
			} else {
				$result = false;
				$errors[] = $module_instance->l('Please wait before posting another comment', 'default') . ' ' . Configuration::get('BON_COMMENTS_MINIMAL_TIME') . ' ' . $module_instance->l('seconds before posting a new comment', 'default');
			}
		} else
			$result = false;

		die(json_encode(array(
			'result' => $result,
			'errors' => $errors
		)));
	}

	protected function ajaxProcessReportAbuse()
	{
		if (!Tools::isSubmit('id_product_comment'))
			die('0');

		if (BonComment::isAlreadyReport(Tools::getValue('id_product_comment'), $this->context->cookie->id_customer))
			die('0');

		if (BonComment::reportComment((int) Tools::getValue('id_product_comment'), $this->context->cookie->id_customer)) {
			Hook::exec('actionSpawnActionBonComment', array(Tools::getValue('')));
			die('1');
		}

		die('0');
	}

	protected function ajaxProcessCommentIsUsefull()
	{
		if (!Tools::isSubmit('id_product_comment') || !Tools::isSubmit('value'))
			die('0');

		if (BonComment::isAlreadyUsefulness(Tools::getValue('id_product_comment'), $this->context->cookie->id_customer))
			die('0');

		if (BonComment::setCommentUsefulness((int) Tools::getValue('id_product_comment'), (bool) Tools::getValue('value'), $this->context->cookie->id_customer)) {
			Hook::exec('actionSpawnActionBonComment', array(Tools::getValue('')));
			die('1');
		}

		die('0');
	}

	// 产品详情页写评论的数据
	protected function showAddCommentForm()
	{
		$id_product = (int) Tools::getValue('id_product');
		$product = new Product($id_product, false, $this->context->language->id);
		$image = Product::getCover($id_product);
		$cover_image = '';
		if ($image) {
			$cover_image = $this->context->link->getImageLink($product->link_rewrite, $image['id_image'], ImageType::getFormattedName('home'));
		}
		// 属性
		$product_obj = new Product($product->id, false, $this->context->language->id);
		$groups = [];
		$attributes_groups = $product_obj->getAttributesGroups($this->context->language->id);
		foreach ($attributes_groups as $row) {
			if ($row['group_type'] == 'color' || $row['group_type'] == 'size') {
				if (!isset($groups[$row['id_attribute_group']])) {
					$groups[$row['id_attribute_group']] = [
						'type' => $row['group_type'],
						'id_attribute_group' => $row['id_attribute_group'],
						'group_name' => $row['group_name'],
						'public_group_name' => Tools::translate($row['public_group_name']),
						'attributes' => [],
					];
				}

				$groups[$row['id_attribute_group']]['attributes'][$row['id_attribute']] = Tools::translate($row['attribute_name']);
			}
		}
		$this->context->smarty->assign([
			'logged' => $this->context->customer->isLogged(true),
			'productcomments_product' => $product,
			'product' => ['id_product' => $product->id],
			'criterions' => BonCommentCriterion::getByProduct((int) $product->id, $this->context->language->id),
			'groups' => $groups,
			'countries' => Country::getCountries($this->context->language->id, true, false, false),
			'allow_guests' => (int) Configuration::get('BON_COMMENTS_ALLOW_GUESTS'),
			'id_product_comment_form' => (int) $product->id,
			// 'productcomment_cover' => (int) Tools::getValue('id_product') . '-' . (int) $image['id_image'],
			'productcomment_cover_image' => $cover_image,
			'productcomment_name' => $product->name,
			'productcomment_sku' => $product->supplier_reference,
			'productcomments_controller_url' => $this->context->link->getModuleLink('boncomments'),
			'moderation_active' => (int) Configuration::get('BON_COMMENTS_MODERATE') ?: false,
			'productcomments_url_rewriting_activated' => (int) Configuration::get('PS_REWRITING_SETTINGS'),
			'secure_key' => Tools::encrypt('boncomments'),
			'product_url' => $this->context->link->getProductLink($product->id, $product->link_rewrite, null, null, $this->context->language->id),
		]);

		// 判断走手机端还是pc端
		$mobile_detect = new Mobile_Detect();
		if ($mobile_detect->isMobile()) {
			$this->setTemplate('module:boncomments/views/templates/front/default_write_mobile.tpl');
		} else {
			$this->setTemplate('module:boncomments/views/templates/front/default_write.tpl');
		}
	}

	public function ajaxShowProductReview()
	{
		$id_product = Tools::getValue('id_product');
		$p = Tools::getValue('p');
		$id_customer = $this->context->customer->id;
		$comments = BonComment::newgetByProduct($id_product, $id_customer, $p, 5);
		$html_comments = [];
		foreach ($comments as $key => $item) {
			$html_comments[$key] = $this->render(
				'catalog/_partials/product_review',
				[
					'comments' => $item['comments'],
				]
			);
		}

		$this->ajaxRender(json_encode([
			'comments' => $html_comments
		]));
	}

	//小语种 翻译
	public function ajaxReviewFanyi()
	{
		Tools::unlimit();
		$reviewdata = array();
		$data = Tools::getValue('rdata');
		$apiKey = _PS_FANYI_KEY_;
		$errors = array();
		if ($data) {
			$url = 'https://www.googleapis.com/language/translate/v2?key=' . $apiKey . '&q=' . rawurlencode($data) . '&target=' . _PS_COUNTRY_FANYI_;
			// 使用 cURL 发送请求
			$handle = curl_init($url);
			curl_setopt($handle, CURLOPT_RETURNTRANSFER, true);
			$response = curl_exec($handle);
			$responseDecoded = json_decode($response, true);
			curl_close($handle);
			$reviewdata['reviewdata'] = $responseDecoded['data']['translations'][0]['translatedText'] ?? '';
		}

		die(json_encode(array(
			'result' => $reviewdata,
			'errors' => $errors
		)));

	}


}
