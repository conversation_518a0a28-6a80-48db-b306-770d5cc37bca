<?php
/**
 * 批量导入产品
 */

namespace PrestaShopBundle\Command;

use Configuration;
use PrestaShop\PrestaShop\Adapter\LegacyContext;
use PrestaShop\PrestaShop\Core\CommandBus\CommandBusInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;
use Symfony\Component\Console\Output\OutputInterface;
use Db;
use Tools;
use ImportProduct;

class ImportProductCommand extends Command
{
    /**
     * @var CommandBusInterface
     */
    private $commandBus;

    /**
     * @var LegacyContext
     */
    private $legacyContext;

    protected $header = [
        'image',
        'gallimg',
        'typecsvname',
        'store',
        'websites',
        'attribute_set',
        'type',
        'category_ids',
        'sku',
        'options_container',
        'is_imported',
        'has_options',
        'name',
        'url_key',
        'meta_title',
        'meta_description',
        'gender',
        'small_image',
        'thumbnail',
        'url_path',
        'price',
        'special_price',
        'weight',
        'likes',
        'more views',
        'description',
        'short_description',
        'special_from_date',
        'news_from_date',
        'news_to_date',
        'status',
        'visibility',
        'tax_class_id',
        'qty',
        'is_in_stock',
        'store_id',
        'Color:drop_down:10:1',
        'Size:drop_down:20:1',
        'Measurement Unit:radio:30:1',
        'Bust:field:40:1',
        'Waist:field:50:1',
        'Hips:field:60:1',
        'Hollow to Floor:field:70:1',
        'Height:field:80:1',
        'Extra Length:field:90',
        'Arm:field:100',
        'Note:area:120',
        'attributes'
    ];

    public function __construct(CommandBusInterface $commandBus, LegacyContext $legacyContext)
    {
        parent::__construct();
        $this->commandBus = $commandBus;
        $this->legacyContext = $legacyContext;
    }

    protected function configure()
    {
        // The name of the command (the part after "bin/console")
        $this->setName('prestashop:import-product');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        set_time_limit(0);
        ini_set('display_errors', 'on');
        ini_set('memory_limit', -1);
        $io = new SymfonyStyle($input, $output);

        // 查询未导入的数据文件
        $import_products = Db::getInstance()->executeS('SELECT * FROM `' . _DB_PREFIX_ . 'import_product` WHERE status = 0 AND `deleted` = 0');
        if (!$import_products) {
            $io->success(sprintf('success'));
            return 0;
        }

        // 先获取所有已存在的数据，用于判断是否已存在
        $exist_data = ImportProduct::getExistInfo();

        // 开始执行
        foreach ($import_products as $import_product) {
            $file_path = _PS_ROOT_DIR_ . $import_product['import_file'];
            if (!file_exists($file_path)) {
                // 写入错误文件
                $error = '文件不存在：' . $file_path;
                $error_file = $this->saveErrorFile($import_product['id_import_product'], [$error]);
                // 修改状态
                $this->changeStatus(2, $import_product['id_import_product'], $error_file);
                continue;
            }
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            if (!$data) {
                $error = '文件数据为空：' . $file_path;
                $error_file = $this->saveErrorFile($import_product['id_import_product'], [$error]);
                // 修改状态
                $this->changeStatus(2, $import_product['id_import_product'], $error_file);
                continue;
            }

            // 获取表头
            $headers = array_flip(array_filter(array_shift($data)));

            // 判断表头是否正确
            if (implode(',', array_keys($headers)) != implode(',', $this->header)) {
                // 有错误信息写入文件
                $error_file = $this->saveErrorFile($import_product['id_import_product'], ['表头错误']);
                // 更改状态失败，并记录错误文件
                $this->changeStatus(2, $import_product['id_import_product'], $error_file);
                continue;
            }

            // 整理数据
            $organizated_data = $this->Organization(
                $data,
                $headers,
                $exist_data['skus'],
                $exist_data['category_ids'],
                $exist_data['feature_ids'],
                $exist_data['feature_value_ids'],
                $exist_data['attribute_group_ids'],
                $exist_data['attribute_ids'],
                $exist_data['material_ids'],
                $exist_data['id_shop'],
                $exist_data['languages']
            );

            // 导入数据
            $this->insertData($organizated_data['insert_data'], $exist_data['id_shop'], $exist_data['languages'], $organizated_data['errors']);

            // 判断是否有错误信息
            if ($organizated_data['errors']) {
                // 有错误信息写入文件
                $error_file = $this->saveErrorFile($import_product['id_import_product'], $organizated_data['errors']);
                // 更改状态失败，并记录错误文件
                $this->changeStatus(2, $import_product['id_import_product'], $error_file);
                continue;
            }

            // 没有错误信息，更改状态成功
            $this->changeStatus(3, $import_product['id_import_product']);
            continue;
        }

        $io->success(sprintf('end'));
        return 0;
    }

    /**
     * 整理数据
     * @param mixed $data
     * @param mixed $exist_skus
     * @param mixed $exist_category_ids
     * @param mixed $exist_feature_ids
     * @param mixed $exist_feature_value_ids
     * @param mixed $exist_attribute_group_ids
     * @param mixed $exist_attribute_ids
     * @param mixed $exist_material_ids
     * @param mixed $id_shop
     * @param mixed $languages
     * @return array
     */
    protected function Organization(
        $data,
        $headers,
        &$exist_skus,
        &$exist_category_ids,
        &$exist_feature_ids,
        &$exist_feature_value_ids,
        &$exist_attribute_group_ids,
        &$exist_attribute_ids,
        &$exist_material_ids,
        $id_shop,
        $languages
    ){
        // 错误信息
        $errors = [];
        // 商品数据
        $products = [];
        // 语言数据
        $product_langs = [];
        // 店铺数据
        $product_shops = [];
        // 特价
        $specific_price = [];
        // 新的sku
        $new_skus = [];
        // 其它关联数据: 分类、属性、特征、图片
        $other_data = [];
        foreach ($data as $key => $value) {
            try {
                $sku = strtoupper($value[$headers['sku']]);
                if (!$sku) {
                    throw new \Exception($key + 2 . '行的 SKU 不可为空');
                }
                // 检测SKU是否存在
                if (in_array($sku, $exist_skus)) {
                    throw new \Exception($key + 2 . '行的 sku: ' . $sku .' 已存在');
                }
                // 判断所有字段是否有中文字符
                foreach ($headers as $label => $header) {
                    if ($value[$header] === false) {
                        throw new \Exception(throw new \Exception($key + 2 . '行的 ' . $label . ': 文本格式错误或有中文字符， 请检查确认'));
                    }
                }
                // 产品数据
                $cover_image = isset($headers['image']) ? $value[$headers['image']] : '';
                $images = (isset($headers['gallimg']) && $value[$headers['gallimg']])
                    ? array_merge([$cover_image], explode(';', $value[$headers['gallimg']]))
                    : ($cover_image ? [$cover_image] : []);
                // 分类
                $category_ids = (isset($headers['category_ids']) && $value[$headers['category_ids']])
                    ? explode(',', $value[$headers['category_ids']])
                    : [Configuration::get('PS_HOME_CATEGORY')];
                // 默认分类
                $id_category_default = 0;
                // 产品类型
                $attribute_set = '';
                $type = 'clothes';
                if (isset($headers['attribute_set']) && $value[$headers['attribute_set']]) {
                    $attribute_set = trim($value[$headers['attribute_set']]);
                    if (strpos(strtolower($attribute_set), 'dresses') !== false) {
                        $type = 'clothes';
                    } elseif (strpos(strtolower($attribute_set), 'shoes') !== false) {
                        $type = 'shoes';
                    } else {
                        $type = 'ornament';
                    }
                    // 如果存在这个属性集的分类就当做主分类用
                    $id_category_default = $exist_category_ids[strtolower($attribute_set)] ?? 0;
                }
                // 如果主分类为0，则从分类列表中获取大于2的第一个分类
                if (!$id_category_default || !in_array($id_category_default, $category_ids)) {
                    // 获取大于2的分类,并升序排列
                    $new_category_ids = array_filter($category_ids, function($id) {
                        return $id > 2;
                    });
                    sort($new_category_ids);
                    // 获取第一个分类
                    $id_category_default = reset($new_category_ids);
                }
                // 没有主分类，则取系统默认分类
                if (!$id_category_default) {
                    $id_category_default = Configuration::get('PS_HOME_CATEGORY');
                }
                $name = isset($headers['name']) ? $value[$headers['name']] : '';
                $url_key = isset($headers['url_key']) ? $value[$headers['url_key']] : '';
                $meta_title = isset($headers['meta_title']) ? $value[$headers['meta_title']] : '';
                $meta_description = isset($headers['meta_description']) ? $value[$headers['meta_description']] : '';
                $price = isset($headers['price']) ? (float) $value[$headers['price']] : 0;
                $special_price = isset($headers['special_price']) ? (float) $value[$headers['special_price']] : 0;
                $weight = isset($headers['weight']) ? $value[$headers['weight']] : 0;
                $description = isset($headers['description']) ? $value[$headers['description']] : '';
                $model_description = isset($headers['model description']) ? $value[$headers['model description']] : '';
                $short_description = isset($headers['short_description']) ? $value[$headers['short_description']] : '';
                $status = isset($headers['status']) ? ($value[$headers['status']] == 'Enabled' ? 1 : 0) : 1;
                $visibility = isset($headers['visibility']) ? (($value[$headers['visibility']] == 'Catalog, Search') ? 'both' : $value[$headers['visibility']]) : '';
                // 颜色
                $attribute_color_sizes = [];
                if (isset($headers['Color:drop_down:10:1']) && $value[$headers['Color:drop_down:10:1']]) {
                    $attribute_color_sizes['Color'] = explode('||', $value[$headers['Color:drop_down:10:1']]);
                }
                // 尺寸
                if (isset($headers['Size:drop_down:20:1']) && $value[$headers['Size:drop_down:20:1']]) {
                    $attribute_color_sizes['Size'] = $this->getSize($value[$headers['Size:drop_down:20:1']]);
                }
                // 没有属性加个默认颜色属性
                if (empty($attribute_color_sizes)) {
                    $attribute_color_sizes['Color'] = ['Show as picture'];
                }
                // 整理属性
                $attributes_result = $this->OrganizationAttributes($attribute_color_sizes, $images);
                // 颜色图片
                $color_images = $attributes_result['color_images'];
                // 属性
                $attributes = $attributes_result['attributes'];
                // 属性价格位置索引
                $attribute_price_positions = $attributes_result['attribute_price_positions'];
                // 获取id
                $attribute_ids = ImportProduct::getAttributeIds($attributes, $exist_attribute_group_ids, $exist_attribute_ids, $languages);
                // 颜色映射
                // $color_mappings = isset($headers['Color Mapping']) ? $this->OrganizationColorMappings($value[$headers['Color Mapping']], $exist_attribute_ids) : [];
                // 是否有拖尾
                $is_trailing = isset($headers['Extra Length:field:90']) && $value[$headers['Extra Length:field:90']] ? 1 : 0;
                // 整理特性
                $features_result = (isset($headers['attributes']) && $value[$headers['attributes']]) ? $this->OrganizationFeatures(explode(';', $value[$headers['attributes']])) : [];
                // 材质
                $material = $features_result['material'] ?? '';
                // 特性
                $features = $features_result['features'] ?? [];
                // 颜色映射
                $color_mappings = (isset($features_result['color_mappings']) && $features_result['color_mappings']) ? $this->OrganizationColorMappings($features_result['color_mappings'], $attribute_ids, $exist_attribute_ids, $languages) : [];
                // 添加时间
                $create_date = Tools::convertToTimezone(date('Y-m-d H:i:s'), 'Asia/Shanghai', Configuration::get('PS_TIMEZONE'));
                // 表格时间
                $news_from_date = isset($headers['news_from_date']) ? date('Y-m-d H:i:s', strtotime($value[$headers['news_from_date']])) : $create_date;

                // 产品数据
                $products[$sku] = [
                    'supplier_reference' => $sku,
                    'id_category_default' => $id_category_default,
                    'price' => $price,
                    'current_price' => $special_price,
                    'weight' => $weight / 1000,
                    'is_trailing' => $is_trailing,
                    'id_material' => $material ? ImportProduct::getIdMaterial($material, $exist_material_ids, $languages) : 0,
                    'visibility' => $visibility,
                    'type' => $type,
                    'active' => 0,
                    'date_add' => $news_from_date,
                    'date_upd' => $news_from_date,
                    'create_date' => $create_date,
                    'attribute_set' => $attribute_set,
                ];

                // shop data
                $product_shops[] = [
                    'supplier_reference' => $sku,
                    'id_shop' => $id_shop,
                    'id_category_default' => $id_category_default,
                    'price' => $price,
                    'active' => 0,
                    'visibility' => $visibility,
                    'date_add' => $news_from_date,
                    'date_upd' => $news_from_date
                ];

                // Product Languages
                foreach ($languages as $language) {
                    $product_langs[] = [
                        'supplier_reference' => $sku,
                        'id_lang' => $language['id_lang'],
                        'id_shop' => $id_shop,
                        'name' => $name,
                        'description' => $description,
                        'description_short' => $short_description,
                        'link_rewrite' => str_replace('-' . strtolower($sku), '', $url_key),
                        'meta_description' => $meta_description,
                        'meta_title' => $meta_title,
                        'model_description' => $model_description,
                    ];
                }

                // 特价
                if ($price != $special_price) {
                    $reduction = $price - $special_price;
                    $specific_price[] = [
                        'supplier_reference' => $sku,
                        'reduction' => $reduction,
                    ];
                }

                // product data
                $other_data[$sku] = [
                    'category_ids' => array_unique($category_ids),
                    'cover_image' => $cover_image,
                    'images' => array_unique($images),
                    'feature_ids' => ImportProduct::getFeatureIds($features, $exist_feature_ids, $exist_feature_value_ids, $languages),
                    'attribute_ids' => $attribute_ids,
                    'product_has_attributes' => $this->OrganizationProductHasAttributes($exist_attribute_group_ids, $exist_attribute_ids, $attribute_price_positions, $color_mappings),
                    'color_id_images' => $this->OrganizationColorIdImages($exist_attribute_group_ids, $exist_attribute_ids, $color_images),
                    'product_active' => $status,
                ];

                // 整理sku
                $new_skus[] = $sku;

                // 保存已存在的sku
                $exist_skus = array_merge($exist_skus, [$sku]);

            } catch (\Throwable $e) {
                $errors[] = $e->getMessage();
                continue;
            }
        }

        return [
            'insert_data' => [
                'products' => $products,
                'product_shops' => $product_shops,
                'product_langs' => $product_langs,
                'specific_price' => $specific_price,
                'other_data' => $other_data,
                'new_skus' => $new_skus,
            ],
            'errors' => $errors
        ];
    }

    /**
     * 批量新增
     * @param mixed $insert_data
     * @param mixed $id_shop
     * @param mixed $languages
     * @param mixed $errors
     * @return bool
     */
    protected function insertData($insert_data, $id_shop, $languages, &$errors)
    {
        // 验证
        if (!$insert_data) {
            return true;
        }
        // 商品数据
        $products = $insert_data['products'];
        if (!$products) {
            return true;
        }
        // 商品主要数据
        try {
            Db::getInstance()->execute('BEGIN');
            // 批量导入产品数据
            ImportProduct::insertProducts($products);
            // 新增的SKU
            $new_skus = $insert_data['new_skus'];
            // 获取新增的ID
            $new_product_ids = ImportProduct::getNewProductSkuToIds($new_skus);
            // 产品店铺数据
            $this->insertProductShops($insert_data['product_shops'], $new_product_ids);
            // 产品语言
            $this->insertProductLangs($insert_data['product_langs'], $new_product_ids);
            // 特价
            $this->insertSpecificPrices($insert_data['specific_price'], $new_product_ids, $id_shop);
            Db::getInstance()->execute('COMMIT');
        } catch (\Throwable $e) {
            Db::getInstance()->execute('ROLLBACK');
            $errors[] = $e->getMessage();
        }
        // 其他数据
        $other_data = $insert_data['other_data'];
        foreach ($other_data as $sku => $other_item) {
            $id_product = $new_product_ids[$sku];
            try {
                Db::getInstance()->execute('BEGIN');
                // 产品分类
                ImportProduct::insertCategoryProduct($other_item['category_ids'], $id_product);
                // 产品特性
                ImportProduct::insertProductFeatures($other_item['feature_ids'], $id_product);
                // 产品图片
                $color_id_image_ids = $this->insertProductImages($other_item['cover_image'], $other_item['images'], $other_item['color_id_images'],  $other_item['product_active'], $id_product, $id_shop, $languages);
                // 产品属性组合
                $attribute_id_product_attribute_ids = $this->insertProductAttributes($other_item['attribute_ids'], $other_item['product_has_attributes'], $id_product, $id_shop, $languages);
                // 变体对应的图片
                ImportProduct::addProductAttributeImage($color_id_image_ids, $attribute_id_product_attribute_ids);
                // 产品属性
                ImportProduct::insertProductHasAttributes($other_item['product_has_attributes'], $id_product);
                Db::getInstance()->execute('COMMIT');
            } catch (\Throwable $e) {
                Db::getInstance()->execute('ROLLBACK');
                // 删除报错产品的数据
                $this->delete($id_product);
                $errors[] = $sku . ':' . $e->getMessage();
            }
            // 更新选项卡的redis
            ImportProduct::updateQueryProductHasAttributeRedis($id_product);
        }

        return true;
    }

    /**
     * 删除报错产品的数据
     * @param mixed $id_product
     * @return void
     */
    public function delete($id_product)
    {
        // 删除报错产品的数据
        if ($id_product) {
            Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product` WHERE `id_product` = ' . $id_product);
            Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product_lang` WHERE `id_product` = ' . $id_product);
            Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product_shop` WHERE `id_product` = ' . $id_product);
            Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'specific_price` WHERE `id_product` = ' . $id_product);
        }
    }

    /**
     * 整理产品店铺数据对应产品ID并新增店铺数据
     * @param mixed $product_shops
     * @param mixed $new_product_ids
     * @return mixed
     */
    protected function insertProductShops($product_shops, $new_product_ids)
    {
        $insert_data = [];
        foreach ($product_shops as $product_shop) {
            $insert_data[] = [
                'id_product' => $new_product_ids[$product_shop['supplier_reference']],
                'id_shop' => $product_shop['id_shop'],
                'id_category_default' => $product_shop['id_category_default'],
                'price' => $product_shop['price'],
                'active' => $product_shop['active'],
                'visibility' => $product_shop['visibility'],
            ];
        }
        // 批量导入产品店铺数据
        return ImportProduct::insertProductShops($insert_data);
    }

    /**
     * 整理产品语言数据对应产品ID并新增语言数据
     * @param mixed $product_langs
     * @param mixed $new_product_ids
     * @return mixed
     */
    protected function insertProductLangs($product_langs, $new_product_ids)
    {
        $insert_data = [];
        foreach ($product_langs as $product_lang) {
            $insert_data[] = [
                'id_product' => $new_product_ids[$product_lang['supplier_reference']],
                'id_lang' => $product_lang['id_lang'],
                'id_shop' => $product_lang['id_shop'],
                'name' => $product_lang['name'],
                'description' => $product_lang['description'],
                'description_short' => $product_lang['description_short'],
                'link_rewrite' => $product_lang['link_rewrite'],
                'meta_description' => $product_lang['meta_description'],
                'model_description' => $product_lang['model_description'],
                'meta_title' => $product_lang['meta_title']
            ];
        }

        unset($product_langs);
        return ImportProduct::insertProductLangs($insert_data);
    }

    /**
     * 特价价格导入
     * @param mixed $specific_price
     * @param mixed $new_product_ids
     * @param mixed $id_shop
     * @return mixed
     */
    protected function insertSpecificPrices($specific_price, $new_product_ids, $id_shop)
    {
        $insert_data = [];
        $delete_ids = [];
        foreach ($specific_price as $special_price) {
            $id_product = $new_product_ids[$special_price['supplier_reference']];
            // 删除旧数据
            $delete_ids[] = $id_product;
            // 新增数据
            $insert_data[$id_product] = [
                'id_product' => $id_product,
                'id_shop' => $id_shop,
                'reduction' => $special_price['reduction'],
            ];
        }
        unset($specific_price);
        // 删除旧数据
        ImportProduct::deleteSpecificPrice($delete_ids);
        // 新增数据
        return ImportProduct::insertSpecificPrices($insert_data);
    }

    /**
     * 批量导入产品
     * @param mixed $cover_image
     * @param mixed $images
     * @param mixed $color_id_images
     * @param mixed $prodcut_active
     * @param mixed $id_product
     * @param mixed $id_shop
     * @param mixed $languages
     * @return array
     */
    protected function insertProductImages($cover_image, $images, $color_id_images, $prodcut_active, $id_product, $id_shop, $languages)
    {
        $insert_image_data = [];
        // 排序
        $sort = 1;
        foreach ($images as $image) {
            // 封面
            if ($cover_image == $image) {
                $cover = 1;
                $position = 0;
            } else {
                $cover = 0;
                $position = $sort;
                $sort++;
            }
            // 颜色
            $id_color_attribute = array_search($image, $color_id_images) ?? 0;
            // 图片路径（名称）
            $image = ltrim($image, '/');
            $firstTwoChars = substr($image, 0, 2);
            $firstTwoDir = implode('/', str_split((string) $firstTwoChars));
            $name = '/' . $firstTwoDir . '/' . substr($image, 0, -4);
            $insert_image_data[] = [
                'id_product' => $id_product,
                'id_shop' => $id_shop,
                'id_color_attribute' => $id_color_attribute,
                'position' => $position,
                'cover' => $cover,
                'name' => $name,
                'type' => 'webp'
            ];
        }

        // 获取商品新增的图片id
        $new_images = ImportProduct::batchInsertImages($insert_image_data, $id_product, $id_shop, $languages);
        unset($insert_image_data);

        // 生成图片缓存信息
        $generate_cache_images = [];
        // 颜色对应图片ID
        $color_id_image_ids = [];
        foreach ($new_images as $new_image) {
            // 颜色对应图片ID
            if ($new_image['id_color_attribute']) {
                $color_id_image_ids[$new_image['id_color_attribute']] = $new_image['id_image'];
            }
            // 生成图片缓存信息
            $generate_cache_images[] = [
                'id_product' => $id_product,
                'id_image' => $new_image['id_image'],
                'path' => pSQL($new_image['name']),
                'product_active' => $prodcut_active
            ];
        }

        unset($new_images);
        Tools::batch_insert($generate_cache_images, 'generate_cache_image');
        unset($generate_cache_images);

        return $color_id_image_ids;
    }

    /**
     * 生成变体
     * @param mixed $attribute_ids
     * @param mixed $product_has_attributes
     * @param mixed $id_product
     * @param mixed $id_shop
     * @param mixed $languages
     * @return mixed
     */
    protected function insertProductAttributes($attribute_ids, $product_has_attributes, $id_product, $id_shop, $languages)
    {
        $combinations = Tools::combinationArrays($attribute_ids);
        // 属性对应的价格
        $attribute_prices = [];
        foreach ($product_has_attributes as $attribute) {
            $attribute_prices[$attribute['id_attribute']] = $attribute['price'];
        }
        return ImportProduct::batchInsertProductAttribute($id_product, $combinations, $attribute_prices, $id_shop, $languages);
    }

    /**
     * 整理属性数据
     * @param mixed $attributes
     * @return array{name: string, position: string, price: string[][]}
     */
    protected function OrganizationAttributes($attributes, $images)
    {
        // 用于存储属性结果
        $attribute_result = [];
        // 用于存储颜色图片
        $color_images = [];
        // 属性相关信息
        $attribute_price_positions = [];
        // 遍历属性组
        foreach ($attributes as $group => $str_attributes) {
            // 便利属性
            foreach ($str_attributes as $str_attribute) {
                // 分割属性
                $arr_attribute = explode(':', $str_attribute);
                // 获取属性名称
                if ($group == 'Size') {
                    $name = ImportProduct::getStandardSize($arr_attribute[0]);
                } else {
                    $name = trim(preg_replace('/\s+/', ' ', preg_replace('/[\_?]+/', ' ',  $arr_attribute[0])));
                }
                // 获取属性信息
                $attribute_result[$group][] = $name;
                // 属性相关信息
                $attribute_price_positions[$group][$name] = [
                    'price' => $arr_attribute[2] ?? 0,
                    'position' => $arr_attribute[3] ?? 0
                ];
                // show as picture
                if ($group == 'Color' && strpos(strtolower($name), 'show as picture') !== false) {
                    $color_images[$name] = array_shift($images);
                }
            }
        }

        // 返回结果
        return [
            'attributes' => $attribute_result,
            'color_images' => $color_images,
            'attribute_price_positions' => $attribute_price_positions
        ];
    }

    /**
     * 整理颜色映射数据
     * @param mixed $color_mapping
     * @return array
     */
    protected function OrganizationColorMappings($color_mappings, $attribute_ids, &$exist_attribute_ids, $languages)
    {
        $color_mappings_result = [];
        $color_ids = $attribute_ids[2] ?? [];
        foreach ($color_mappings as $color_mapping) {
            $color_mapping = trim(preg_replace('/\s+/', ' ', preg_replace('/[\_?]+/', ' ',  $color_mapping)));
            $id_attribute_color = ImportProduct::getAttributeId(2, $color_mapping, $exist_attribute_ids, $languages);
            $color_mappings_result[array_shift($color_ids)] = $id_attribute_color;
        }
        return $color_mappings_result;
    }

    /**
     * 整理feature数据
     * @param $features 要整理的特性数据
     * @return array  整理后的特性数据   以及材质信息
     */
    protected function OrganizationFeatures($features)
    {
        $material_name = '';
        $feature_result = [];
        $color_mappings = [];
        foreach ($features as $item) {
            // 特性与值分离
            $feature_key_values = explode(':', $item);
            // 特性与值不为空
            if (isset($feature_key_values[0]) && isset($feature_key_values[1])) {
                // 获取特性名称
                $feature = $feature_key_values[0];
                // 获取特性值
                $feature_values = explode(',', $feature_key_values[1]);
                // 组合特性数据
                $feature_result[$feature] = $feature_values;
                // 获取材质名称
                if (strpos(strtolower($feature), 'fabric') !== false) {
                    $material_name = current($feature_values);
                }
                if (strpos(strtolower($feature), 'dress_color') !== false) {
                    $color_mappings = $feature_values;
                }
            }
        }

        // 返回结果
        return [
            'material' => $material_name,
            'features' => $feature_result,
            'color_mappings' => $color_mappings
        ];
    }

    /**
     * 获取产品关联属性的信息
     * @param mixed $exist_attribute_ids
     * @param mixed $attribute_price_positions
     * @return array
     */
    protected function OrganizationProductHasAttributes($exist_attribute_group_ids, $exist_attribute_ids, $attribute_price_positions, $color_mappings)
    {
        // 获取属性id与价格、位置
        $product_has_attributes = [];
        // 遍历属性价格位置
        foreach ($attribute_price_positions as $group => $attributes) {
            $id_attribute_group = $exist_attribute_group_ids[strtolower($group)];
            foreach ($attributes as $attribute => $price_position) {
                // 获取属性id
                $id_attribute = $exist_attribute_ids[$id_attribute_group][strtolower($attribute)];
                // 获取属性id与价格、位置
                $product_has_attributes[] = [
                    'id_attribute_group' => $id_attribute_group,
                    'id_attribute' => $id_attribute,
                    'price' => $price_position['price'],
                    'position' => $price_position['position'],
                    'id_attribute_color' => $color_mappings[$id_attribute] ?? 0
                ];
            }
        }
        // 返回结果
        return $product_has_attributes;
    }

    /**
     * 获取颜色属性id与图片的键值对
     * @param mixed $exist_attribute_group_ids
     * @param mixed $exist_attribute_ids
     * @param mixed $color_images
     * @return array
     */
    protected function OrganizationColorIdImages($exist_attribute_group_ids, $exist_attribute_ids, $color_images)
    {
        $color_id_images = [];
        // 获取颜色属性组id
        $id_attribute_group = $exist_attribute_group_ids['color'];
        foreach ($color_images as $color => $image) {
            // 获取颜色属性id
            $id_attribute = $exist_attribute_ids[$id_attribute_group][strtolower($color)];
            // 组合颜色属性id与图片的键值对
            $color_id_images[$id_attribute] = $image;
        }

        return $color_id_images;
    }

    /**
     * 更改状态
     * @param mixed $status
     * @param mixed $id_import_product
     * @param mixed $error_file
     * @return bool
     */
    protected function changeStatus($status, $id_import_product, $error_file = false)
    {
        return Db::getInstance()->execute(
            'UPDATE `' . _DB_PREFIX_ .'import_product`
            SET `status` = ' . $status . '
            ' . ($error_file ? ', `error_file` = \'' . pSQL($error_file) . '\'' : '') . '
            WHERE `id_import_product` = ' . $id_import_product);
    }

    // 保存错误文件
    protected function saveErrorFile($id_import_product, array $errors)
    {
        // 将错误信息写入文件
        $error_file_name = 'import_product_error_' . $id_import_product . '.csv';
        $error_file_path = _PS_UPLOAD_DIR_ . 'tools/import_product/error/';
        if (!is_dir($error_file_path)) {
            mkdir($error_file_path, 0777, true);
        }
        // 打开文件句柄
        $file = fopen($error_file_path . $error_file_name, 'w');

        // 将数组写入CSV文件
        foreach ($errors as $row) {
            fputcsv($file, [$row]);
        }
        // 关闭文件句柄
        fclose($file);

        return _THEME_PROD_PIC_DIR_ . 'tools/import_product/error/' . $error_file_name;
    }

    /**
     * 获取尺寸
     */
    protected function getSize($size_str)
    {
        $data = explode('||', $size_str);
        $sizes = [];
        foreach ($data as $item) {
            $arr_attribute = explode(':', $item);
            $size[$arr_attribute[0]] = [
                'price' => $arr_attribute[2] ?? 0,
                'position' => $arr_attribute[3] ?? 0
            ];
        }
        $sizes = $this->unified_size($size);
        $result = [];
        foreach ($sizes as $key => $size) {
            $result[] = $key . ':fixed:' . $size['price'] . ':' . $size['position'];
        }
        return $result;
    }

    /**
     * 产品尺寸属性去重
     * @param mixed $group
     * @param mixed $attributes
     * @return
     */
    protected function unified_size($attributes)
    {
        if (in_array(_PS_COUNTRY_, ['AU'])) {
            // US14 US14W 都存在保留 US14
            if (isset($attributes['US14-AU/UK18-EUR44']) && isset($attributes['US14W-AU/UK18-EUR44'])) {
                if ($attributes['US14W-AU/UK18-EUR44']['price'] > $attributes['US14-AU/UK18-EUR44']['price']) {
                    $attributes['US14-AU/UK18-EUR44']['price'] = $attributes['US14W-AU/UK18-EUR44']['price'];
                }
                unset($attributes['US14W-AU/UK18-EUR44']);
            }
            // US16 US16W 都存在保留 US16W
            if (isset($attributes['US16-AU/UK20-EUR46']) && isset($attributes['US16W-AU/UK20-EUR46'])) {
                if ($attributes['US16-AU/UK20-EUR46']['price'] > $attributes['US16W-AU/UK20-EUR46']['price']) {
                    $attributes['US16W-AU/UK20-EUR46']['price'] = $attributes['US16-AU/UK20-EUR46']['price'];
                }
                unset($attributes['US16-AU/UK20-EUR46']);
            }
        } elseif (in_array(_PS_COUNTRY_, ['SE', 'FR'])) {
            // US14 US14W 都存在保留 US14
            if (isset($attributes['US14-AU/UK18-EUR44']) && isset($attributes['US14W-AU/UK18-EUR44'])) {
                if ($attributes['US14W-AU/UK18-EUR44']['price'] > $attributes['US14-AU/UK18-EUR44']['price']) {
                    $attributes['US14-AU/UK18-EUR44']['price'] = $attributes['US14W-AU/UK18-EUR44']['price'];
                }
                unset($attributes['US14W-AU/UK18-EUR44']);
            }
            // US16 US16W 都存在保留 US16
            if (isset($attributes['US16-AU/UK20-EUR46']) && isset($attributes['US16W-AU/UK20-EUR46'])) {
                if ($attributes['US16W-AU/UK20-EUR46']['price'] > $attributes['US16-AU/UK20-EUR46']['price']) {
                    $attributes['US16-AU/UK20-EUR46']['price'] = $attributes['US16W-AU/UK20-EUR46']['price'];
                }
                unset($attributes['US16W-AU/UK20-EUR46']);
            }
        }
        return $attributes;
    }
}