$header-text-color: #4e6167;
$search-border-color: #bbcdd2;

#header {
  z-index: 20;
}

#header_infos {
  position: fixed;
  z-index: 503;
  box-sizing: content-box;
  display: flex;
  align-items: stretch;
  width: 100%;
  height: $size-header-height;
  font-size: 13px;
  color: $header-text-color;
  background: #fff;

  .material-icons {
    position: relative;
    vertical-align: middle;
  }

  #header_logo {
    width: 13.13rem;
    height: 100%;
    background: url("#{$img-path}/prestashop-avatar-header_shopname.png") no-repeat center left;

    .mobile & {
      flex-grow: 1;
      width: 0;
      min-width: 172px;
      margin-left: -15px;
    }
  }

  #shop_version {
    position: absolute;
    top: 0.9375rem;
    left: 11.25rem;
    font-size: 10px;
    color: #4a4a4a;
    // hide if viewport <= tablet portrait size
    @media (max-width: breakpoint-max("md")) {
      display: none;
    }
  }

  > .component {
    margin: {
      right: 0.5rem;
      left: 0.5rem;
    };

    @include media-breakpoint-down(sm) {
      margin: 0;
    }
    // hide on very small screens
    &.hide-mobile-sm {
      @media (max-width: breakpoint-max("sm")) {
        display: none;
      }
    }	// disable bootstrap styling
  }

  .btn-link {
    color: initial;

    &:hover {
      color: initial;
      text-decoration: none;
    }

    &:active,
    &:focus {
      text-decoration: none;
      outline: none;
      box-shadow: none;
    }
  }

  #quick_select {
    height: $size-header-height;
    padding: 7px 10px 5px;
    font-size: 13px;
    font-weight: 500;
    line-height: 1.5;
    color: $header-text-color;

    > .material-icons {
      margin-left: -5px;
      font-size: 20px;
    }
    // Shop state / Maintenance / Debug / View my shop
  }

  .shop-state,
  #header_shopname {
    display: inline-block;
    height: $navbar-height;
    padding: $navbar-padding-vertical 5px;
    line-height: $line-height-computed;

    @include media-breakpoint-down(sm) {
      display: flex;
      align-items: center;
    }

    > .material-icons {
      margin-right: $navbar-padding-horizontal;

      @include media-breakpoint-down(sm) {
        margin-right: 0;
      }
    }
  }

  .shop-state {
    padding-top: 11px;
    color: $header-text-color;
    text-decoration: none;
    white-space: nowrap;

    &:hover,
    &:focus {
      color: $brand-info !important;
      text-decoration: none !important;
    }

    .material-icons {
      margin-top: -4px;
      font-size: 20px;
      color: #6c868e;
    }
    @media (max-width: $screen-sm-max) {
      padding: {
        right: 10px !important;
        left: 10px !important;
      }
      margin-right: 0;

      .material-icons {
        margin-right: 0;
      }
    }
  }

  ul.header-list {
    display: flex;
    align-items: stretch;
    padding: 0;
    margin-bottom: 0;
    color: $header-text-color;

    #header_shopname {
      padding-top: 11px;
      color: $header-text-color !important;

      @include media-breakpoint-down(sm) {
        // stylelint-disable-next-line
        height: 100% !important;

        i {
          height: 18px;
        }
      }

      &:hover,
      &:focus {
        color: $brand-info !important;

        i {
          color: $brand-info;
        }
      }
    }

    li {
      display: block;
      list-style: none;

      @include media-breakpoint-down(sm) {
        display: flex;
        align-items: center;
      }

      > a {
        @include media-breakpoint-down(sm) {
          // stylelint-disable-next-line
          height: 100% !important;
        }

        &.notifs {
          position: relative;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          min-width: $size-header-height;
          height: $size-header-height;
          padding: 0 5px !important;
          line-height: $size-header-height;
          vertical-align: middle;

          &:hover,
          &:focus {
            color: $brand-info;
            text-decoration: none;
          }
        }
      }
    }
  }

  #maintenance-mode {
    a {
      color: #6c868e;

      &:hover,
      &:focus {
        color: $brand-info;
        text-decoration: none;
      }
      // view my shop
    }
  }

  #header_shopname {
    height: inherit !important;
    padding-top: 11px;
    overflow-x: hidden;
    font-size: 13px !important;
    text-decoration: none;
    text-overflow: ellipsis;
    white-space: nowrap;

    i {
      color: $medium-gray;
    }
    @media (max-width: 768px) {
      font-size: 0 !important;
    }
    @media (max-width: 320px) {
      max-width: 140px;
    }
  }

  // multishop menu
  #header_shop {
    .dropdown {
      &.open {
        .dropdown-toggle {
          color: $brand-info !important;
          background: none !important;
        }
      }

      .material-icons {
        top: -1px;
        color: $medium-gray;
      }

      .dropdown-toggle {
        display: inline-block !important;
        color: $header-text-color !important;
        text-decoration: none !important;
        @media (max-width: breakpoint-max("lg")) {
          position: relative;
          top: -10px;
          z-index: 50;
          width: 25px;
          height: 25px;
          margin-left: -25px;
          font-size: 0;

          i {
            display: none;
          }
        }

        &:hover {
          color: $brand-info !important;
        }

        i {
          padding-left: 5px;
        }
      }

      .dropdown-menu {
        .list-dropdown-menu {
          max-height: 500px;
          padding: 0;
          margin: 20px;
          overflow-y: auto;
        }
        // employee button
      }
    }
  }

  #header_employee_box {
    display: flex;
    align-items: center;
    padding: 0;
    margin-right: 0;
    margin-bottom: 0;
    margin-left: 0;

    #employee_infos {
      box-sizing: border-box;
      display: block;
      width: 24px;
      margin-right: 10px;
      list-style-type: none;

      @include media-breakpoint-down(sm) {
        width: auto;
        height: 100%;
        margin-right: 0;

        .dropdown-toggle {
          padding: 0 10px;

          i {
            overflow: hidden;
          }
        }
      }

      #employee_links {
        .employee_avatar {
          width: auto;
          height: auto;

          img {
            width: 3.8rem;
            height: 3.8rem;
            padding: 0;
            border: none;
          }
        }

        .employee-wrapper {
          &-avatar {
            float: left;
            margin: 0 20px;
          }

          &-profile {
            display: block;
            margin: 2px 0 20px;
            text-align: center;

            > .admin-link {
              display: inline-block;

              @include media-breakpoint-down(sm) {
                display: block;
                margin: 0 20px;
                border-radius: 5px;
              }
            }
          }
        }
      }

      .employee_name {
        text-align: center;
      }

      .dropdown-menu {
        width: 17.5rem;
        min-width: inherit;
        padding-top: 10px;

        li {
          margin: 15px 7px;
          font-size: 14px;

          &:hover {
            .material-icons {
              color: #fff;
            }
          }

          &.username {
            display: inline-block;
            max-width: 120px;
            padding: 1rem 0 1.5rem;
            margin: 0;
            font-weight: 600;
            white-space: normal;
            vertical-align: top;

            @include media-breakpoint-down(sm) {
              max-width: inherit;
              padding-top: 2rem;
              font-size: 16px;
              font-weight: 500;
              color: $gray-dark-menu;
            }
          }

          &.divider {
            width: 90%;
            margin: 0 auto !important;
            clear: both;
            border: 1px solid $gray-lighter;
          }

          a {
            &[target="_blank"]::after {
              position: relative;
              top: 2px;
              font-family: "Material Icons", sans-serif;
              color: #bbcdd2;
              content: "\e89e";
              opacity: 0.7;
            }

            .material-icons {
              padding: 0;
              margin-right: 5px;
              color: #576c72;
              vertical-align: middle;
            }

            &.admin-link {
              color: #576c72;
              background-color: inherit;
              border: 1px solid #899ea5;
              border-radius: 1px;

              &:hover {
                color: #25b9d7;
                border-color: #25b9d7;

                .material-icons {
                  color: #25b9d7;
                }
              }
            }
          }

          &.signout {
            a {
              color: #6c868e;

              &:hover {
                color: #25b9d7;
                background-color: inherit;
              }

              @include media-breakpoint-down(sm) {
                display: flex;
                align-items: center;
                justify-content: center;

                &,
                i {
                  color: $brand-danger;
                }

                &:hover {
                  color: #fff;
                  background-color: #f54c3e;
                }
              }
            }
          }
        }
      }
    }
  }
}

.bo_search_form {
  @extend .hidden-xs;
  flex-grow: 1;
  margin: 4px 0 0;

  #bo_query {
    @include border-radius(0, 55px, 55px, 0);
    height: 35px;
    padding-right: 35px;
    padding-left: 6px;
    font-style: italic;
    color: $main-color;
    background: #fff;
    border: 1px solid $search-border-color;
    border-left: none;

    &::placeholder {
      color: $gray-medium;
    }
    @include box-shadow(none);
    @include transition(none);
  }

  .form-group {
    width: 250px;

    .input-group {
      margin: 0 !important;

      .input-group-btn {
        @include border-radius(55px, 0, 0, 55px);
        border: 1px solid $search-border-color;
        border-right: none;

        @at-root .bootstrap & {
          > .btn {
            @include border-radius(55px, 0, 0, 55px);
            @include box-shadow(none);
            height: 33px;
            padding: 8px 6px 6px 8px;
            outline: none;
          }
        }

        .btn-default {
          @include box-shadow(none);
          border: 1px solid transparent !important;
        }
      }

      .dropdown-menu {
        .material-icons {
          font-size: 18px;
          color: $medium-gray;
        }

        > li {
          &.active {
            .material-icons {
              color: #fff;
            }
          }

          a {
            display: flex;
            flex-wrap: nowrap;
            column-gap: 10px;
            align-items: center;

            &:hover,
            &:focus {
              .material-icons {
                color: #fff;
              }
            }
          }
        }
      }
    }
  }

  .clear_search {
    position: absolute;
    top: 7px;
    z-index: 10;
    @include right(10px);

    .material-icons {
      font-size: 18px;
    }
  }

  #search_type_icon {
    top: -2px;
    padding-right: 0;
    font-size: 18px;
    line-height: 1;
  }
}

#header_quick {
  @extend .hidden-xs;
  @extend .hidden-sm;
  font-size: 13px;

  .dropdown {
    > a,
    > a:focus {
      color: $header-text-color !important;
      background-color: transparent !important;
    }

    > a {
      .material-icons {
        margin-left: -5px;
        font-size: 20px;
      }
    }

    > a:hover {
      color: $toolbar-buttons-hover-color !important;
    }
  }

  .dropdown-menu {
    > li {
      a {
        padding: 7px 15px;

        &:hover {
          color: #fff;
        }

        .material-icons {
          margin-right: $navbar-padding-horizontal;
        }

        &:not(:hover) {
          .material-icons {
            color: #6c868e;
          }
        }
      }
    }

    .material-icons {
      font-size: 1.125rem;
    }
  }
}

#ajax_running {
  position: absolute;
  top: 0;
  width: 35px;
  height: 35px;
  text-align: center;
  background-color: rgba(#000, 0.4);
  @include left(0);

  i {
    font-size: 20px;
    line-height: 35px;
    color: rgba(#fff, 0.6);
  }
}

#notification,
#employee_infos {

  @include media-breakpoint-down(sm) {
    position: initial;

    &::before {
      position: fixed;
      top: 5rem;
      left: 0;
      width: 100vw;
      height: 100vh;
      pointer-events: none;
      content: "";
      background: rgba(0, 0, 0, 0.8);
      opacity: 0;
    }
  }

  &.open {
    background: none;

    a {
      &.dropdown-toggle {
        background: none !important;

        @include media-breakpoint-down(sm) {
          padding: 0 10px;
          border-bottom: 2px solid $brand-primary;

          &,
          i {
            color: $brand-primary;
          }
        }
      }
    }

    @include media-breakpoint-down(sm) {
      &::before {
        pointer-events: all;
        opacity: 1;
      }
    }
  }

  @include media-breakpoint-down(sm) {
    a {
      &.dropdown-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 100%;
        text-align: center;
      }
    }
  }

  .material-icons {
    font-size: 22px;
    color: #6c868e;
    vertical-align: bottom;

    @include media-breakpoint-down(sm) {
      height: 22px;
    }
  }

  #total_notif_number_wrapper {
    position: absolute;
    top: 0.3rem;
    right: 0.6rem;
    display: inline-block;
    height: 1rem;
    padding: 0 0.18rem;
    line-height: 0.75rem;
    color: #fff;
    vertical-align: middle;
    background: $alert-danger-border;
    border: 2px solid #fff;
    border-radius: 0.625rem;

    font: {
      size: 0.625rem;
    }

    @include media-breakpoint-down(sm) {
      top: 0.8rem;
      right: 0.3rem;
    }

    > span {
      font-size: inherit;
      color: inherit;
    }
  }

  .dropdown-menu {
    top: 42px;
    right: 5px !important;
    min-width: 25rem;
    padding: 0;
    margin: 0;
    border: 1px solid #bbcdd2;
    border-radius: 0;
    box-shadow: 0 2px 2px 0 rgba(0, 0, 0, 0.1);

    @include media-breakpoint-down(sm) {
      top: 60px;
      right: inherit;
      left: 0;
      // stylelint-disable-next-line
      width: 100vw !important;
      min-width: inherit;
      overflow: hidden;
      border: none;
      border-top: 1px solid $gray-background;
      border-radius: 5px;
      border-top-left-radius: 0;
      border-top-right-radius: 0;
      box-shadow: 0 8px 16px 0 rgba(#000, 0.1);
      transform: inherit !important;

      &::before,
      &::after {
        display: none;
      }
    }

    &::before,
    &::after {
      position: absolute;
      right: 8px;
      bottom: 100%;
      width: 0;
      height: 0;
      pointer-events: none;
      content: " ";
      border: solid transparent;
    }

    &::after {
      margin-right: 1px;
      border-color: transparent;
      border-width: 7px;
      border-bottom-color: #fff;
    }

    &::before {
      border-color: transparent;
      border-width: 8px;
      border-bottom-color: #bbcdd2;
    }

    .notifications {
      .nav-tabs {
        border-bottom: 1px solid #fff;

        @include media-breakpoint-down(sm) {
          display: flex;

          .nav-item {
            display: flex;
            align-self: stretch;

            a.nav-link {
              display: flex;
              align-items: center;
              align-self: stretch;
              font-size: 16px;
              color: #b3c7cd;
            }
          }
        }

        .nav-item {
          width: 33%;
          margin-bottom: -1px;
          margin-left: 0;
          text-align: center;

          &.active {
            a {
              color: $brand-info;
              border-top: 0;
              border-bottom: 3px solid $brand-info;

              @include media-breakpoint-down(sm) {
                &,
                &:focus,
                &:active {
                  color: $gray-dark-menu;
                  background-color: $input-focus-background;
                }
              }
            }
          }

          &:hover {
            a {
              color: $brand-info;
            }
          }

          .nav-link {
            padding: 0.9375rem 0.9375rem 0.625rem;
            font-family: "Open Sans", sans-serif;
            font-size: 0.9rem;
            font-weight: 400;
            color: #6c868e;
            text-transform: none;
            border: 0;
          }
        }
      }

      .tab-content {
        padding: 0;
        margin-top: -0.0625rem;
        background: #f2f4f5;
        border-top: 0.125rem solid $gray-lighter;

        @include media-breakpoint-down(sm) {
          background: #fff;
          border-top: none;

          .tab-pane.empty.active {
            display: flex;
            flex-direction: column-reverse;
            align-items: center;
            justify-content: center;

            .no-notification {
              // stylelint-disable-next-line
              position: inherit !important;
              font-size: 14px;
              color: $gray-dark-menu;
            }
          }
        }

        .tab-pane {
          height: 11.875rem;
          overflow-y: auto;

          .no-notification {
            display: none;
          }

          &.empty {
            color: #6c868e;
            text-align: center;

            &::after {
              font-family: "Material Icons", sans-serif;
              font-size: 5.625rem;
              color: #bbcdd2;
              content: "\E7F5";
              opacity: 0.7;

              @include media-breakpoint-down(sm) {
                font-size: 25px;
                color: $gray-dark-menu;
                opacity: 1;
              }
            }

            > .no-notification {
              position: absolute;
              bottom: 0;
              display: block;
              width: 100%;
            }
          }
        }

        .notif {
          display: block;
          padding: 0.625rem 0.9375rem;
          color: #6c868e;

          .material-icons {
            font-size: 16px;
            line-height: 16px;
            vertical-align: top;
          }

          .message-notification-status {
            &.open {
              color: #5cb85c;
            }

            &.closed {
              color: #d9534f;
            }

            &.pending1,
            &.pending2 {
              color: #fb0;
            }
          }

          strong {
            color: #363a41;
          }

          &:hover {
            color: #fff;
            text-decoration: none;
            background-color: #3ed2f0;

            strong {
              color: #fff;
            }
          }
        }
      }
    }
  }
}

#employee_infos {
  > a > .material-icons {
    font-size: 22px;
  }
}
