services:
  _defaults:
    public: true

  PrestaShopBundle\Command\Init\ImportCategoryCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\ImportFeatureCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\ImportProductAttributeCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\ImportMaterialCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\ImportProductCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\ImportImageCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\ImportImageS3Command:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\ImportCommentCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\UpdateSizeCodeCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\ImportCommentImageCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\CustomerJsonImportCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\WishlistsJsonImportCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\RuleJsonImportCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\CartJsonImportCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\OrdersJsonImportCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\DeliveryJsonCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\ConfigurationCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'

  PrestaShopBundle\Command\Init\ProductAttributeRedisCommand:
    arguments:
      - '@prestashop.core.command_bus'
      - '@prestashop.adapter.legacy.context'
    tags:
      - 'console.command'