<?php
/**
 * 产品性能监控类
 * 用于监控和优化产品变体处理的性能
 */

class ProductPerformanceMonitor
{
    private static $instance = null;
    private $startTime;
    private $startMemory;
    private $config;
    private $logs = [];

    private function __construct()
    {
        $this->config = include _PS_ROOT_DIR_ . '/config/product_optimization.php';
        $this->startTime = microtime(true);
        $this->startMemory = memory_get_usage(true);
    }

    public static function getInstance()
    {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * 开始监控
     */
    public function start($context = 'default')
    {
        $this->logs[$context] = [
            'start_time' => microtime(true),
            'start_memory' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true),
        ];
    }

    /**
     * 结束监控并记录日志
     */
    public function end($context = 'default', $additionalData = [])
    {
        if (!isset($this->logs[$context])) {
            return;
        }

        $endTime = microtime(true);
        $endMemory = memory_get_usage(true);
        $peakMemory = memory_get_peak_usage(true);

        $this->logs[$context]['end_time'] = $endTime;
        $this->logs[$context]['end_memory'] = $endMemory;
        $this->logs[$context]['peak_memory'] = $peakMemory;
        $this->logs[$context]['execution_time'] = $endTime - $this->logs[$context]['start_time'];
        $this->logs[$context]['memory_used'] = $endMemory - $this->logs[$context]['start_memory'];
        $this->logs[$context]['additional_data'] = $additionalData;

        // 如果启用了性能日志，记录到文件
        if ($this->config['logging']['enable_performance_log']) {
            $this->writeLog($context);
        }
    }

    /**
     * 检查是否应该使用简化处理
     */
    public function shouldUseSimplifiedProcessing($variantCount)
    {
        return $variantCount > $this->config['variants']['max_variants'];
    }

    /**
     * 获取批处理大小
     */
    public function getBatchSize()
    {
        return $this->config['variants']['batch_size'];
    }

    /**
     * 获取垃圾回收频率
     */
    public function getGcFrequency()
    {
        return $this->config['variants']['gc_frequency'];
    }

    /**
     * 设置内存和时间限制
     */
    public function setLimits()
    {
        if (function_exists('ini_set')) {
            $originalLimits = [
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
            ];

            ini_set('memory_limit', $this->config['limits']['memory_limit']);
            ini_set('max_execution_time', $this->config['limits']['max_execution_time']);

            return $originalLimits;
        }
        return null;
    }

    /**
     * 恢复原始限制
     */
    public function restoreLimits($originalLimits)
    {
        if (function_exists('ini_set') && $originalLimits) {
            ini_set('memory_limit', $originalLimits['memory_limit']);
            ini_set('max_execution_time', $originalLimits['max_execution_time']);
        }
    }

    /**
     * 执行垃圾回收
     */
    public function collectGarbage()
    {
        if (function_exists('gc_collect_cycles')) {
            gc_collect_cycles();
        }
    }

    /**
     * 记录大量变体的产品
     */
    public function logLargeVariants($productId, $variantCount)
    {
        if ($this->config['logging']['log_large_variants'] && 
            $variantCount > $this->config['logging']['large_variants_threshold']) {
            
            $logMessage = sprintf(
                "[%s] Product ID: %d has %d variants (threshold: %d)",
                date('Y-m-d H:i:s'),
                $productId,
                $variantCount,
                $this->config['logging']['large_variants_threshold']
            );
            
            error_log($logMessage, 3, _PS_ROOT_DIR_ . '/var/logs/large_variants.log');
        }
    }

    /**
     * 写入性能日志
     */
    private function writeLog($context)
    {
        $log = $this->logs[$context];
        $logMessage = sprintf(
            "[%s] Context: %s | Time: %.4fs | Memory: %s | Peak: %s | Additional: %s\n",
            date('Y-m-d H:i:s'),
            $context,
            $log['execution_time'],
            $this->formatBytes($log['memory_used']),
            $this->formatBytes($log['peak_memory']),
            json_encode($log['additional_data'])
        );

        error_log($logMessage, 3, _PS_ROOT_DIR_ . '/var/logs/product_performance.log');
    }

    /**
     * 格式化字节数
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }

    /**
     * 获取当前性能统计
     */
    public function getCurrentStats()
    {
        return [
            'execution_time' => microtime(true) - $this->startTime,
            'memory_usage' => memory_get_usage(true) - $this->startMemory,
            'peak_memory' => memory_get_peak_usage(true),
            'current_memory' => memory_get_usage(true),
        ];
    }
}
