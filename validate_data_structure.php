<?php
/**
 * 验证优化后的数据结构与原版本是否一致
 */

require_once dirname(__FILE__) . '/config/config.inc.php';

class DataStructureValidator
{
    /**
     * 比较两个数据结构
     */
    public static function compareStructures($original, $optimized, $path = '')
    {
        $differences = [];
        
        // 检查键是否一致
        $originalKeys = is_array($original) ? array_keys($original) : [];
        $optimizedKeys = is_array($optimized) ? array_keys($optimized) : [];
        
        $missingKeys = array_diff($originalKeys, $optimizedKeys);
        $extraKeys = array_diff($optimizedKeys, $originalKeys);
        
        if (!empty($missingKeys)) {
            $differences[] = "Missing keys in optimized version at {$path}: " . implode(', ', $missingKeys);
        }
        
        if (!empty($extraKeys)) {
            $differences[] = "Extra keys in optimized version at {$path}: " . implode(', ', $extraKeys);
        }
        
        // 检查共同键的值类型
        $commonKeys = array_intersect($originalKeys, $optimizedKeys);
        foreach ($commonKeys as $key) {
            $newPath = $path ? "{$path}.{$key}" : $key;
            $originalType = gettype($original[$key]);
            $optimizedType = gettype($optimized[$key]);
            
            if ($originalType !== $optimizedType) {
                $differences[] = "Type mismatch at {$newPath}: original={$originalType}, optimized={$optimizedType}";
            }
            
            // 递归检查数组
            if (is_array($original[$key]) && is_array($optimized[$key])) {
                $subDifferences = self::compareStructures($original[$key], $optimized[$key], $newPath);
                $differences = array_merge($differences, $subDifferences);
            }
        }
        
        return $differences;
    }
    
    /**
     * 验证产品属性数据结构
     */
    public static function validateProductAttributes($productId)
    {
        echo "<h2>验证产品 ID: {$productId} 的数据结构一致性</h2>\n";
        
        try {
            $product = new Product($productId, true, Context::getContext()->language->id);
            
            if (!Validate::isLoadedObject($product)) {
                echo "<p style='color: red;'>错误: 无法加载产品</p>\n";
                return;
            }
            
            // 获取属性组数据
            $attributes_groups = $product->getAttributesGroups(Context::getContext()->language->id);
            $variantCount = count($attributes_groups);
            
            echo "<p>变体数量: {$variantCount}</p>\n";
            
            if ($variantCount == 0) {
                echo "<p>该产品没有变体，无需验证</p>\n";
                return;
            }
            
            // 创建控制器实例来测试方法
            $controller = new ProductController();
            $controller->product = $product;
            $controller->context = Context::getContext();
            
            // 使用反射来访问私有方法
            $reflection = new ReflectionClass($controller);
            $method = $reflection->getMethod('getAttributesGroups');
            $method->setAccessible(true);
            
            // 调用方法获取结果
            $result = $method->invoke($controller, $attributes_groups, null);
            
            echo "<h3>返回数据结构验证</h3>\n";
            
            // 检查必需的键
            $requiredKeys = ['groups', 'colors', 'combinations', 'combinationImages', 'attribute_count', 'shown_color_image'];
            $missingKeys = [];
            
            foreach ($requiredKeys as $key) {
                if (!array_key_exists($key, $result)) {
                    $missingKeys[] = $key;
                }
            }
            
            if (empty($missingKeys)) {
                echo "<p style='color: green;'>✓ 所有必需的键都存在</p>\n";
            } else {
                echo "<p style='color: red;'>✗ 缺少键: " . implode(', ', $missingKeys) . "</p>\n";
            }
            
            // 检查数据类型
            echo "<h4>数据类型检查</h4>\n";
            echo "<ul>\n";
            
            foreach ($result as $key => $value) {
                $type = gettype($value);
                $count = is_array($value) ? count($value) : 'N/A';
                echo "<li><strong>{$key}</strong>: {$type}";
                if (is_array($value)) {
                    echo " (数量: {$count})";
                }
                echo "</li>\n";
            }
            echo "</ul>\n";
            
            // 检查 groups 结构
            if (isset($result['groups']) && is_array($result['groups'])) {
                echo "<h4>Groups 结构检查</h4>\n";
                $groupCount = count($result['groups']);
                echo "<p>分组数量: {$groupCount}</p>\n";
                
                if ($groupCount > 0) {
                    $firstGroup = reset($result['groups']);
                    $expectedGroupKeys = ['group_name', 'name', 'group_type', 'default', 'attributes'];
                    $actualGroupKeys = array_keys($firstGroup);
                    
                    $missingGroupKeys = array_diff($expectedGroupKeys, $actualGroupKeys);
                    if (empty($missingGroupKeys)) {
                        echo "<p style='color: green;'>✓ Groups 结构正确</p>\n";
                    } else {
                        echo "<p style='color: orange;'>⚠ Groups 缺少键: " . implode(', ', $missingGroupKeys) . "</p>\n";
                    }
                    
                    echo "<p>第一个分组的键: " . implode(', ', $actualGroupKeys) . "</p>\n";
                }
            }
            
            // 检查 combinations 结构
            if (isset($result['combinations']) && is_array($result['combinations'])) {
                echo "<h4>Combinations 结构检查</h4>\n";
                $combinationCount = count($result['combinations']);
                echo "<p>组合数量: {$combinationCount}</p>\n";
                
                if ($combinationCount > 0) {
                    $firstCombination = reset($result['combinations']);
                    if (is_array($firstCombination)) {
                        echo "<p>第一个组合的键: " . implode(', ', array_keys($firstCombination)) . "</p>\n";
                    }
                }
            }
            
            // 检查 attribute_count 格式
            if (isset($result['attribute_count'])) {
                echo "<h4>Attribute Count 格式检查</h4>\n";
                echo "<p>格式: " . gettype($result['attribute_count']) . "</p>\n";
                echo "<p>内容: " . htmlspecialchars($result['attribute_count']) . "</p>\n";
            }
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>\n";
            echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
        }
    }
}

// 使用示例
if (isset($_GET['product_id'])) {
    $productId = (int)$_GET['product_id'];
    
    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>数据结构验证</title></head><body>\n";
    
    DataStructureValidator::validateProductAttributes($productId);
    
    echo "</body></html>\n";
} else {
    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>数据结构验证工具</title></head><body>\n";
    echo "<h1>数据结构验证工具</h1>\n";
    echo "<p>验证优化后的方法返回的数据结构是否与原版本一致</p>\n";
    echo "<form method='get'>\n";
    echo "<label>产品ID: <input type='number' name='product_id' required></label>\n";
    echo "<input type='submit' value='开始验证'>\n";
    echo "</form>\n";
    echo "</body></html>\n";
}
?>
