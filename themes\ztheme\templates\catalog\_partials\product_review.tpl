{foreach from=$comments item=comment}
    {if $comment.content}
        <div class="comment clearfix jscommentiterm" style="margin: 0 0 10px 0;" data-name="{$comment.customer_name}"
            data-url="{$comment.product_url}" data-text="{$comment.content|escape:'html':'UTF-8'|nl2br nofilter}"
            data-img="{$comment.product_image}">
            <div class="comment_author">
                <div class="commentItem"
                    style="display: flex;width: 100%;border-bottom: 1px solid #ccc;margin-bottom: 10px;padding-bottom: 10px;position: relative;">
                    {* 星级 评论人 属国*}
                    <div class="customer_photo">{* 头像 *}
                    </div>
                    <span class="icon-savedui">
                        <svg t="1693548825988" class="icon" viewBox="0 0 1024 1024" version="1.1"
                            xmlns="http://www.w3.org/2000/svg" p-id="4829" width="16" height="16">
                            <path
                                d="M882.432387 204.696792c18.921552-21.157736 51.260205-22.705863 72.245926-3.440283 20.813707 19.26558 22.361834 52.120275 3.440283 73.278011L435.711742 856.458256a50.656435 50.656435 0 0 1-74.48211 1.204099l-295.348228-307.905258c-19.609609-20.469679-19.26558-53.324374 1.032085-73.278011 20.297665-19.953637 52.636318-19.437594 72.245926 1.032085l257.505124 268.514026L882.432387 204.696792z m0 0"
                                p-id="4830"></path>
                        </svg>
                    </span>
                    <div style="display: flex;flex-direction:column;margin-left: 1rem;padding-top:5px">
                        <strong>{$comment.customer_name|escape:'html':'UTF-8'}</strong>
                        <label style="text-align: left;margin-bottom: 0px;">
                            {l s="Vérifié" mod='boncomments'}
                            {* 国家 *}
                            {if $comment.country_iso}
                                <svg t="1693538422217" class="icon icon-city" viewBox="0 0 1024 1024" version="1.1"
                                    xmlns="http://www.w3.org/2000/svg" p-id="4094" width="16" height="16">
                                    <path
                                        d="M115.264 422.656C112.576 241.984 223.488 80.64 388.352 25.216c185.024-62.336 367.616-5.568 482.24 149.504 104.128 141.056 105.728 348.032-0.896 489.408-78.528 103.872-161.28 204.608-244.352 304.64-62.72 75.264-130.688 72.64-192.64-4.352-76.096-94.656-153.152-188.48-226.816-284.992C148.672 604.352 112.64 519.808 115.264 422.656zM168.448 422.592C161.28 492.864 189.184 563.776 233.92 624.384c79.552 108.224 165.568 211.904 250.112 316.48 33.472 41.6 69.568 32.512 99.84-4.608 81.728-101.248 166.976-200 241.984-305.728 99.52-140.608 92.48-307.776-7.872-437.12-94.848-122.048-257.152-170.624-403.392-120.64C266.432 123.456 168.448 259.072 168.448 422.592z"
                                        fill="#272636" p-id="4095"></path>
                                    <path
                                        d="M351.552 416.96c0-101.824 73.792-178.688 172.032-179.2 108.032-0.512 190.144 75.648 190.784 176.96 0.64 102.912-83.136 186.688-184.96 185.344C427.584 598.4 351.744 520.384 351.552 416.96zM532.352 290.688C461.696 289.856 407.296 342.72 405.376 413.888c-1.92 73.344 53.696 131.328 126.592 131.84 69.504 0.384 126.144-54.912 127.424-124.672C660.864 346.624 607.104 291.52 532.352 290.688z"
                                        fill="#272636" p-id="4096"></path>
                                </svg>
                                {$comment.country_iso}
                            {/if}
                        </label>
                    </div>
                </div>
                {* 属性 *}
                {if $comment.attributes}
                    <div class="colerSize">
                        {foreach from=$comment.attributes key=key item=attribute}
                            <label style="margin-right: 10px;text-transform: capitalize;">{$key}：{$attribute}</label>
                        {/foreach}
                    </div>
                {/if}
                <div style="display: flex;margin-top: 10px;">
                    {* 星级 *}
                    <div class="star_content clearfix" style="margin-top:2px ;margin-bottom: 15px;">
                        {section name="i" start=0 loop=5 step=1}
                            {if $comment.grade le $smarty.section.i.index}
                                <div class="star"></div>
                            {else}
                                <div class="star star_on"></div>
                            {/if}
                        {/section}
                    </div>
                    {* 是否合适 *}
                    {if $comment.is_fit !== ''}
                        <div class="fit" style="height: 20px; line-height: 10px;margin-top: 6px;margin-left: 10px;">
                            <span class="text">
                                <span style="margin-right: 3px;">{l s="Ajuster" mod='boncomments'}:</span>
                                {if $comment.is_fit}
                                    {l s="Oui" mod='boncomments'}
                                {else}
                                    {l s="Non" mod='boncomments'}
                                {/if}
                            </span>
                        </div>
                    {/if}
                </div>
            </div>
            <div class="comment_biaoti">
                <label style="font-weight: 600;">{$comment.title}</label>
            </div>
            <div class="comment_details" style="width: 100%;">{* background: tan;评论内容 *}
                {* <h4 class="title_block"></h4> *}
                <p>{$comment.content|escape:'html':'UTF-8'|nl2br nofilter}</p>
                {if _PS_COUNTRY_FANYI_}
                    <div class="translate">
                        <a href="javascript:void(0);" onclick="reviewln({$comment.id_product_comment});" style="color: #FF7176;">
                            <svg t="1683602829356" class="translate-icon" viewBox="0 0 1024 1024" version="1.1"
                                xmlns="http://www.w3.org/2000/svg" p-id="3967" width="12" height="12">
                                <path
                                    d="M99.119848 428.002088h828.014506a43.350032 43.350032 0 0 0 43.350032-43.350031 43.248882 43.248882 0 0 0-16.451337-33.957525 43.465632 43.465632 0 0 0-9.219107-8.756707L635.50424 124.869766a43.350032 43.350032 0 1 0-49.809186 70.964002l207.270952 145.461032H99.119848a43.350032 43.350032 0 0 0 0 86.707288zM927.134354 601.07709H99.119848a43.350032 43.350032 0 0 0-43.350032 43.350032 43.205532 43.205532 0 0 0 16.451337 33.9503c2.564877 3.272927 5.635504 6.242405 9.219107 8.756706l309.309701 217.075284a43.313907 43.313907 0 0 0 60.386594-10.584632 43.350032 43.350032 0 0 0-10.577407-60.386595L233.288196 687.777154h693.846158a43.350032 43.350032 0 0 0 0-86.700064z"
                                    fill="#FF7176" p-id="3968">
                                </path>
                            </svg>
                            {l s='Traduire' mod='boncomments'}
                        </a>
                    </div>
                    <div class="translate-box">
                        <div class="translate-close" onclick="closereviewln({$comment.id_product_comment});"></div>
                        <div class="translate-content reviewdata-{$comment.id_product_comment}"></div>
                        <div class="translate-footer">
                            <span class="comment-target-language-name">{l s='néerlandais' mod='boncomments'}</span>
                            <div class="translate-by">
                                <span class="translate-by-google">{l s='Traduit par' mod='boncomments'}</span>
                                <img class="translate-google-img" src="/themes/ztheme/assets/images/translate_google.svg">
                            </div>
                        </div>
                    </div>
                {/if}
                {* 评论图片 *}
                {if $comment.images}
                    <div class="comment_img" style="margin-bottom: 12px;">
                        {foreach from=$comment.images item=image}
                            <img class="comment-modal-img" data-bgsrc="{$image.image_path}" src="{$image.image_thumb_path}" alt=""
                                style="width: 72px;height: 96px;margin-right: 6px;">
                        {/foreach}
                    </div>
                {/if}
                <div class="comment_author_infos">
                    <span class="text" style="font-weight: bold;">{l s="Date d'achat :" mod='boncomments'}</span>
                    <em>{$comment.date_add|date_format}</em>
                </div>
                <ul>{* 底部图标 *}
                    <div class="review_sharelinks">
                        <div class="review_useful">
                            <a class="icon_useful">
                                <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                    xmlns="http://www.w3.org/2000/svg" width="15px" height="15px">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M7.94.94A1.5 1.5 0 0 1 10.5 2a20.774 20.774 0 0 1-.384 4H14.5A1.5 1.5 0 0 1 16 7.5v.066l-1.845 6.9-.094.095A1.5 1.5 0 0 1 13 15H9c-.32 0-.685-.078-1.038-.174-.357-.097-.743-.226-1.112-.349l-.008-.003c-.378-.126-.74-.246-1.067-.335C5.44 14.047 5.18 14 5 14v.941l-5 .625V6h5v.788c.913-.4 1.524-1.357 1.926-2.418A10.169 10.169 0 0 0 7.5 1.973 1.5 1.5 0 0 1 7.94.939ZM8 2l.498.045v.006l-.002.013a4.507 4.507 0 0 1-.026.217 11.166 11.166 0 0 1-.609 2.443C7.396 5.951 6.541 7.404 5 7.851V13c.32 0 .685.078 1.038.174.357.097.743.226 1.112.349l.008.003c.378.126.74.246 1.067.335.335.092.594.139.775.139h4a.5.5 0 0 0 .265-.076l1.732-6.479A.5.5 0 0 0 14.5 7H8.874l.138-.61c.326-1.44.49-2.913.488-4.39a.5.5 0 0 0-1 0v.023l-.002.022L8 2ZM4 7H1v7.434l3-.375V7Zm-1.5 5.75a.25.25 0 1 0 0-.5.25.25 0 0 0 0 .5Zm-.75-.25a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0Z">
                                    </path>
                                </svg>
                                <span>Utile</span>
                            </a>
                        </div>
                        <div class="review_share">
                            <a href="https://www.facebook.com/sharer/sharer.php?u={$comment.product_url}">
                                <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                    xmlns="http://www.w3.org/2000/svg" width="14px" height="14px">
                                    <path fill-rule="evenodd" clip-rule="evenodd"
                                        d="M13 1a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm-3 2a3 3 0 1 1 .583 1.778L5.867 7.115a3 3 0 0 1 0 1.77l4.716 2.337a3 3 0 1 1-.45.893L5.417 9.778a3 3 0 1 1 0-3.556l4.716-2.337A3.002 3.002 0 0 1 10 3ZM1 8a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm10 5a2 2 0 1 1 4 0 2 2 0 0 1-4 0Z">
                                    </path>
                                </svg>
                                <span>Partager</span>
                            </a>
                        </div>
                    </div>
                </ul>
            </div>
            <br />
        </div>
    {/if}
{/foreach}