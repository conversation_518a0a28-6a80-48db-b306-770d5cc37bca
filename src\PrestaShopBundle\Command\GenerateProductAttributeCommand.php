<?php
/**
 * 同步评论
 */

namespace PrestaShopBundle\Command;

use PrestaShop\PrestaShop\Adapter\LegacyContext;
use PrestaShop\PrestaShop\Core\CommandBus\CommandBusInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Db;
use Tools;
use ImportProduct;

class GenerateProductAttributeCommand extends Command
{
    /**
     * @var CommandBusInterface
     */
    private $commandBus;

    /**
     * @var LegacyContext
     */
    private $legacyContext;

    public function __construct(CommandBusInterface $commandBus, LegacyContext $legacyContext)
    {
        parent::__construct();
        $this->commandBus = $commandBus;
        $this->legacyContext = $legacyContext;
    }

    protected function configure()
    {
        // The name of the command (the part after "bin/console")
        $this->setName('prestashop:generate-product-attribute');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Tools::unlimit();

        // 需要执行的产品
        $id_products = Db::getInstance()->executeS('SELECT id_product FROM `' . _DB_PREFIX_ . 'product` WHERE is_update_attribute = 1');
        foreach ($id_products as $product) {
            $id_product = $product['id_product'];
            try {
                Db::getInstance()->execute('BEGIN');
                // 获取产品属性
                $has_attributes = Db::getInstance()->executeS('SELECT * FROM `' . _DB_PREFIX_ . 'product_has_attribute` WHERE id_product = ' . $id_product);
                // 整理
                $attributes = [];
                // 价格
                $attribute_prices = [];
                foreach ($has_attributes as $has_attribute) {
                    $attributes[$has_attribute['id_attribute_group']][$has_attribute['id_attribute']] = $has_attribute['id_attribute'];
                    $attribute_prices[$has_attribute['id_attribute']] = $has_attribute['price'];
                }
                ksort($attributes);
                // 组合
                $com_attributes = Tools::combinationArrays($attributes);
                // 已存在的变体
                $exist_product_attributes = Db::getInstance()->executeS('SELECT pac.`id_product_attribute`, pac.`id_attribute`, a.`id_attribute_group`
                    FROM `' . _DB_PREFIX_ . 'product_attribute` pa
                    LEFT JOIN `' . _DB_PREFIX_ . 'product_attribute_combination` pac ON (pa.`id_product_attribute` = pac.`id_product_attribute`)
                    LEFT JOIN `' . _DB_PREFIX_ . 'attribute` a ON (a.`id_attribute` = pac.`id_attribute`)
                    LEFT JOIN `' . _DB_PREFIX_ . 'attribute_group` ag ON (ag.`id_attribute_group` = a.`id_attribute_group`)
                    WHERE pa.`id_product` = ' . $id_product);
                if ($exist_product_attributes) {
                    // 存在的属性值根据变体来分组，用于获得属性组合对应的变体ID
                    $exist_pa_com_attributes = [];
                    foreach ($exist_product_attributes as $exist_product_attribute) {
                        // 存在的属性值根据变体来分组
                        $exist_pa_com_attributes[$exist_product_attribute['id_product_attribute']][$exist_product_attribute['id_attribute_group']] = $exist_product_attribute['id_attribute'];
                    }
                    // 属性组合对应的变体ID
                    $exist_implode_com_attribute_to_pa = [];
                    foreach ($exist_pa_com_attributes as $id_product_attribute => $exist_com_attributes) {
                        // 排序，保证组合属性顺序一致
                        ksort($exist_com_attributes);
                        // 组合属性对应的变体ID
                        $exist_implode_com_attribute_to_pa[implode('-', $exist_com_attributes)] = $id_product_attribute;
                    }
                    // 需要上架的变体ID
                    $put_on_pa_ids = [];
                    // 获取需要新增的变体属性组合
                    $add_com_attributes = [];
                    // 循环筛选需要上架的变体/需要添加属性的变体/需要新增成变体的组合
                    foreach ($com_attributes as $newAttributeCombinationRow) {
                        // 判断组合属性对应的变体是否存在
                        $combinationKey = implode('-', $newAttributeCombinationRow);
                        if (isset($exist_implode_com_attribute_to_pa[$combinationKey])) {
                            // 存在将变体添加到产品属性中
                            $put_on_pa_ids[] = $exist_implode_com_attribute_to_pa[$combinationKey];
                        } else {
                            // 不存在, 记录组合进行新增
                            $add_com_attributes[] = $newAttributeCombinationRow;
                        }
                    }

                    // 所有存在的变体ID
                    $exist_product_attribute_ids = array_column($exist_product_attributes, 'id_product_attribute', 'id_product_attribute');
                    // 先下架所有变体
                    $this->putOffProductAttribute($id_product, $exist_product_attribute_ids);

                    // 上架变体
                    if (!empty($put_on_pa_ids)) {
                        // 存在需要上架的变体，直接上架
                        $this->putOnProductAttribute($put_on_pa_ids, $exist_pa_com_attributes, $attribute_prices);
                    }

                    // 需要新增成变体的组合
                    if ($add_com_attributes) {
                        ImportProduct::batchInsertProductAttribute($id_product, $add_com_attributes, $attribute_prices, 1, $this->legacyContext->getLanguages(), $exist_product_attribute_ids);
                    }
                } else {
                    // 没有变体，直接新增
                    ImportProduct::batchInsertProductAttribute($id_product, $com_attributes, $attribute_prices, 1, $this->legacyContext->getLanguages());
                }
                Db::getInstance()->execute('UPDATE ' . _DB_PREFIX_ . 'product SET is_update_attribute = 0 WHERE id_product = ' . $id_product);

                // 同步产品的颜色对应图片
                $this->setColorImage($id_product);
                // 清除搜索缓存
                Tools::clearProductSearchRedis([$id_product]);
                Db::getInstance()->execute('COMMIT');
            } catch (\Throwable $e) {
                Db::getInstance()->execute('ROLLBACK');
                Db::getInstance()->execute('UPDATE ' . _DB_PREFIX_ . 'product SET is_update_attribute = 2 WHERE id_product = ' . $id_product);
                echo $e->getMessage();
                $this->saveLog( $e->getMessage(), $id_product);
                continue;
            }
            // 设置默认变体
            $this->setDefaultProductAttribute($id_product);
            // 更新选项卡的redis
            ImportProduct::updateQueryProductHasAttributeRedis($id_product);
        }

        echo 'end';
        die;
    }

    // 设置默认变体
    public function setDefaultProductAttribute($id_product) {
        $id_attribute = Db::getInstance()->executeS(
            'SELECT id_attribute FROM `' . _DB_PREFIX_ . 'product_has_attribute`
            WHERE `id_attribute_group` = 2
            AND `id_product` = ' . $id_product .
            ' ORDER BY `sort` ASC');
        $id_product_attribute = Db::getInstance()->getValue(
            'SELECT pa.`id_product_attribute` FROM `' . _DB_PREFIX_ . 'product_attribute_combination` pac
            INNER JOIN `' . _DB_PREFIX_ . 'product_attribute` pa ON pac.`id_product_attribute` = pa.`id_product_attribute`
            WHERE pa.`id_product` = ' . (int) $id_product . '
            AND pac.`id_attribute` = ' . (int) $id_attribute
        );
        Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'product_attribute` SET `default_on` = NULL WHERE `id_product` = ' . (int) $id_product);
        Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'product_attribute_shop` SET `default_on` = NULL WHERE `id_product` = ' . (int) $id_product);
        Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'product_attribute` SET `default_on` = 1 WHERE `id_product_attribute` = ' . (int) $id_product_attribute);
        Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'product_attribute_shop` SET `default_on` = 1 WHERE `id_product_attribute` = ' . (int) $id_product_attribute);
    }

    /**
     * 上架变体
     * @param mixed $id_product_attributes
     * @param mixed $product_attribute_to_attribute_ids
     * @param mixed $attributePrices
     * @return bool
     */
    protected function putOnProductAttribute($id_product_attributes, $product_attribute_to_attribute_ids, $attributePrices)
    {
        $new_product_attribute_to_prices = []; // 变体的新价格
        foreach ($id_product_attributes as $id_product_attribute) {
            $attribute_ids = $product_attribute_to_attribute_ids[$id_product_attribute] ?? [];
            $price = 0;
            foreach ($attribute_ids as $id_attribute) {
                $price += $attributePrices[$id_attribute] ?? 0;
            }
            $new_product_attribute_to_prices[$id_product_attribute] = $price;
        }
        // 循环更新价格
        $update_price_sqls = [];
        foreach ($new_product_attribute_to_prices as $id_product_attribute => $price) {
            $update_price_sqls[] = 'WHEN ' . $id_product_attribute . ' THEN ' . (float) $price;
        }

        // 批量更新价格和状态
        $res = Db::getInstance()->execute(
            'UPDATE `' . _DB_PREFIX_ . 'product_attribute`
                SET `price` = CASE `id_product_attribute`' . implode(' ', $update_price_sqls) . ' END
                WHERE `id_product_attribute` IN (' . implode(',', $id_product_attributes) . ')'
        )
            && Db::getInstance()->execute(
                'UPDATE `' . _DB_PREFIX_ . 'product_attribute_shop`
                    SET `price` = CASE `id_product_attribute`' . implode(' ', $update_price_sqls) . ' END
                    WHERE `id_product_attribute` IN (' . implode(',', $id_product_attributes) . ')'
            )
            && Db::getInstance()->execute(
                'UPDATE `' . _DB_PREFIX_ . 'product_attribute`
                    SET `active` = 1
                    WHERE `id_product_attribute` IN (' . implode(',', $id_product_attributes) . ')'
            );
        return $res;
    }

    // 下架变体
    protected function putOffProductAttribute($id_product, $id_product_attributes)
    {
        try {
            Db::getInstance()->update('product_attribute', ['active' => 0], 'id_product_attribute IN (' . implode(',', $id_product_attributes) . ')');
            Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'product_attribute` SET `default_on` = NULL WHERE `id_product_attribute` IN (' . implode(',', $id_product_attributes) . ')');
            Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'product_attribute_shop` SET `default_on` = NULL WHERE `id_product_attribute` IN (' . implode(',', $id_product_attributes) . ')');
            if (!Db::getInstance()->getRow('SELECT * FROM ' . _DB_PREFIX_ . 'product_attribute WHERE id_product = ' . $id_product . ' AND active = 1 AND default_on = 1')) {
                $firstActiveProductAttributeId = Db::getInstance()->getValue('SELECT `id_product_attribute` FROM `' . _DB_PREFIX_ . 'product_attribute` WHERE `id_product` = ' . $id_product . ' AND `active` = 1');
                if ($firstActiveProductAttributeId) {
                    Db::getInstance()->update('product_attribute', ['default_on' => 1], 'id_product_attribute = ' . $firstActiveProductAttributeId);
                    Db::getInstance()->update('product_attribute_shop', ['default_on' => 1], 'id_product_attribute = ' . $firstActiveProductAttributeId);
                }
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }

        return true;
    }

    /**
     * 同步颜色图片
     *
     * @param int $id_product
     */
    protected function setColorImage($id_product)
    {
        // 查找所有对应产品有颜色对应的图片
        $images = Db::getInstance()->executeS(
            'SELECT `id_image`, `id_color_attribute` FROM `' . _DB_PREFIX_ . 'image` WHERE `id_product` = ' . (int) $id_product . ' AND `id_color_attribute` <> 0'
        );
        if ($images) {
            try {
                foreach ($images as $image) {
                    // 删除变体图片关联
                    Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product_attribute_image` WHERE  id_image = ' . $image['id_image']);

                    // 该属性的变体
                    $id_product_attributes = Db::getInstance()->executeS(
                        'SELECT pac.id_product_attribute FROM ps_product_attribute_combination pac
                        LEFT JOIN ps_product_attribute pa ON pac.id_product_attribute = pa.id_product_attribute
                        WHERE pa.id_product = ' . (int) $id_product . '
                        AND pac.id_attribute = ' . (int) $image['id_color_attribute']);
                    $id_product_attributes = array_column($id_product_attributes, 'id_product_attribute');
                    if ($id_product_attributes) {
                        // 删除变体图片关联
                        Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product_attribute_image`
                        WHERE  id_product_attribute IN (' . implode(',', $id_product_attributes) . ')');

                        // 新增关联
                        $insert_sql = 'INSERT INTO `ps_product_attribute_image` (`id_product_attribute`, `id_image`) VALUES ';
                        foreach ($id_product_attributes as $id_product_attribute) {
                            $insert_sql .= '(' . (int) $id_product_attribute . ', ' . (int) $image['id_image'] . '), ';
                        }
                        $insert_sql = rtrim($insert_sql, ', ');
                        Db::getInstance()->execute($insert_sql);
                    }
                }
            } catch (Exception $e) {
                throw new Exception($e->getMessage());
            }
        }
    }

    /**
     * Summary of savelog
     * @param mixed $context
     * @return bool|int
     */
    private function saveLog($context, $log_name = 'log') {
        if (!is_dir(_PS_UPLOAD_DIR_ . 'log/command/generate-product-attribute/')) {
            mkdir(_PS_UPLOAD_DIR_ . 'log/command/generate-product-attribute/' , 0777, true);
        }
        // Log file
        $log_file = _PS_UPLOAD_DIR_ . 'log/command/generate-product-attribute/log-' . $log_name . '.log';

        return file_put_contents($log_file, $context . "\n", FILE_APPEND);
    }
}