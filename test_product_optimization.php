<?php
/**
 * 产品变体优化测试脚本
 * 用于测试优化后的性能表现
 */

// 包含必要的文件
require_once dirname(__FILE__) . '/config/config.inc.php';
require_once dirname(__FILE__) . '/classes/ProductPerformanceMonitor.php';

class ProductOptimizationTest
{
    private $monitor;
    
    public function __construct()
    {
        $this->monitor = ProductPerformanceMonitor::getInstance();
    }
    
    /**
     * 测试产品变体处理性能
     */
    public function testProductVariants($productId)
    {
        echo "开始测试产品 ID: {$productId}\n";
        
        $this->monitor->start('product_test');
        
        try {
            // 获取产品对象
            $product = new Product($productId, true, Context::getContext()->language->id);
            
            if (!Validate::isLoadedObject($product)) {
                echo "错误: 无法加载产品 ID {$productId}\n";
                return false;
            }
            
            // 获取变体数据
            $attributes_groups = $product->getAttributesGroups(Context::getContext()->language->id);
            $variantCount = count($attributes_groups);
            
            echo "变体数量: {$variantCount}\n";
            
            // 记录大量变体的产品
            $this->monitor->logLargeVariants($productId, $variantCount);
            
            // 检查是否应该使用简化处理
            $useSimplified = $this->monitor->shouldUseSimplifiedProcessing($variantCount);
            echo "使用简化处理: " . ($useSimplified ? '是' : '否') . "\n";
            
            // 设置性能限制
            $originalLimits = $this->monitor->setLimits();
            
            // 模拟处理过程
            $this->simulateProcessing($attributes_groups);
            
            // 恢复限制
            $this->monitor->restoreLimits($originalLimits);
            
            // 结束监控
            $this->monitor->end('product_test', [
                'product_id' => $productId,
                'variant_count' => $variantCount,
                'use_simplified' => $useSimplified
            ]);
            
            // 显示性能统计
            $this->displayStats();
            
            return true;
            
        } catch (Exception $e) {
            echo "错误: " . $e->getMessage() . "\n";
            return false;
        }
    }
    
    /**
     * 模拟变体处理过程
     */
    private function simulateProcessing($attributes_groups)
    {
        $variantCount = count($attributes_groups);
        $batchSize = $this->monitor->getBatchSize();
        $gcFrequency = $this->monitor->getGcFrequency();
        
        echo "批处理大小: {$batchSize}\n";
        echo "垃圾回收频率: 每 {$gcFrequency} 批\n";
        
        $totalBatches = ceil($variantCount / $batchSize);
        echo "总批次数: {$totalBatches}\n";
        
        for ($batch = 0; $batch < $totalBatches; $batch++) {
            $batchStart = $batch * $batchSize;
            $batchEnd = min($batchStart + $batchSize, $variantCount);
            $currentBatchSize = $batchEnd - $batchStart;
            
            echo "处理批次 " . ($batch + 1) . "/{$totalBatches} (变体 {$batchStart}-{$batchEnd}, 共 {$currentBatchSize} 个)\n";
            
            // 模拟处理时间
            usleep(100000); // 0.1秒
            
            // 定期执行垃圾回收
            if ($batch > 0 && $batch % $gcFrequency === 0) {
                echo "执行垃圾回收...\n";
                $this->monitor->collectGarbage();
            }
        }
    }
    
    /**
     * 显示性能统计
     */
    private function displayStats()
    {
        $stats = $this->monitor->getCurrentStats();
        
        echo "\n=== 性能统计 ===\n";
        echo "执行时间: " . round($stats['execution_time'], 4) . " 秒\n";
        echo "内存使用: " . $this->formatBytes($stats['memory_usage']) . "\n";
        echo "峰值内存: " . $this->formatBytes($stats['peak_memory']) . "\n";
        echo "当前内存: " . $this->formatBytes($stats['current_memory']) . "\n";
        echo "================\n\n";
    }
    
    /**
     * 格式化字节数
     */
    private function formatBytes($bytes)
    {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        $bytes /= pow(1024, $pow);
        return round($bytes, 2) . ' ' . $units[$pow];
    }
    
    /**
     * 批量测试多个产品
     */
    public function batchTest($productIds)
    {
        echo "开始批量测试 " . count($productIds) . " 个产品\n\n";
        
        $results = [];
        foreach ($productIds as $productId) {
            $result = $this->testProductVariants($productId);
            $results[$productId] = $result;
            echo str_repeat('-', 50) . "\n";
        }
        
        echo "\n=== 批量测试结果 ===\n";
        foreach ($results as $productId => $success) {
            echo "产品 {$productId}: " . ($success ? '成功' : '失败') . "\n";
        }
        echo "==================\n";
        
        return $results;
    }
}

// 使用示例
if (php_sapi_name() === 'cli') {
    // 命令行模式
    $productId = isset($argv[1]) ? (int)$argv[1] : null;
    
    if (!$productId) {
        echo "用法: php test_product_optimization.php <product_id>\n";
        echo "示例: php test_product_optimization.php 123\n";
        exit(1);
    }
    
    $test = new ProductOptimizationTest();
    $test->testProductVariants($productId);
    
} else {
    // Web模式
    $productId = isset($_GET['product_id']) ? (int)$_GET['product_id'] : null;
    
    if (!$productId) {
        echo "<h1>产品变体优化测试</h1>";
        echo "<p>请在URL中添加 ?product_id=123 来测试指定产品</p>";
        echo "<p>示例: test_product_optimization.php?product_id=123</p>";
        exit;
    }
    
    echo "<h1>产品变体优化测试</h1>";
    echo "<pre>";
    
    $test = new ProductOptimizationTest();
    $test->testProductVariants($productId);
    
    echo "</pre>";
}
