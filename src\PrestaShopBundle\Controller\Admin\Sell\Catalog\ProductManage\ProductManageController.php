<?php

declare(strict_types=1);

namespace PrestaShopBundle\Controller\Admin\Sell\Catalog\ProductManage;

use Configuration;
use Context;
use Db;
use ImportProduct;
use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Product;
use ProductCore;
use Symfony\Component\HttpFoundation\Request;
use Tools;
use Shop;
use Symfony\Component\HttpFoundation\Response;
use Exception;

class ProductManageController extends FrameworkBundleAdminController
{
    // 首页
    public function indexAction(Request $request)
    {
        $data = $_GET;
        // 查询数据
        $sql = 'SELECT p.*, pl.name, i.id_image, i.type as image_type, i.name as image_name' .
            ((isset($data['id_category']) && $data['id_category']) ? ', cl.name as category' : '') .
            ' FROM `' . _DB_PREFIX_ . 'product` p
        LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl ON (p.id_product = pl.id_product AND pl.id_lang = 1)
        LEFT JOIN `' . _DB_PREFIX_ . 'image` i ON (p.id_product = i.id_product AND i.cover = 1)' .
            ((isset($data['id_category']) && $data['id_category']) ?
                ' LEFT JOIN `' . _DB_PREFIX_ . 'category_product` cp ON (p.id_product = cp.id_product)
        LEFT JOIN `' . _DB_PREFIX_ . 'category_lang` cl ON (cp.id_category = cl.id_category AND cl.id_lang = 1)' : '');
        // 查询总数
        $count_sql = 'SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'product` p
        LEFT JOIN `' . _DB_PREFIX_ . 'product_lang` pl ON (p.id_product = pl.id_product AND pl.id_lang = 1)
        LEFT JOIN `' . _DB_PREFIX_ . 'image` i ON (p.id_product = i.id_product AND i.cover = 1)' .
            ((isset($data['id_category']) && $data['id_category']) ?
                ' LEFT JOIN `' . _DB_PREFIX_ . 'category_product` cp ON (p.id_product = cp.id_product)
        LEFT JOIN `' . _DB_PREFIX_ . 'category_lang` cl ON (cp.id_category = cl.id_category AND cl.id_lang = 1)' : '');
        // 筛选
        $where = ' WHERE 1=1';
        if (isset($data['min_id']) && $data['min_id']) {
            // 最小ID
            $where .= ' AND p.id_product >= ' . $data['min_id'];
        }
        if (isset($data['max_id']) && $data['max_id']) {
            // 最大ID
            $where .= ' AND p.id_product <= ' . $data['max_id'];
        }
        if (isset($data['name']) && $data['name']) {
            // 产品名称
            $where .= ' AND pl.name LIKE "%' . trim(preg_replace('/\s+/', ' ', $data['name'])) . '%"';
        }
        if (isset($data['supplier_reference']) && $data['supplier_reference']) {
            // sku
            $where .= ' AND p.supplier_reference LIKE "%' . trim(preg_replace('/\s+/', ' ', $data['supplier_reference'])) . '%"';
        }
        if (isset($data['id_category']) && $data['id_category']) {
            // 主分类名
            $where .= ' AND cp.id_category = ' . $data['id_category'];
        }
        if (isset($data['attribute_set']) && $data['attribute_set']) {
            // 属性集
            $where .= ' AND p.attribute_set = "' . $data['attribute_set'] . '"';
        }
        if (isset($data['min_price']) && $data['min_price']) {
            // 最小价格
            $where .= ' AND p.price >= ' . (float) $data['min_price'];
        }
        if (isset($data['max_price']) && $data['max_price']) {
            // 最大价格
            $where .= ' AND p.price <= ' . (float) $data['max_price'];
        }
        if (isset($data['min_current_price']) && $data['min_current_price']) {
            // 最小当前价格
            $where .= ' AND p.current_price >= ' . (float) $data['min_current_price'];
        }
        if (isset($data['max_current_price']) && $data['max_current_price']) {
            // 最大当前价格
            $where .= ' AND p.current_price <= ' . (float) $data['max_current_price'];
        }
        if (isset($data['active']) && $data['active'] != '') {
            // 是否上架
            $where .= ' AND p.active = ' . (int) $data['active'];
        }
        $count_sql .= $where;
        $total = Db::getInstance()->getValue($count_sql);
        // 分页
        $page = (int) ($data['page'] ?? 1);
        $limit = (int) ($data['limit'] ?? 20);
        $offset = (int) (($page - 1) * $limit);

        $sql .= $where . $this->getOrderBy($data, ' ORDER BY p.id_product DESC', 'p') . ' LIMIT ' . $offset . ',' . $limit;
        $products = Db::getInstance()->executeS($sql);
        foreach ($products as &$product) {
            $product['image'] = Context::getContext()->link->getImageLink('image', $product['id_image'], 'small_default', $product['image_type'], $product['image_name']);
            $product['price'] = Tools::displayPrice(number_format((float) $product['price'], 2, '.', ''));
            $product['current_price'] = Tools::displayPrice(number_format((float) $product['current_price'], 2, '.', ''));
        }

        // 所有的属性集
        $attribute_sets = Db::getInstance()->executeS('SELECT attribute_set FROM `' . _DB_PREFIX_ . 'product` GROUP BY attribute_set');
        $attribute_sets = array_column($attribute_sets, 'attribute_set');

        // 所有的分类
        $categories = Db::getInstance()->executeS(
            'SELECT c.id_category, cl.name FROM `' . _DB_PREFIX_ . 'category` c
            LEFT JOIN `' . _DB_PREFIX_ . 'category_lang` cl
            ON (c.id_category = cl.id_category AND cl.id_lang = 1)
            WHERE c.id_category > 2'
        );
        $default_category = Db::getInstance()->getValue(
            'SELECT name FROM ' . _DB_PREFIX_ . 'category_lang
            WHERE id_category = ' . Configuration::get('PS_HOME_CATEGORY')
        );

        // 当前页的请求连接存入session中
        $request->getSession()->set('product_manage_url', $request->getRequestUri());

        return $this->render('@PrestaShop/Admin/Sell/Catalog/ProductManage/index.html.twig', [
            'products' => $products,
            'pagination' => $this->pagination($total, $limit, $page),
            'attribute_sets' => $attribute_sets,
            'categories' => $categories,
            'min_id' => $data['min_id'] ?? '',
            'max_id' => $data['max_id'] ?? '',
            'name' => $data['name'] ?? '',
            'supplier_reference' => $data['supplier_reference'] ?? '',
            'id_category' => $data['id_category'] ?? '',
            'attribute_set' => $data['attribute_set'] ?? '',
            'min_price' => $data['min_price'] ?? '',
            'max_price' => $data['max_price'] ?? '',
            'min_current_price' => $data['min_current_price'] ?? '',
            'max_current_price' => $data['max_current_price'] ?? '',
            'active' => $data['active'] ?? '',
            'default_category' => $default_category,
        ]);
    }

    // 新增页面
    public function createAction(Request $request)
    {
        $legacyContextService = $this->get('prestashop.adapter.legacy.context');
        $id_lang = $legacyContextService->getContext()->language->id;
        // 分类
        $query_categories = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('category', 'c')
                ->leftJoin('category_lang', 'cl', 'c.id_category = cl.id_category AND cl.id_lang = ' . $id_lang)
                ->select('c.id_category, c.id_parent, cl.name')
        );
        $categories = array_column($query_categories, 'name', 'id_category');

        // 语言列表
        $languages = $legacyContextService->getLanguages();
        return $this->render('@PrestaShop/Admin/Sell/Catalog/ProductManage/create.html.twig', [
            'categories' => $categories,
            'languages' => $languages,
            'default_language_iso' => $languages[0]['iso_code']
        ]);
    }

    // 新增
    public function addAction(Request $request)
    {
        try {
            Db::getInstance()->execute('BEGIN');
            // 验证
            $data = $request->request->all();
            if (!$data['name'][1]) {
                throw new Exception('name is required');
            }
            if (!$data['sku']) {
                throw new Exception('SKU is required');
            }
            if (Db::getInstance()->getValue('SELECT id_product FROM ' . _DB_PREFIX_ . 'product WHERE supplier_reference = "' . pSQL($data['sku']) . '"')) {
                throw new Exception('SKU is exists');
            }
            if ($data['price'] < 0 || $data['current_price'] < 0) {
                throw new Exception($this->trans('Price cannot be negative', 'Admin.Notifications.Error'));
            }
            if ($data['price'] < $data['current_price']) {
                throw new Exception($this->trans('Price cannot be lower than current price', 'Admin.Notifications.Error'));
            }
            // 新增产品
            $product = new Product();
            $product->name = $data['name'];
            $product->supplier_reference = pSQL(strtoupper($data['sku']));
            $product->description = $data['description'];
            $product->description_short = $data['description_short'];
            $product->model_description = $data['model_description'];
            $product->meta_title = $data['meta_title'];
            $product->meta_description = $data['meta_description'];
            $product->link_rewrite = $data['link_rewrite'][1] ? $data['link_rewrite'] : [1 => Tools::link_rewrite($data['name'][1])];
            $product->price = (float) $data['price'];
            $product->current_price = (float) $data['current_price'];
            $product->id_category_default = $data['id_category_default'];
            $product->visibility = pSQL($data['visibility']);
            $product->weight = $data['weight'];
            $product->type = pSQL($data['type']);
            $product->active = $data['active'];
            $product->is_update = 1;
            $product->save();

            // 分类
            if ($data['categories']) {
                $product->updateCategories($data['categories']);
            }

            // 折扣价格
            if ($data['price'] != $data['current_price']) {
                $price_difference = (float) $data['price'] - $data['current_price'];
                // 是否已存在折扣
                if (Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'specific_price` WHERE `id_product` = ' . $product->id)) {
                    // 新增折扣
                    ImportProduct::insertSpecificPrices([
                        'id_product' => $product->id,
                        'reduction' => $price_difference,
                        'id_shop' => 1
                    ]);
                }
            }
            if (ProductCore::updateRedisEsData($product->id, 'add')) {
                Db::getInstance()->execute('COMMIT');
                $this->addFlash('success', $this->trans('successfully updated', 'Admin.Notifications.Success'));
            } else {
                Db::getInstance()->execute('ROLLBACK');
                $this->addFlash('error', $this->trans('ES data synchronization failed, please contact technical personnel.', 'Admin.Notifications.Error'));
            }
            return $this->redirectToRoute('admin_products_edit', ['productId' => $product->id, 'form' => 'combinations']);
        } catch (Exception $e) {
            Db::getInstance()->execute('ROLLBACK');
            $this->addFlash('error', $e->getMessage());
            return $this->redirectToRoute('admin_product_manages_create');
        }
    }

    // 删除
    public function deleteAction(int $productId)
    {
        try {
            Db::getInstance()->execute('BEGIN');

            // 验证产品是否有订单
            if (Db::getInstance()->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'order_detail` WHERE `product_id` = ' . $productId)) {
                throw new Exception($this->trans('This product has orders, cannot be deleted', 'Admin.Catalog.Notification'));
            }
            // 删除
            Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product_lang` WHERE `id_product` = ' . $productId);
            Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product_shop` WHERE `id_product` = ' . $productId);
            Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product` WHERE `id_product` = ' . $productId);
            // 新增任务
            Db::getInstance()->insert('product_delete_task', ['id_product' => $productId, 'date_del' => date('Y-m-d H:i:s')]);
            // 更新ES
            if (ProductCore::deleteRedisEsData($productId)) {
                ProductCore::saveEsProductType($productId, 1, 'delete');
                Db::getInstance()->execute('COMMIT');
                return $this->json([
                    'success' => true,
                ]);
            } else {
                Db::getInstance()->execute('ROLLBACK');
                return $this->json([
                    'error' => 'ES data synchronization failed, please contact technical personnel.',
                ]);
            }
        } catch (Exception $e) {
            Db::getInstance()->execute('ROLLBACK');
            return $this->json([
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 批量删除
     */
    public function bulkDeleteAction(Request $request)
    {
        try {
            Db::getInstance()->execute('BEGIN');
            $ids = $request->request->get('product_bulk');
            if (!$ids) {
                throw new Exception($this->trans('Please select at least one item', 'Admin.Notifications.Error'));
            }
            // 验证产品是否有订单
            $error = [];
            foreach ($ids as $k => $id) {
                if (Db::getInstance()->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'order_detail` WHERE `product_id` = ' . $id)) {
                    $error[] = $id . ': ' . $this->trans('This product has orders, cannot be deleted', 'Admin.Catalog.Notification');
                    unset($ids[$k]);
                }
            }
            if ($ids) {
                Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product_lang` WHERE `id_product` IN (' . implode(',', $ids) . ')');
                Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product_shop` WHERE `id_product` IN (' . implode(',', $ids) . ')');
                Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product` WHERE `id_product` IN (' . implode(',', $ids) . ')');
                // 新增任务
                $insert = [];
                foreach ($ids as $id) {
                    $insert[] = [
                        'id_product' => $id,
                        'date_del' => date('Y-m-d H:i:s')
                    ];
                    //删除ES数据
                    ProductCore::deleteRedisEsData($id);
                    ProductCore::saveEsProductType($id, 1, 'deleteBulk');
                }
                // 新增任务
                Db::getInstance()->insert('product_delete_task', $insert);
            }

            Db::getInstance()->execute('COMMIT');

            // 是否有错误信息
            if ($error) {
                throw new Exception(implode("\n", $error));
            }
            return $this->json([
                'success' => true,
            ]);
        } catch (Exception $e) {
            Db::getInstance()->execute('ROLLBACK');
            return $this->json([
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 更新产品状态
     * @param \Symfony\Component\HttpFoundation\Request $request
     * @return
     */
    public function updateActiveAction(int $productId, Request $request)
    {
        $active = $request->request->get('active');
        try {
            $product = new Product($productId);
            $product->active = $active;
            $product->is_update = 1;
            $product->save();

            // 更新ES
            if (ProductCore::updateRedisEsData($productId, 'updateActive')) {
                Db::getInstance()->execute('COMMIT');
                return $this->json([
                    'success' => true,
                ]);
            } else {
                Db::getInstance()->execute('ROLLBACK');
                return $this->json([
                    'error' => 'ES data synchronization failed, please contact technical personnel.',
                ]);
            }
        } catch (Exception $e) {
            return $this->json([
                'error' => $e->getMessage()
            ]);
        }
    }

    /**
     * 批量更新状态
     */
    public function bulkUpdateActiveAction(int $active, Request $request)
    {
        try {
            Db::getInstance()->execute('BEGIN');
            $ids = $request->request->get('product_bulk');
            if (!$ids) {
                throw new Exception($this->trans('Please select at least one item', 'Admin.Notifications.Error'));
            }
            // 更新
            Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'product` SET `active` = ' . $active . ', `is_update` = 1 WHERE `id_product` IN (' . implode(',', $ids) . ')');
            Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'product_shop` SET `active` = ' . $active . ' WHERE `id_product` IN (' . implode(',', $ids) . ')');
            //更新ES记录日志
            ProductCore::updateRedisEsData($ids, 'updateBulkActive');

            Db::getInstance()->execute('COMMIT');
            return $this->json([
                'success' => true,
            ]);
        } catch (Exception $e) {
            Db::getInstance()->execute('ROLLBACK');
            return $this->json([
                'error' => $e->getMessage()
            ]);
        }
    }

    //商品属性更新
    public function SaveAttributeAction(Request $request): Response
    {
        set_time_limit(600); // 设置为600秒，可以根据需要调整
        ini_set('display_errors', 'on');
        ini_set('memory_limit', -1);
        try {
            $file = $request->files->get('product_save_attribute_file');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];
            // 将文件上传到指定文件夹
            $this->uploadedFile($file, "save_attribute");
            // 整理表头数据
            $header = [];
            foreach ($data_header as $k => $v) {
                $header[$v] = $k;
            }
            $header = array_keys($header);
            // 去掉表头
            unset($data[1]);
            $data = array_map(fn($raw) => array_combine($header, $raw), $data);
            // 获取商品信息
            $products = Db::getInstance()->executeS((new \DbQueryCore())->from('product', 'p')
                ->where('p.supplier_reference IN ( "' . implode('","', array_column($data, 'sku')) . '")')
                ->select('UPPER(p.supplier_reference) AS supplier_reference, p.id_product'));
            $productIds = [];
            foreach ($products as $product) {
                $productIds[$product['supplier_reference']] = $product['id_product'];
            }
            $error_text = '';
            // 获取最大feature排序
            $max_feature_position = Db::getInstance()->getValue('SELECT MAX(position) FROM ' . _DB_PREFIX_ . 'feature');
            // 获取最大attribute_group排序
            $max_attribute_group_position = Db::getInstance()->getValue('SELECT MAX(position) FROM ' . _DB_PREFIX_ . 'attribute_group');
            // 查看所有material
            $materials = Db::getInstance()->executeS((new \DbQueryCore())->from('material', 'm')
                ->leftJoin('material_lang', 'ml', 'ml.id_material = m.id_material')
                ->leftJoin('material_shop', 'ms', 'ms.id_material = m.id_material')
                ->where('ml.`id_lang` = 1')
                ->select('m.`id_material`,ml.`name`'));
            // 将material名称当作键值以供查询
            $material_names = [];
            foreach ($materials as $material) {
                $material_names[strtolower(trim($material['name']))] = $material['id_material'];
            }
            // 查询所有的attribute并以group分组
            $attributes = Db::getInstance()->executeS((new \DbQueryCore())->from('attribute', 'a')
                ->leftJoin('attribute_lang', 'al', 'al.id_attribute = a.id_attribute')
                ->leftJoin('attribute_shop', 'as', 'as.id_attribute = a.id_attribute')
                ->where('al.`id_lang` = 1 ')
                ->select('a.`id_attribute`, a.`id_attribute_group`, al.`name`,a.`position`'));
            $groupedAttributes = [];
            foreach ($attributes as $attribute) {
                $idGroup = $attribute['id_attribute_group'];
                if (!isset($groupedAttributes[$idGroup])) {
                    $groupedAttributes[$idGroup] = [];
                }
                $groupedAttributes[$idGroup][strtolower(trim($attribute['name']))] = $attribute['id_attribute'];
            }
            // 储存第一次创建的feature id或者已经存在的feature id 或者attribute id
            $key_data = $key_id = [];
            $batchSize = 100; // 每批处理100条记录
            $offset = 0;
            $text = '';
            do {
                $dataBatch = array_slice($data, $offset, $batchSize);
                Db::getInstance()->execute('BEGIN');
                try {
                    foreach ($dataBatch as $datum) {
                        // 产品sku转大写
                        $sku = strtoupper($datum['sku']);
                        // 无法匹配商品直接跳过
                        if (!isset($sku) || !isset($productIds[$sku])) {
                            $error_text .= $sku . ': SKU not found' . PHP_EOL;
                            continue;
                        }
                        $id_product = $productIds[$sku];
                        foreach ($datum as $key => $value) {
                            // 第一次执行默认没有
                            if (!isset($key_data[$key])) {
                                $key_data[$key] = 0;
                            }
                            if ($key == 'sku') {
                                continue;
                            }
                            if ($value == '') {
                                continue;
                            }
                            // 颜色映射
                            if ($key == 'Color Mapping') {
                                if ($value != '') {
                                    // 整理映射关系
                                    $id_attribute_colors = $this->OrganizationColorMappings($value, $groupedAttributes) ?? [];
                                    // 创建颜色关系
                                    $error_text .= $this->CreateColorMapping($sku, $id_product, $id_attribute_colors);
                                }
                                continue;
                            }
                            // 如果是fabric
                            if (strpos($key, 'fabric') !== false) {
                                $fabric = $value;
                                // 暂时默认以逗号分隔的
                                $fabric_values = explode(',', $fabric);
                                $feature_code = $key;
                                // 查看是否有这个feature
                                if ($key_data[$key] == 0) {   // 第一次加载
                                    $fabric_feature = Db::getInstance()->executeS(
                                        'SELECT f.* FROM ' . _DB_PREFIX_ . 'feature f
                                    LEFT JOIN ' . _DB_PREFIX_ . 'feature_lang fl ON (f.id_feature = fl.id_feature)
                                    ' . Shop::addSqlAssociation('feature', 'f') . '
                                    WHERE f.code = \'' . $feature_code . '\''
                                    );
                                    $key_data[$key] = 1;
                                    if ($fabric_feature) {
                                        $key_id[$key] = $fabric_feature[0]['id_feature'];
                                    }
                                } else {
                                    $fabric_feature = $key_id[$key];
                                }
                                // 如果没有这个feature就创建
                                if (empty($fabric_feature)) {
                                    $max_feature_position++;
                                    // 创建feature
                                    $fabric_feature_id = $this->createFeature($this->camelCase($feature_code), $max_feature_position, $idLang);
                                    $key_id[$key] = $fabric_feature_id;
                                    // 创建feature value
                                    foreach ($fabric_values as $fabric_value) {
                                        $this->createFeatureValue($id_product, $fabric_value, $fabric_feature_id, $idLang);
                                    }
                                } else {
                                    $fabric_feature_id = $key_id[$key];
                                    // 存在feature product 删除
                                    Db::getInstance()->delete('feature_product', 'id_product = ' . $id_product . ' AND id_feature = ' . $fabric_feature_id);
                                    // 获取fabric_values feature_value_id
                                    foreach ($fabric_values as $fabric_value) {
                                        $feature_value_id = Db::getInstance()->getValue(
                                            'SELECT fv.`id_feature_value` FROM ' . _DB_PREFIX_ . 'feature_value fv
                                        LEFT JOIN ' . _DB_PREFIX_ . 'feature_value_lang fvl ON (fv.id_feature_value = fvl.id_feature_value)
                                        WHERE fv.id_feature = ' . (int) $fabric_feature_id . ' AND fvl.`value` = \'' . $fabric_value . '\''
                                        );
                                        if ($feature_value_id) {
                                            // feature product
                                            $fabric_feature_product = [
                                                'id_feature' => $fabric_feature_id,
                                                'id_feature_value' => $feature_value_id,
                                                'id_product' => $id_product,
                                            ];
                                            Db::getInstance()->insert('feature_product', $fabric_feature_product);
                                        } else {
                                            // 创建feature value
                                            $this->createFeatureValue($id_product, $fabric_value, $fabric_feature_id, $idLang);
                                        }
                                    }
                                }
                                // 查看material 默认第一个
                                $material_name = $this->camelCase($fabric_values[0]);
                                $material_name = strtolower($material_name);
                                if (isset($material_names[$material_name])) {
                                    $material_id = $material_names[$material_name];
                                } else {
                                    // 没有material新增一个
                                    $material_id = $this->createMaterial($material_name, $idLang);
                                    $material_names[$material_name] = $material_id;
                                }
                                // 更新id_material 跟同步redis跟ES
                                Db::getInstance()->update('product', ['id_material' => $material_id, 'is_update' => 1], 'id_product = ' . (int) $id_product);
                            } else {
                                // 颜色属性
                                $attribute = $value;
                                $attribute_values = explode('||', $attribute);
                                $attribute_group_name = $key;
                                // 查看是否有这个Attribute_Group
                                if ($key_data[$key] == 0) {   // 第一次加载
                                    $attribute_group = Db::getInstance()->executeS(
                                        'SELECT ag.* FROM ' . _DB_PREFIX_ . 'attribute_group ag
                                    LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_lang agl ON (ag.id_attribute_group = agl.id_attribute_group)
                                    ' . Shop::addSqlAssociation('attribute_group', 'ag') . '
                                    WHERE agl.`name` = \'' . $attribute_group_name . '\''
                                    );
                                    $key_data[$key] = 1;
                                    if ($attribute_group) {
                                        $key_id[$key] = $attribute_group[0]['id_attribute_group'];
                                    }
                                } else {
                                    $attribute_group = $key_id[$key];
                                }
                                // 如果没有这个attribute_group
                                if (empty($attribute_group)) {
                                    $max_attribute_group_position++;
                                    // 新增attribute_group
                                    $attribute_group_id = $this->createAttributeGroup($key, $max_attribute_group_position, $idLang);
                                    $key_id[$key] = $attribute_group_id;
                                    // 创建attribute
                                    foreach ($attribute_values as $attribute_value) {
                                        // 根据：分隔
                                        $attribute_value_all = explode(':', $attribute_value);
                                        $attribute_name = $this->camelCase($attribute_value_all[0]);
                                        $attribute_name = strtolower($attribute_name);
                                        $product_attribute_price = $attribute_value_all[2] ?? 0;
                                        $product_attribute_sort = $attribute_value_all[3] ?? 0;
                                        // 新增对应attribute
                                        $attribute_id = $this->createAttribute($attribute_name, $attribute_group_id, $idLang);
                                        // 新增has——attribute
                                        $this->createProductHasAttribute($id_product, $attribute_group_id, $attribute_id, $product_attribute_sort, $product_attribute_price);
                                        //将新增的属性添加到groupedAttributes中
                                        $groupedAttributes[$attribute_group_id][$attribute_name] = $attribute_id;
                                    }
                                } else {
                                    $attribute_group_id = $key_id[$key];
                                    // 删除has对应的group属性
                                    Db::getInstance()->delete('product_has_attribute', 'id_product = ' . $id_product . ' AND id_attribute_group = ' . $attribute_group_id);
                                    foreach ($attribute_values as $attribute_value) {
                                        // 根据：分隔
                                        $attribute_value_all = explode(':', $attribute_value);
                                        $attribute_name = $this->camelCase($attribute_value_all[0]);
                                        $attribute_name = strtolower($attribute_name);
                                        $product_attribute_price = $attribute_value_all[2] ?? 0;
                                        $product_attribute_sort = $attribute_value_all[3] ?? 0;
                                        // 判断attribute是否存在
                                        if (isset($groupedAttributes[$attribute_group_id]) && isset($groupedAttributes[$attribute_group_id][$attribute_name])) {
                                            $attribute_id = $groupedAttributes[$attribute_group_id][$attribute_name];
                                        } else {
                                            // 新增对应attribute
                                            $attribute_id = $this->createAttribute($attribute_name, $attribute_group_id, $idLang);
                                            //将新增的属性添加到groupedAttributes中
                                            $groupedAttributes[$attribute_group_id][$attribute_name] = $attribute_id;
                                        }
                                        // 新增has——attribute
                                        $this->createProductHasAttribute($id_product, $attribute_group_id, $attribute_id, $product_attribute_sort, $product_attribute_price);
                                    }
                                }
                                // 更新同步redis跟ES
                                Db::getInstance()->update('product', ['is_update_attribute' => 1, 'is_update' => 1], 'id_product = ' . (int) $id_product);
                                // // 生成操作日志无需更新redis
                                // Tools::clearProductSearchRedis([$id_product]);
                                // ProductCore::addOperateData($id_product, 'updateAttribute');
                            }
                        }
                        ProductCore::updateRedisEsData($id_product, 'updateActive');
                    }
                    Db::getInstance()->execute('COMMIT');
                } catch (Exception $e) {
                    Db::getInstance()->execute('ROLLBACK');
                    throw $e;
                }
                $offset += $batchSize;
            } while (count($dataBatch) == $batchSize);
            $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.' . $text);
        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }
        return $this->redirectToRoute('admin_product_manages_index');
    }

    //商品属性添加
    public function AddAttributeAction(Request $request): Response
    {
        set_time_limit(0);
        ini_set('display_errors', 'on');
        ini_set('memory_limit', -1);
        try {
            $file = $request->files->get('product_save_attribute_file');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];
            // 整理表头数据
            //将文件上传到指定文件夹
            $this->uploadedFile($file, "add_attribute");
            $header = [];
            foreach ($data_header as $k => $v) {
                $header[$v] = $k;
            }
            $header = array_keys($header);
            // 去掉表头
            unset($data[1]);
            $data = array_map(fn($raw) => array_combine($header, $raw), $data);
            // 获取商品信息
            $products = Db::getInstance()->executeS((new \DbQueryCore())->from('product', 'p')
                ->where('p.supplier_reference IN ( "' . implode('","', array_column($data, 'sku')) . '")')
                ->select('UPPER(p.supplier_reference) AS supplier_reference, p.id_product'));
            $productIds = [];
            foreach ($products as $product) {
                $productIds[$product['supplier_reference']] = $product['id_product'];
            }
            $error_text = '';
            //获取最大feature排序
            $max_feature_position = Db::getInstance()->getValue('SELECT MAX(position) FROM ' . _DB_PREFIX_ . 'feature');
            //获取最大attribute_group排序
            $max_attribute_group_position = Db::getInstance()->getValue('SELECT MAX(position) FROM ' . _DB_PREFIX_ . 'attribute_group');
            //查看所有material
            $materials = Db::getInstance()->executeS((new \DbQueryCore())->from('material', 'm')
                ->leftJoin('material_lang', 'ml', 'ml.id_material = m.id_material')
                ->leftJoin('material_shop', 'ms', 'ms.id_material = m.id_material')
                ->where('ml.`id_lang` = 1')
                ->select('m.`id_material`,ml.`name`'));
            //将material名称当作键值以供查询
            $material_names = [];
            foreach ($materials as $material) {
                $material_names[strtolower(trim($material['name']))] = $material['id_material'];
            }
            //查询所有的attribute并以group分组
            $attributes = Db::getInstance()->executeS(
                (new \DbQueryCore())->from('attribute', 'a')
                    ->leftJoin('attribute_lang', 'al', 'al.id_attribute = a.id_attribute')
                    ->leftJoin('attribute_shop', 'as', 'as.id_attribute = a.id_attribute')
                    ->where('al.`id_lang` = 1 ')
                    ->select('a.`id_attribute`, a.`id_attribute_group`, al.`name`,a.`position`')
            );
            $groupedAttributes = [];

            foreach ($attributes as $attribute) {
                $idGroup = $attribute['id_attribute_group'];
                if (!isset($groupedAttributes[$idGroup])) {
                    $groupedAttributes[$idGroup] = [];
                }
                $groupedAttributes[$idGroup][strtolower(trim($attribute['name']))] = $attribute['id_attribute'];
            }
            //储存第一次创建的feature id或者已经存在的feature id 或者attribute id
            $key_data = $key_id = [];
            foreach ($data as $datum) {
                // 产品sku转大写
                $sku = strtoupper($datum['sku']);
                // 无法匹配商品直接跳过
                if (!isset($sku) || !isset($productIds[$sku])) {
                    $error_text .= $sku . ': SKU not found' . PHP_EOL;
                    continue;
                }
                $id_product = $productIds[$sku];
                //是否需要更新product
                $product_type = 0;
                foreach ($datum as $key => $value) {
                    //第一次执行默认没有
                    if (!isset($key_data[$key])) {
                        $key_data[$key] = 0;
                    }
                    if ($key == 'sku') {
                        continue;
                    }
                    if ($value == '') {
                        continue;
                    }
                    //颜色映射
                    if ($key == 'Color Mapping') {
                        if ($value != '') {
                            //整理映射关系
                            $id_attribute_colors = $this->OrganizationColorMappings($value, $groupedAttributes) ?? [];
                            //创建颜色关系
                            $error_text .= $this->CreateColorMapping($sku, $id_product, $id_attribute_colors);
                        }
                        continue;
                    }
                    //如果是fabric
                    if (strpos($key, 'fabric') !== false) {
                        $fabric = $value;
                        //暂时默认以逗号分隔的
                        $fabric_values = explode(',', $fabric);
                        $feature_code = $key;
                        //查看是否有这个feature
                        if ($key_data[$key] == 0) {   //第一次加载
                            $fabric_feature = Db::getInstance()->executeS(
                                'SELECT f.* FROM ' . _DB_PREFIX_ . 'feature f
                                LEFT JOIN ' . _DB_PREFIX_ . 'feature_lang fl ON (f.id_feature = fl.id_feature)
                                ' . Shop::addSqlAssociation('feature', 'f') . '
                                WHERE f.code = \'' . $feature_code . '\''
                            );
                            $key_data[$key] = 1;
                            if ($fabric_feature) {
                                $key_id[$key] = $fabric_feature[0]['id_feature'];
                            }
                        } else {
                            $fabric_feature = $key_id[$key];
                        }
                        //如果没有这个feature就创建
                        if (empty($fabric_feature)) {
                            $max_feature_position++;
                            //创建feature
                            $fabric_feature_id = $this->createFeature($this->camelCase($feature_code), $max_feature_position, $idLang);
                            $key_id[$key] = $fabric_feature_id;
                            //创建feature value
                            foreach ($fabric_values as $fabric_value) {
                                $this->createFeatureValue($id_product, $fabric_value, $fabric_feature_id, $idLang);
                            }
                        } else {
                            $fabric_feature_id = $key_id[$key];
                            //获取fabric_values feature_value_id
                            foreach ($fabric_values as $fabric_value) {
                                $feature_value_id = Db::getInstance()->getValue(
                                    'SELECT fv.`id_feature_value` FROM ' . _DB_PREFIX_ . 'feature_value fv
                                    LEFT JOIN ' . _DB_PREFIX_ . 'feature_value_lang fvl ON (fv.id_feature_value = fvl.id_feature_value)
                                    WHERE fv.id_feature = ' . (int) $fabric_feature_id . ' AND fvl.`value` = \'' . $fabric_value . '\''
                                );
                                if ($feature_value_id) {
                                    //查看原来是否存在
                                    $feature_product_id = Db::getInstance()->getValue(
                                        'SELECT * FROM ' . _DB_PREFIX_ . 'feature_product WHERE id_feature = ' . (int) $fabric_feature_id . ' AND id_feature_value = ' . (int) $feature_value_id . ' AND id_product = ' . (int) $id_product
                                    );
                                    if (!empty($feature_product_id)) {
                                        // $error_text .= '此'.$sku.' '.$fabric_value.'已存在</br>';
                                        continue;
                                    }
                                    //feature product
                                    $fabric_feature_product = [
                                        'id_feature' => $fabric_feature_id,
                                        'id_feature_value' => $feature_value_id,
                                        'id_product' => $id_product,
                                    ];
                                    Db::getInstance()->insert('feature_product', $fabric_feature_product);
                                } else {
                                    //创建feature value
                                    $this->createFeatureValue($id_product, $fabric_value, $fabric_feature_id, $idLang);
                                }
                            }
                        }
                        //查看material 默认第一个
                        $material_name = $this->camelCase($fabric_values[0]);
                        $material_name = strtolower($material_name);
                        if (isset($material_names[$material_name])) {
                            $material_id = $material_names[$material_name];
                        } else {
                            //没有material新增一个
                            $material_id = $this->createMaterial($material_name, $idLang);
                            $material_names[$material_name] = $material_id;
                        }
                        //更新id_material 跟同步redis跟ES
                        Db::getInstance()->update('product', ['id_material' => $material_id, 'is_update' => 1], 'id_product = ' . (int) $id_product);
                    } else {
                        //颜色属性
                        $attribute = $value;
                        $attribute_values = explode('||', $attribute);
                        $attribute_group_name = $key;
                        //查看是否有这个Attribute_Group
                        if ($key_data[$key] == 0) {   //第一次加载
                            $attribute_group = Db::getInstance()->executeS(
                                'SELECT ag.* FROM ' . _DB_PREFIX_ . 'attribute_group ag
                                LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_lang agl ON (ag.id_attribute_group = agl.id_attribute_group)
                                ' . Shop::addSqlAssociation('attribute_group', 'ag') . '
                                WHERE agl.`name` = \'' . $attribute_group_name . '\''
                            );
                            $key_data[$key] = 1;
                            if ($attribute_group) {
                                $key_id[$key] = $attribute_group[0]['id_attribute_group'];
                            }
                        } else {
                            $attribute_group = $key_id[$key];
                        }
                        //如果没有这个attribute_group
                        if (empty($attribute_group)) {
                            $max_attribute_group_position++;
                            //新增attribute_group
                            $attribute_group_id = $this->createAttributeGroup($key, $max_attribute_group_position, $idLang);
                            $key_id[$key] = $attribute_group_id;
                            //创建attribute
                            foreach ($attribute_values as $attribute_value) {
                                //根据：分隔
                                $attribute_value_all = explode(':', $attribute_value);
                                $attribute_name = $this->camelCase($attribute_value_all[0]);
                                $attribute_name = strtolower($attribute_name);
                                if (!isset($attribute_value_all[2]) && !isset($attribute_value_all[3])) {
                                    $error_text .= '此' . $sku . ' ' . $attribute_name . '属性格式不正确</br>';
                                    continue;
                                }
                                $product_attribute_price = $attribute_value_all[2] ?? 0;
                                $product_attribute_sort = $attribute_value_all[3] ?? 0;
                                //新增对应attribute
                                $attribute_id = $this->createAttribute($attribute_name, $attribute_group_id, $idLang);
                                //新增has——attribute
                                $this->createProductHasAttribute($id_product, $attribute_group_id, $attribute_id, $product_attribute_sort, $product_attribute_price);
                                $product_type = 1;
                                //将新增的属性添加到groupedAttributes中
                                $groupedAttributes[$attribute_group_id][$attribute_name] = $attribute_id;
                            }
                        } else {
                            $attribute_group_id = $key_id[$key];
                            foreach ($attribute_values as $attribute_value) {
                                //根据：分隔
                                $attribute_value_all = explode(':', $attribute_value);
                                $attribute_name = $this->camelCase($attribute_value_all[0]);
                                $attribute_name = strtolower($attribute_name);
                                if (!isset($attribute_value_all[2]) && !isset($attribute_value_all[3])) {
                                    $error_text .= '此' . $sku . ' ' . $attribute_name . '属性格式不正确</br>';
                                    continue;
                                }
                                $product_attribute_price = $attribute_value_all[2] ?? 0;
                                $product_attribute_sort = $attribute_value_all[3] ?? 0;
                                //判断attribute是否存在
                                if (isset($groupedAttributes[$attribute_group_id]) && isset($groupedAttributes[$attribute_group_id][$attribute_name])) {
                                    $attribute_id = $groupedAttributes[$attribute_group_id][$attribute_name];
                                } else {
                                    //新增对应attribute
                                    $attribute_id = $this->createAttribute($attribute_name, $attribute_group_id, $idLang);
                                    //将新增的属性添加到groupedAttributes中
                                    $groupedAttributes[$attribute_group_id][$attribute_name] = $attribute_id;
                                }
                                //判断是否已经存在
                                $product_has_attribute_id = Db::getInstance()->getValue(
                                    'SELECT * FROM ' . _DB_PREFIX_ . 'product_has_attribute WHERE id_product = ' . (int) $id_product . ' AND id_attribute_group = ' . (int) $attribute_group_id . ' AND id_attribute = ' . (int) $attribute_id
                                );
                                if (!empty($product_has_attribute_id)) {
                                    // $error_text .= '此'.$sku.' '.$attribute_name.'已存在</br>';
                                    continue;
                                }
                                //新增has——attribute
                                $this->createProductHasAttribute($id_product, $attribute_group_id, $attribute_id, $product_attribute_sort, $product_attribute_price);
                                $product_type = 1;
                            }
                        }
                    }
                }
                //更新同步redis跟ES
                if ($product_type == 1) {
                    Db::getInstance()->update('product', ['is_update_attribute' => 1, 'is_update' => 1], 'id_product = ' . (int) $id_product);
                    //生成操作日志
                    ProductCore::updateRedisEsData($id_product, 'addAttribute');
                }
                //别忘记同步ES跟redis product表  is_update_attribute  同步变体 is_update   同步ES 和 redis
            }
            if ($error_text == '') {
                $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.');
            } else {
                $this->addFlash('error', $error_text);
            }

        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }
        return $this->redirectToRoute('admin_product_manages_index');
    }

    //颜色映射添加
    public function createColorMapping($sku, $id_product, $color_mappings)
    {
        $error_text = '';
        foreach ($color_mappings as $id_attribute => $color_mapping) {
            if (Db::getInstance()->update('product_has_attribute', ['id_attribute_color' => $color_mapping], 'id_product = ' . (int) $id_product . ' AND id_attribute = ' . (int) $id_attribute)) {
                //添加映射成功
            } else {
                $error_text .= '此' . $sku . ' ' . $color_mapping . '颜色映射格式不正确</br>';
            }
        }
        return $error_text;
    }

    /**
     * 整理颜色映射数据
     * @param mixed $color_mapping
     * @return array
     */
    protected function OrganizationColorMappings($color_mapping, &$exist_attribute_ids)
    {
        $color_mappings_result = [];
        $color_mappings = explode(',', $color_mapping);
        foreach ($color_mappings as $color_mapping) {
            $arr = explode(':', $color_mapping);
            $key = ImportProduct::getAttributeId(2, $this->camelCase($arr[0]), $exist_attribute_ids);
            $value = ImportProduct::getAttributeId(2, $this->camelCase($arr[1]), $exist_attribute_ids);
            $color_mappings_result[$key] = $value;
        }
        return $color_mappings_result;
    }

    //将字符串以下划线分隔重组大小写
    public function camelCase($input_string)
    {
        // 按下划线拆分
        $parts = explode('_', $input_string);
        // 每个单词首字母大写
        $capitalizeCdParts = array_map('ucwords', $parts);
        // 拼接成字符串
        $camelCaseString = implode(' ', $capitalizeCdParts);
        // 去除多余空格 多个空格压缩为一个
        $camelCaseString = preg_replace('/\s+/', ' ', $camelCaseString);
        return $camelCaseString;
    }

    //创建feature
    public function createFeature($feature_code, $max_feature_position = 0, $idLang = 1)
    {
        $fabric_feature = [
            'code' => $feature_code,
            'is_color' => 0,
            'position' => $max_feature_position,
        ];
        $fabric_feature_id = Db::getInstance()->insert('feature', $fabric_feature);
        $fabric_feature_id = Db::getInstance()->Insert_ID();
        //feature shop
        $fabric_feature_shop = [
            'id_feature' => $fabric_feature_id,
            'id_shop' => 1,
        ];
        Db::getInstance()->insert('feature_shop', $fabric_feature_shop);
        //创建语言
        // 拼接成字符串
        $featureCode = Tools::formatAttributeName($this->camelCase($feature_code));
        $fabric_feature_lang = [
            'id_feature' => $fabric_feature_id,
            'id_lang' => $idLang,
            'name' => $featureCode,
        ];
        Db::getInstance()->insert('feature_lang', $fabric_feature_lang);
        return $fabric_feature_id;
    }

    //创建material
    public function createMaterial($name, $idLang)
    {
        $material = [
            'position' => 1,
            'date_add' => date('Y-m-d H:i:s'),
            'date_upd' => date('Y-m-d H:i:s'),
        ];
        $material_id = Db::getInstance()->insert('material', $material);
        $material_id = Db::getInstance()->Insert_ID();
        //material shop
        $material_shop = [
            'id_material' => $material_id,
            'id_shop' => 1,
        ];
        Db::getInstance()->insert('material_shop', $material_shop);
        //创建语言
        $name = Tools::formatAttributeName($name);
        $material_lang = [
            'id_material' => $material_id,
            'id_lang' => $idLang,
            'name' => $name,
        ];
        Db::getInstance()->insert('material_lang', $material_lang);
        return $material_id;
    }

    //创建feature value
    public function createFeatureValue($id_product, $fabric_value, $fabric_feature_id, $idLang = 1)
    {
        $fabric_value = trim($fabric_value);
        $fabric_value_data = [
            'id_feature' => $fabric_feature_id,
            'url_name' => Tools::link_rewrite($fabric_value),
            'custom' => 0,
            'position' => 0,
        ];
        $feature_value_id = Db::getInstance()->insert('feature_value', $fabric_value_data);
        $feature_value_id = Db::getInstance()->Insert_ID();
        //创建feature value lang
        $fabric_value = Tools::formatAttributeName($fabric_value);
        $fabric_value_lang = [
            'id_feature_value' => $feature_value_id,
            'id_lang' => $idLang,
            'value' => $this->camelCase($fabric_value),
        ];
        Db::getInstance()->insert('feature_value_lang', $fabric_value_lang);
        //feature product
        $fabric_feature_product = [
            'id_feature' => $fabric_feature_id,
            'id_feature_value' => $feature_value_id,
            'id_product' => $id_product,
        ];
        Db::getInstance()->insert('feature_product', $fabric_feature_product);
    }

    //创建attribute_group
    public function createAttributeGroup($name, $max_attribute_group_position = 0, $idLang = 1)
    {
        $attribute_group = [
            'group_type' => $name,
            'is_color_group' => 0,
            'position' => $max_attribute_group_position,
        ];
        $attribute_group_id = Db::getInstance()->insert('attribute_group', $attribute_group);
        $attribute_group_id = Db::getInstance()->Insert_ID();
        //feature shop
        $attribute_group_shop = [
            'id_attribute_group' => $attribute_group_id,
            'id_shop' => 1,
        ];
        Db::getInstance()->insert('attribute_group_shop', $attribute_group_shop);
        //创建语言
        $name = Tools::formatAttributeName($name);
        $attribute_group_lang = [
            'id_attribute_group' => $attribute_group_id,
            'id_lang' => $idLang,
            'name' => $name,
            'public_name' => $name,
        ];
        Db::getInstance()->insert('attribute_group_lang', $attribute_group_lang);
        return $attribute_group_id;
    }

    //创建attribute
    public function createAttribute($name, $id_attribute_group, $idLang = 1)
    {
        $max_attribute_position = Db::getInstance()->getValue('SELECT MAX(position) FROM ' . _DB_PREFIX_ . 'attribute WHERE id_attribute_group = ' . $id_attribute_group);
        $max_attribute_position++;
        if (strpos(strtolower($name), 'show as picture') !== false) {
            $code = 'show-as-picture';
        } else {
            $code = Tools::link_rewrite($name);
        }
        $attribute = [
            'code' => $code,
            'id_attribute_group' => $id_attribute_group,
            'is_custom_size' => 0,
            'position' => $max_attribute_position,
            'price' => 0
        ];
        $attribute_id = Db::getInstance()->insert('attribute', $attribute);
        $attribute_id = Db::getInstance()->Insert_ID();
        //feature shop
        $attribute_shop = [
            'id_attribute' => $attribute_id,
            'id_shop' => 1,
        ];
        Db::getInstance()->insert('attribute_shop', $attribute_shop);
        //创建语言
        $name = Tools::formatAttributeName($name);
        $attribute_lang = [
            'id_attribute' => $attribute_id,
            'id_lang' => $idLang,
            'name' => $name,
        ];
        Db::getInstance()->insert('attribute_lang', $attribute_lang);
        return $attribute_id;
    }

    //创建product_has_attribute
    public function createProductHasAttribute($id_product, $id_attribute_group, $id_attribute, $sort, $price)
    {
        $product_has_attribute = [
            'id_product' => $id_product,
            'id_attribute_group' => $id_attribute_group,
            'id_attribute' => $id_attribute,
            'sort' => $sort,
            'price' => $price
        ];
        Db::getInstance()->insert('product_has_attribute', $product_has_attribute);
    }

    //同步产品信息到ES
    public function SyncProductToEs(int $productId)
    {
        if ($productId) {
            // // 定义命令
            // $command = 'cd ' . _PS_ROOT_DIR_ . ' && php bin/console ftp';
            // // 执行命令
            // exec($command, $output, $return_var);
            if (ProductCore::updateRedisEsData($productId)) {
                return $this->json([
                    'success' => true,
                ]);
            } else {
                return $this->json([
                    'error' => 'Synchronization failed',
                ]);
            }
        }
        return $this->json([
            'error' => 'Synchronization failed',
        ]);
    }

    function ftp_append($ftp_conn, $remote_file, $local_file, $mode = FTP_ASCII)
    {
        // 创建临时文件
        $temp_file = tempnam(sys_get_temp_dir(), 'ftp_append');

        try {
            //下载远程文件到临时文件
            if (ftp_get($ftp_conn, $temp_file, $remote_file, $mode)) {
                //将本地文件内容追加到临时文件
                file_put_contents($temp_file, file_get_contents($local_file), FILE_APPEND);
            } else {
                // 如果远程文件不存在，直接使用本地文件
                copy($local_file, $temp_file);
            }

            //上传临时文件到远程服务器
            if (!ftp_put($ftp_conn, $remote_file, $temp_file, $mode)) {
                // throw new Exception('Failed to upload file');
                return false;
            }
            return true;
        } finally {
            // 清理临时文件
            if (file_exists($temp_file)) {
                unlink($temp_file);
            }
        }
    }

    //更新属性size价格
    function SaveAttributeSizePriceAction(Request $request): Response
    {
        set_time_limit(0);
        ini_set('display_errors', 'on');
        ini_set('memory_limit', -1);
        try {
            $file = $request->files->get('product_save_attribute_file');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];
            // 整理表头数据
            //将文件上传到指定文件夹
            $this->uploadedFile($file, "product_attribute_price");
            $header = [];
            foreach ($data_header as $k => $v) {
                $header[$v] = $k;
            }
            $header = array_keys($header);
            // 去掉表头
            unset($data[1]);
            $data = array_map(fn($raw) => array_combine($header, $raw), $data);
            // 获取商品信息
            $products = Db::getInstance()->executeS((new \DbQueryCore())->from('product', 'p')
                ->where('p.supplier_reference IN ( "' . implode('","', array_column($data, 'sku')) . '")')
                ->select('UPPER(p.supplier_reference) AS supplier_reference, p.id_product'));
            $productIds = [];
            foreach ($products as $product) {
                $productIds[$product['supplier_reference']] = $product['id_product'];
            }
            //查询所有的attribute并以group分组
            $attributes = Db::getInstance()->executeS(
                (new \DbQueryCore())->from('attribute', 'a')
                    ->leftJoin('attribute_lang', 'al', 'al.id_attribute = a.id_attribute')
                    ->leftJoin('attribute_shop', 'as', 'as.id_attribute = a.id_attribute')
                    ->where('al.`id_lang` = 1 ')
                    ->select('a.`id_attribute`, a.`id_attribute_group`, al.`name`,a.`position`')
            );
            $groupedAttributes = [];

            foreach ($attributes as $attribute) {
                $idGroup = $attribute['id_attribute_group'];
                if (!isset($groupedAttributes[$idGroup])) {
                    $groupedAttributes[$idGroup] = [];
                }
                $groupedAttributes[$idGroup][strtolower($attribute['name'])] = $attribute['id_attribute'];
            }
            $error_text = '';
            $productRedisIds = $updateData = [];
            $type_1 = 1;
            foreach ($data as $datum) {
                // 产品sku转大写
                $sku = strtoupper($datum['sku']);
                // 无法匹配商品直接跳过
                if (!isset($sku) || !isset($productIds[$sku])) {
                    $error_text .= $sku . ': SKU not found' . PHP_EOL;
                    continue;
                }
                $id_product = $productIds[$sku];
                //是否需要更新product
                $product_type = 0;
                foreach ($datum as $key => $value) {
                    if ($key == 'sku') {
                        continue;
                    }
                    if ($value == '') {
                        continue;
                    }
                    if ($type_1 = 1) {
                        $attribute_group_name = explode('_', $key);
                        $attribute_group_name = $attribute_group_name[0];
                        $attribute_group = Db::getInstance()->executeS(
                            'SELECT ag.* FROM ' . _DB_PREFIX_ . 'attribute_group ag
                            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_lang agl ON (ag.id_attribute_group = agl.id_attribute_group)
                            ' . Shop::addSqlAssociation('attribute_group', 'ag') . '
                            WHERE agl.`name` = \'' . $attribute_group_name . '\''
                        );
                        $type_1 = 0;
                    }
                    if (empty($attribute_group)) {
                        $error_text .= $sku . ': Attribute group not found' . PHP_EOL;
                        break;
                    }
                    $attribute_group_id = $attribute_group[0]['id_attribute_group'];
                    $attribute_values = explode(';', $value);
                    foreach ($attribute_values as $attribute_value) {
                        //根据：分隔
                        $attribute_value_all = explode(':', $attribute_value);

                        $size_name = strtolower($attribute_value_all[0]);
                        if ($attribute_group_id == 1) {
                            $size_name = explode('-', $size_name);
                            $size_name = $size_name[0]; //只保留US部分
                        }
                        if (isset($attribute_value_all[1]) && $groupedAttributes[$attribute_group_id][$size_name]) {
                            $id_attribute = $groupedAttributes[$attribute_group_id][$size_name];
                            $price = $attribute_value_all[1];
                            //更新product_has_attribute
                            // Db::getInstance()->update('product_has_attribute', ['price' => $price], 'id_product = ' . (int)$id_product . ' AND id_attribute = ' .(int)$id_attribute);
                            // 收集要更新的数据
                            $updateData[] = [
                                'id_product' => $id_product,
                                'id_attribute' => $id_attribute,
                                'price' => $price
                            ];
                        }
                    }
                }
                $productRedisIds[] = $id_product;
            }
            $ids = implode(',', $productRedisIds);
            // Db::getInstance()->update('product', ['is_update_attribute' => 1, 'is_update' => 1], 'id_product IN (' . $ids . ')');
            // 批量更新
            if (!empty($updateData)) {
                // // 批量更新
                // $sql = "UPDATE " . _DB_PREFIX_ . "product_has_attribute SET price = CASE ";
                // foreach ($updateData as $update) {
                //     $sql .= "WHEN id_product = " . (int)$update['id_product'] . " AND id_attribute = " . (int)$update['id_attribute'] . " THEN " . (float)$update['price'] . " ";
                // }
                // $sql .= "END WHERE ";

                // $whereClauses = [];
                // foreach ($updateData as $update) {
                //     $whereClauses[] = "(id_product = " . (int)$update['id_product'] . " AND id_attribute = " . (int)$update['id_attribute'] . ")";
                // }

                // $sql .= implode(' OR ', $whereClauses);
                // // 批量更新
                // Db::getInstance()->execute($sql);
                // 批量更新或插入
                $insertValues = [];
                foreach ($updateData as $update) {
                    // 如果存在记录，就更新价格和类型；如果没有记录，就插入新记录
                    $insertValues[] = "(" . (int) $update['id_product'] . ", " . (int) $update['id_attribute'] . ", " . (float) $update['price'] . ", 0)";
                }

                $insertSql = "INSERT INTO " . _DB_PREFIX_ . "attribute_prices (id_product, id_attribute, price, type) VALUES " . implode(', ', $insertValues);

                // 更新部分
                $insertSql .= " ON DUPLICATE KEY UPDATE price = VALUES(price), type = 0";

                // 执行批量插入或更新
                Db::getInstance()->execute($insertSql);
            }
        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }
        //更新ES
        ProductCore::updateRedisEsData($productRedisIds, 'updatePrice');
        if ($error_text != '') {
            $this->addFlash('error', $error_text);
        } else {
            $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.');
        }
        // Tools::clearProductSearchRedis($productIds);
        return $this->redirectToRoute('admin_product_manages_index');
    }

    //更新产品上下架状态
    public function updateProductStatusAction(Request $request): Response
    {
        Tools::unlimit();
        $headers = ['sku', 'status'];
        $error_text = '';
        $productIds = [];
        try {
            $file = $request->files->get('product_active_file');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];
            $data_header_values = array_values($data_header);
            // 整理表头数据
            //将文件上传到指定文件夹
            $this->uploadedFile($file, "product_active");
            $productRedisIds = [];
            // 验证表头
            $isSame = count($headers) === count($data_header_values) && empty(array_diff($headers, $data_header_values)) && empty(array_diff($data_header_values, $headers));
            if (!$isSame) {
                //表头验证不通过
                $error_text .= "文件表头验证不正确";
            } else {
                $header = [];
                foreach ($data_header as $k => $v) {
                    $header[$v] = $k;
                }
                $header = array_keys($header);
                // 去掉表头
                unset($data[1]);
                $data = array_map(fn($raw) => array_combine($header, $raw), $data);
                // 获取商品信息
                $products = Db::getInstance()->executeS((new \DbQueryCore())->from('product', 'p')
                    ->where('p.supplier_reference IN ( "' . implode('","', array_column($data, 'sku')) . '")')
                    ->select('UPPER(p.supplier_reference) AS supplier_reference, p.id_product'));

                foreach ($products as $product) {
                    $productIds[$product['supplier_reference']] = $product['id_product'];
                }
                foreach ($data as $datum) {
                    // 产品sku转大写
                    $sku = strtoupper($datum['sku']);
                    // 无法匹配商品直接跳过
                    if (!isset($sku) || !isset($productIds[$sku])) {
                        $error_text .= $sku . ': SKU not found' . PHP_EOL;
                        continue;
                    }
                    $id_product = $productIds[$sku];
                    $productRedisIds[] = $id_product;
                    if ($datum['status'] == '1') {
                        Db::getInstance()->execute("UPDATE " . _DB_PREFIX_ . "product SET active = 1,is_update = 1 WHERE id_product = " . (int) $id_product);
                    } else {
                        Db::getInstance()->execute("UPDATE " . _DB_PREFIX_ . "product SET active = 0,is_update = 1 WHERE id_product = " . (int) $id_product);
                    }
                    // 生成操作日志无需更新redis
                    ProductCore::addOperateData($id_product, '产品上下架');
                }
            }
            //更新ES
            ProductCore::updateRedisEsData($productRedisIds, 'updateActive');
        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }

        if ($error_text != '') {
            $this->addFlash('error', $error_text);
        } else {
            $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.');
        }
        // Tools::clearProductSearchRedis($productIds);
        return $this->redirectToRoute('admin_product_manages_index');
    }

    //批量更新产品材质
    public function updateProductFeaturesAction(Request $request): Response
    {
        Tools::unlimit();
        $headers = ['sku', 'name', 'description', 'feature_value', 'feature'];
        $error_text = '';
        $productIds = [];
        try {
            $file = $request->files->get('product_features_file');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];
            $data_header_values = array_values($data_header);
            // 整理表头数据
            //将文件上传到指定文件夹
            $this->uploadedFile($file, "product_features");
            $productRedisIds = [];
            // 验证表头
            $isSame = count($headers) === count($data_header_values) && empty(array_diff($headers, $data_header_values)) && empty(array_diff($data_header_values, $headers));
            if (!$isSame) {
                //表头验证不通过
                $error_text .= "文件表头验证不正确";
            } else {
                $header = [];
                foreach ($data_header as $k => $v) {
                    $header[$v] = $k;
                }
                $header = array_keys($header);
                // 去掉表头
                unset($data[1]);
                $data = array_map(fn($raw) => array_combine($header, $raw), $data);
                // 获取商品信息
                $products = Db::getInstance()->executeS((new \DbQueryCore())->from('product', 'p')
                    ->where('p.supplier_reference IN ( "' . implode('","', array_column($data, 'sku')) . '")')
                    ->select('UPPER(p.supplier_reference) AS supplier_reference, p.id_product'));

                foreach ($products as $product) {
                    $productIds[$product['supplier_reference']] = $product['id_product'];
                }

                $featureMap = Db::getInstance()->executeS(
                    (new \DbQueryCore())
                        ->from('feature')
                        ->where('code IN ("' . implode('","', array_column($data, 'feature')) . '")')
                        ->select('code, id_feature')
                );
                if (!$featureMap) {
                    $error_text .= "材质信息未找到";
                    $this->addFlash('error', $error_text);
                    return $this->redirectToRoute('admin_product_manages_index');
                }
                // 创建映射数组：code => id_feature
                $codeToId = array_column($featureMap, 'id_feature', 'code');

                //获取material id
                $materialMap = Db::getInstance()->executeS(
                    (new \DbQueryCore())
                        ->from('material_lang')
                        ->select('name, id_material')
                );
                if (!$materialMap) {
                    $error_text .= "产品材质信息未找到";
                    $this->addFlash('error', $error_text);
                    return $this->redirectToRoute('admin_product_manages_index');
                }
                // 创建映射数组：name => id_material
                $materialName = [];
                foreach ($materialMap as $value) {
                    $NameValue = strtoupper(trim($value['name']));
                    $materialName[$NameValue] = $value['id_material'];
                }

                // 反转为 id_feature => code（用于后面分组时用 code 做键）
                $idToCode = array_flip($codeToId);
                $feature_values = Db::getInstance()->executeS(
                    (new \DbQueryCore())
                        ->from('feature_value', 'fv')
                        ->leftJoin('feature_value_lang', 'fvl', 'fvl.id_feature_value = fv.id_feature_value')
                        ->where('fv.id_feature IN (' . implode(',', array_map('intval', $codeToId)) . ')')
                        ->select('fv.id_feature, fv.id_feature_value, fvl.value')
                );

                $grouped = [];
                foreach ($feature_values as $value) {
                    $code = $idToCode[$value['id_feature']] ?? 'unknown';
                    $upperValue = strtoupper(trim($value['value']));
                    $grouped[$code][$upperValue] = $value['id_feature_value'];
                }

                foreach ($data as $datum) {
                    // 产品sku转大写
                    $sku = strtoupper($datum['sku']);
                    // 无法匹配商品直接跳过
                    if (!isset($sku) || !isset($productIds[$sku])) {
                        $error_text .= $sku . ': 此产品未找到' . PHP_EOL;
                        continue;
                    }
                    $id_product = $productIds[$sku];
                    $productRedisIds[] = $id_product;
                    $name = $datum['name'];
                    //转大写
                    $feature_value = strtoupper($datum['feature_value']);
                    $feature = $datum['feature'];
                    $description = $datum['description'];
                    if ($feature_value != '' && $feature != '' && isset($grouped[$feature][$feature_value])) {
                        $feature_value_id = $grouped[$feature][$feature_value];
                        $feature_id = $codeToId[$feature];
                        //更新产品对应材质信息
                        DB::getInstance()->execute("UPDATE " . _DB_PREFIX_ . "feature_product SET id_feature_value = " . (int) $feature_value_id . " WHERE id_product = " . (int) $id_product . " AND id_feature = " . (int) $feature_id);
                        //更新产品名称
                        Db::getInstance()->execute("UPDATE " . _DB_PREFIX_ . "product_lang SET name = '" . pSQL($name) . "', description = '" . pSQL($description) . "' WHERE id_product = " . (int) $id_product . " AND id_lang = " . (int) $idLang);
                    } else {
                        //材质信息未找到
                        $error_text .= $sku . ': 材质信息未找到' . PHP_EOL;
                        continue;
                    }
                    //同步数据状态
                    // 生成操作日志无需更新redis
                    // ProductCore::addOperateData($id_product, '产品材质修改');
                    $id_material = $materialName[$feature_value];
                    Db::getInstance()->execute("UPDATE " . _DB_PREFIX_ . "product SET is_update = 1,id_material = " . (int) $id_material . " WHERE id_product = " . (int) $id_product);
                }
            }
            //更新ES
            ProductCore::updateRedisEsData($productRedisIds, 'updatePrice');
        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }
        if ($error_text != '') {
            $this->addFlash('error', $error_text);
        } else {
            $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.');
        }
        Tools::clearProductSearchRedis($productIds);
        return $this->redirectToRoute('admin_product_manages_index');
    }

    public function saveProductAttributesFeaturesAction(Request $request): Response
    {
        Tools::unlimit();
        $headers = ['sku', 'Feature'];
        $error_text = '';
        $productIds = [];
        try {
            $file = $request->files->get('product_attributes_features_file');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];
            $data_header_values = array_values($data_header);
            // print_r($data_header_values);exit;
            // 整理表头数据
            //将文件上传到指定文件夹
            $this->uploadedFile($file, "product_fature");
            // 验证表头
            $isSame = count($headers) === count($data_header_values) && empty(array_diff($headers, $data_header_values)) && empty(array_diff($data_header_values, $headers));
            if (!$isSame) {
                //表头验证不通过
                $error_text .= "文件表头验证不正确";
            } else {
                $header = [];
                foreach ($data_header as $k => $v) {
                    $header[$v] = $k;
                }
                $header = array_keys($header);
                // 去掉表头
                unset($data[1]);
                $data = array_map(fn($raw) => array_combine($header, $raw), $data);
                // 获取商品信息
                $products = Db::getInstance()->executeS((new \DbQueryCore())->from('product', 'p')
                    ->where('p.supplier_reference IN ( "' . implode('","', array_column($data, 'sku')) . '")')
                    ->select('UPPER(p.supplier_reference) AS supplier_reference, p.id_product'));

                foreach ($products as $product) {
                    $productIds[$product['supplier_reference']] = $product['id_product'];
                }

                $product_features_ids = [];

                foreach ($data as $datum) {
                    // 产品sku转大写
                    $sku = strtoupper($datum['sku']);
                    // 无法匹配商品直接跳过
                    if (!isset($sku) || !isset($productIds[$sku])) {
                        $error_text .= $sku . ': SKU not found' . PHP_EOL;
                        continue;
                    }
                    $id_product = $productIds[$sku];

                    if ($datum['Feature']) {
                        $attribute_feature = Db::getInstance()->getValue(
                            "SELECT id_feature FROM " . _DB_PREFIX_ . "feature_product
                            WHERE id_product = " . $id_product . " and id_feature=571"
                        );

                        if ($attribute_feature) {
                            Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'feature_product` WHERE `id_feature` = 571 AND `id_product` = ' . $id_product);

                            $fature_value = Db::getInstance()->getValue(
                                "SELECT id_feature_value FROM " . _DB_PREFIX_ . "feature_value
                                WHERE id_feature = 571 and url_name='" . $datum['Feature'] . "'"
                            );
                            $attribute_feature_product = [
                                'id_feature' => 571,
                                'id_feature_value' => $fature_value,
                                'id_product' => $id_product,
                            ];
                            Db::getInstance()->insert('feature_product', $attribute_feature_product);
                        } else {
                            $fature_value = Db::getInstance()->getValue(
                                "SELECT id_feature_value FROM " . _DB_PREFIX_ . "feature_value
                                WHERE id_feature = 571 and url_name='" . $datum['Feature'] . "'"
                            );
                            $attribute_feature_product = [
                                'id_feature' => 571,
                                'id_feature_value' => $fature_value,
                                'id_product' => $id_product,
                            ];
                            Db::getInstance()->insert('feature_product', $attribute_feature_product);
                        }

                    } else {
                        //材质信息未找到
                        $error_text .= $sku . ': 材质信息未找到' . PHP_EOL;
                        continue;
                    }
                    $product_features_ids[] = $id_product;
                    // 生成操作日志无需更新redis
                    // ProductCore::addOperateData($id_product, '产品Feature修改');
                }
                if (isset($product_features_ids)) {
                    ProductCore::updateRedisEsData($product_features_ids, 'updateActive');
                }
            }
        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }

        if ($error_text != '') {
            $this->addFlash('error', $error_text);
        } else {
            $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.');
        }
        Tools::clearProductSearchRedis($productIds);
        return $this->redirectToRoute('admin_product_manages_index');
    }

    public function updateProductCategoryAction(Request $request): Response
    {
        Tools::unlimit();
        $headers = ['sku', 'category_id'];
        $error_text = '';
        $productIds = [];
        try {
            $file = $request->files->get('product_category_file');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];
            $data_header_values = array_values($data_header);
            // print_r($data_header_values);exit;
            // 整理表头数据
            //将文件上传到指定文件夹
            $this->uploadedFile($file, "product_category");
            // 验证表头
            $isSame = count($headers) === count($data_header_values) && empty(array_diff($headers, $data_header_values)) && empty(array_diff($data_header_values, $headers));
            if (!$isSame) {
                //表头验证不通过
                $error_text .= "文件表头验证不正确";
            } else {
                $header = [];
                foreach ($data_header as $k => $v) {
                    $header[$v] = $k;
                }
                $header = array_keys($header);
                // 去掉表头
                unset($data[1]);
                $data = array_map(fn($raw) => array_combine($header, $raw), $data);
                // 获取商品信息
                $products = Db::getInstance()->executeS((new \DbQueryCore())->from('product', 'p')
                    ->where('p.supplier_reference IN ( "' . implode('","', array_column($data, 'sku')) . '")')
                    ->select('UPPER(p.supplier_reference) AS supplier_reference, p.id_product'));

                foreach ($products as $product) {
                    $productIds[$product['supplier_reference']] = $product['id_product'];
                }
                $product_ids = [];
                foreach ($data as $datum) {
                    // 产品sku转大写
                    $sku = strtoupper($datum['sku']);
                    // 无法匹配商品直接跳过
                    if (!isset($sku) || !isset($productIds[$sku])) {
                        $error_text .= $sku . ': SKU not found' . PHP_EOL;
                        continue;
                    }
                    $id_product = $productIds[$sku];

                    if ($datum['category_id']) {
                        Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'category_product` WHERE `id_product` = ' . $id_product);
                        $catearr = explode(",", (string) $datum['category_id']);
                        if (!empty($catearr)) {
                            foreach ($catearr as $cat) {
                                $category = Db::getInstance()->getValue(
                                    "SELECT id_category FROM " . _DB_PREFIX_ . "category_product
                                    WHERE id_product = " . $id_product . " and id_category=" . $cat
                                );
                                if (!$category && empty($category)) {
                                    $category_product = [
                                        'id_category' => $cat,
                                        'id_product' => $id_product,
                                        'position' => 1,
                                    ];
                                    Db::getInstance()->insert('category_product', $category_product);
                                }
                            }
                        }

                    } else {
                        //材质信息未找到
                        $error_text .= $sku . ': 分类未找到' . PHP_EOL;
                        continue;
                    }
                    $product_ids[] = $id_product;
                    // 生成操作日志无需更新redis
                    // ProductCore::addOperateData($id_product, '产品分类修改');
                }
                if (isset($product_ids)) {
                    ProductCore::updateRedisEsData($product_ids, '产品分类修改');
                }


            }
        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }

        if ($error_text != '') {
            $this->addFlash('error', $error_text);
        } else {
            $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.');
        }
        Tools::clearProductSearchRedis($productIds);
        return $this->redirectToRoute('admin_product_manages_index');
    }

    public function updateProductAttributesFeaturesAction(Request $request): Response
    {
        Tools::unlimit();
        // $headers = ['sku','Feature'];
        $error_text = '';
        $productIds = [];
        try {
            $file = $request->files->get('product_attributes_features_all');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];

            $data_header_values = array_values($data_header);

            // 整理表头数据
            //将文件上传到指定文件夹
            $this->uploadedFile($file, "product_fature_all");
            // 验证表头
            if ($data_header['A'] !== 'sku(feature)') {
                throw new Exception("sku 表头不正确，请重新填写 例：sku(feature)");
            }

            $fature_code = Db::getInstance()->getValue(
                "SELECT code FROM " . _DB_PREFIX_ . "feature
                WHERE code='" . $data_header['B'] . "'"
            );

            if (!$fature_code) {
                //表头验证不通过
                $error_text .= "文件表头验证不正确,不存在的Feature属性！";
            } else {
                $header = [];
                foreach ($data_header as $k => $v) {
                    $header[$v] = $k;
                }
                $header = array_keys($header);
                // 去掉表头
                unset($data[1]);
                $data = array_map(fn($raw) => array_combine($header, $raw), $data);

                // 获取商品信息
                $products = Db::getInstance()->executeS((new \DbQueryCore())->from('product', 'p')
                    ->where('p.supplier_reference IN ( "' . implode('","', array_column($data, 'sku(feature)')) . '")')
                    ->select('UPPER(p.supplier_reference) AS supplier_reference, p.id_product'));

                foreach ($products as $product) {
                    $productIds[$product['supplier_reference']] = $product['id_product'];
                }

                $product_features_all_ids = [];
                $attribute_feature_product = [];
                foreach ($data as $key => $datum) {
                    // 产品sku转大写
                    $sku = strtoupper($datum['sku(feature)']);
                    // 无法匹配商品直接跳过
                    if (!isset($sku) || !isset($productIds[$sku])) {
                        $error_text .= $sku . ': SKU not found' . PHP_EOL;
                        continue;
                    }
                    $id_product = $productIds[$sku];

                    foreach ($datum as $kk => $val) {
                        if ($kk != 'sku(feature)') {
                            $feature = Db::getInstance()->getValue(
                                "SELECT id_feature FROM " . _DB_PREFIX_ . "feature
                                WHERE   code='" . $kk . "'"
                            );
                            $feature_product = Db::getInstance()->getValue(
                                "SELECT id_feature FROM " . _DB_PREFIX_ . "feature_product
                                WHERE  id_product='" . $id_product . "' and id_feature='" . $feature . "'"
                            );
                            if ($feature_product) {
                                Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'feature_product` WHERE `id_feature` = ' . $feature_product . ' AND `id_product` = ' . $id_product);
                            }

                            if ($val) {
                                $feature_value = explode(',', $val);
                                for ($c = 0; $c < count($feature_value); $c++) {
                                    $id_feature_value = Db::getInstance()->executeS(
                                        'SELECT fv.id_feature,fvl.id_feature_value FROM ' . _DB_PREFIX_ . 'feature_value fv
                                        LEFT JOIN ' . _DB_PREFIX_ . 'feature_value_lang fvl ON (fv.id_feature_value = fvl.id_feature_value)
                                        WHERE  fv.id_feature = \'' . $feature . '\' and fvl.value=\'' . $feature_value[$c] . '\''
                                    );

                                    if ($kk == 'dress_fabric') {
                                        $materials = Db::getInstance()->getValue(
                                            "SELECT id_material FROM " . _DB_PREFIX_ . "material_lang
                                            WHERE  name='" . $feature_value[0] . "'"
                                        );

                                        if ($materials) {
                                            Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'product` SET `id_material` = ' . $materials . ' WHERE `id_product`  =' . $id_product);

                                        } else {
                                            $error_text .= $sku . ': 材质属性不匹配！' . PHP_EOL;
                                        }
                                        if ($id_feature_value) {
                                            $attribute_feature_product[] = [
                                                'id_feature' => $id_feature_value[0]['id_feature'],
                                                'id_feature_value' => $id_feature_value[0]['id_feature_value'],
                                                'id_product' => $id_product,
                                            ];
                                        } else {
                                            //材质信息未找到
                                            $error_text .= $feature_value[$c] . ': 材质属性未找到' . PHP_EOL;
                                            continue;
                                        }

                                    } else {
                                        if ($id_feature_value) {
                                            $attribute_feature_product[] = [
                                                'id_feature' => $id_feature_value[0]['id_feature'],
                                                'id_feature_value' => $id_feature_value[0]['id_feature_value'],
                                                'id_product' => $id_product,
                                            ];
                                        } else {
                                            //材质信息未找到
                                            $error_text .= $feature_value[$c] . ': 材质属性未找到' . PHP_EOL;
                                            continue;
                                        }
                                    }
                                }
                            } else {
                                //材质信息未找到
                                $error_text .= $sku . ': 材质信息未找到' . PHP_EOL;
                                continue;
                            }
                        }
                    }

                    $product_features_all_ids[] = $id_product;

                }

                Db::getInstance()->insert('feature_product', $attribute_feature_product);
                if (isset($product_features_all_ids)) {
                    ProductCore::updateRedisEsData($product_features_all_ids, 'updateActive');
                }
            }
        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }

        if ($error_text != '') {
            $this->addFlash('error', $error_text);
        } else {
            $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.');
        }
        Tools::clearProductSearchRedis($productIds);
        return $this->redirectToRoute('admin_product_manages_index');
    }

    //修改产品的基础信息功能
    public function productBasicInformationUpdateAction(Request $request): Response
    {
        Tools::unlimit();
        $headers = ['sku(basc)', 'name', 'description', 'short_description', 'model_description', 'meta_title', 'meta_description', 'link_rewrite', 'attribute_set', 'is_trailing', 'video_url'];
        $error_text = '';
        $productIds = [];
        try {
            $file = $request->files->get('product_basic_information');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];
            $data_header_values = array_values($data_header);
            // print_r($data_header_values);exit;
            // 整理表头数据
            //将文件上传到指定文件夹
            $this->uploadedFile($file, "product_basic_information");
            // 验证表头
            $isSame = count($headers) === count($data_header_values) && empty(array_diff($headers, $data_header_values)) && empty(array_diff($data_header_values, $headers));
            if (!$isSame) {
                //表头验证不通过
                $error_text .= "文件表头验证不正确";
            } else {
                $header = [];
                foreach ($data_header as $k => $v) {
                    $header[$v] = $k;
                }
                $header = array_keys($header);
                // 去掉表头
                unset($data[1]);
                $data = array_map(fn($raw) => array_combine($header, $raw), $data);
                // 获取商品信息
                $products = Db::getInstance()->executeS((new \DbQueryCore())->from('product', 'p')
                    ->where('p.supplier_reference IN ( "' . implode('","', array_column($data, 'sku(basc)')) . '")')
                    ->select('UPPER(p.supplier_reference) AS supplier_reference, p.id_product'));

                foreach ($products as $product) {
                    $productIds[$product['supplier_reference']] = $product['id_product'];
                }

                $product_basc_information = [];
                foreach ($data as $datum) {
                    $product_information = [];
                    $product_information_p = [];
                    // 产品sku转大写
                    $sku = strtoupper($datum['sku(basc)']);
                    // 无法匹配商品直接跳过
                    if (!isset($sku) || !isset($productIds[$sku])) {
                        $error_text .= $sku . ': SKU not found' . PHP_EOL;
                        continue;
                    }
                    $id_product = $productIds[$sku];

                    if ($datum['name']) {
                        $product_information['name'] = pSQL($datum['name']);
                    }

                    if ($datum['description']) {
                        $product_information['description'] = pSQL($datum['description'], true);

                    }

                    if ($datum['short_description']) {
                        $product_information['description_short'] = pSQL($datum['short_description'], true);
                    }

                    if ($datum['model_description']) {
                        $product_information['model_description'] = pSQL($datum['model_description'], true);
                    }

                    if ($datum['meta_title']) {
                        $product_information['meta_title'] = pSQL($datum['meta_title']);
                    }

                    if ($datum['meta_description']) {
                        $product_information['meta_description'] = pSQL($datum['meta_description'], true);
                    }

                    if ($datum['link_rewrite']) {
                        $product_information['link_rewrite'] = pSQL($datum['link_rewrite']);
                    }

                    if ($datum['attribute_set']) {
                        $product_information_p['attribute_set'] = pSQL($datum['attribute_set']);
                    }

                    if ($datum['is_trailing']) {
                        if ($datum['is_trailing'] == 'offline') {
                            $product_information_p['is_trailing'] = 0;

                        } else {
                            $product_information_p['is_trailing'] = 1;
                        }
                    }

                    if ($datum['video_url']) {
                        $product_information_p['video_url'] = pSQL($datum['video_url']);
                    }

                    $where = 'id_product =' . $id_product;
                    $product_basc_information[] = $id_product;
                    if ($product_information) {
                        Db::getInstance()->update('product_lang', $product_information, $where);

                    }

                    if ($product_information_p) {
                        Db::getInstance()->update('product', $product_information_p, $where);
                    }
                }
                if (isset($product_basc_information)) {
                    ProductCore::updateRedisEsData($product_basc_information, 'updateActive');
                }
            }
        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }

        if ($error_text != '') {
            $this->addFlash('error', $error_text);
        } else {
            $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.');
        }
        Tools::clearProductSearchRedis($productIds);
        return $this->redirectToRoute('admin_product_manages_index');
    }

    public function categorygGroupColorUpdateAction(Request $request): Response
    {
        Tools::unlimit();
        $headers = ['category', 'color'];
        $error_text = '';
        $productIds = [];
        try {
            $file = $request->files->get('category_group_color');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];
            $data_header_values = array_values($data_header);
            // print_r($data_header_values);exit;
            // 整理表头数据
            //将文件上传到指定文件夹
            $this->uploadedFile($file, "category_group_color");
            // 验证表头
            $isSame = count($headers) === count($data_header_values) && empty(array_diff($headers, $data_header_values)) && empty(array_diff($data_header_values, $headers));
            if (!$isSame) {
                //表头验证不通过
                $error_text .= "文件表头验证不正确";
            } else {
                $header = [];
                foreach ($data_header as $k => $v) {
                    $header[$v] = $k;
                }
                $header = array_keys($header);
                // 去掉表头
                unset($data[1]);
                $data = array_map(fn($raw) => array_combine($header, $raw), $data);
                // 获取商品信息
                $category = Db::getInstance()->executeS((new \DbQueryCore())->from('category_lang', 'p')
                    ->where('p.name IN ( "' . implode('","', array_column($data, 'category')) . '")')
                    ->select('UPPER(p.name) AS name, p.id_category'));

                foreach ($category as $product) {
                    $categorys[$product['name']] = $product['id_category'];
                }
                $attribute_group_color = [];
                foreach ($data as $datum) {
                    $category_name = strtoupper($datum['category']);
                    // 无法匹配商品直接跳过
                    if (!isset($category_name) || !isset($categorys[$category_name])) {
                        $error_text .= $category_name . ': category_name not found' . PHP_EOL;
                        continue;
                    }
                    $id_category = $categorys[$category_name];
                    if ($datum['color']) {
                        $cat_color = explode(',', $datum['color']);
                        for ($c = 0; $c < count($cat_color); $c++) {
                            $group_color = Db::getInstance()->executeS(
                                'SELECT acgl.id_attribute_color_group, acg.position FROM ' . _DB_PREFIX_ . 'attribute_color_group_lang acgl
                                LEFT JOIN ' . _DB_PREFIX_ . 'attribute_color_group acg ON (acgl.id_attribute_color_group = acg.id_attribute_color_group)
                                WHERE  acgl.name = \'' . $cat_color[$c] . '\''
                            );

                            if ($group_color) {
                                $attribute_group_color[] = [
                                    'color_id' => $group_color[0]['id_attribute_color_group'],
                                    'position' => $group_color[0]['position'],
                                    'cat_id' => $id_category,
                                ];
                            }
                        }
                    }

                }
                Db::getInstance()->insert('category_colors', $attribute_group_color);

            }
        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }

        if ($error_text != '') {
            $this->addFlash('error', $error_text);
        } else {
            $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.');
        }
        Tools::clearProductSearchRedis($productIds);
        return $this->redirectToRoute('admin_product_manages_index');
    }

    //更新48小时发货产品
    public function productShipHrsUpdateAction(Request $request): Response
    {
        Tools::unlimit();
        $headers = ['sku(ship)', 'color', 'size', 'style', 'quantity'];
        $error_text = '';
        $productIds = [];
        try {
            $file = $request->files->get('product_ship_48hrs');
            if (!$file->getFilename()) {
                throw new Exception("Incorrect file format or file upload failure");
            }
            $file_extension = $file->getClientOriginalExtension();
            if ($file_extension != 'xls' && $file_extension != 'xlsx' && $file_extension != 'csv') {
                throw new Exception("Upload file type error");
            }
            $idLang = (int) Context::getContext()->language->id;
            $file_path = $file->getPathname();
            // 读取文件数据
            $data = Tools::read_excel($file_path, 0);
            $data_header = $data[1];
            $data_header_values = array_values($data_header);
            // print_r($data_header_values);exit;
            // 整理表头数据
            //将文件上传到指定文件夹
            $this->uploadedFile($file, "product_ship_48hrs");
            // 验证表头
            $isSame = count($headers) === count($data_header_values) && empty(array_diff($headers, $data_header_values)) && empty(array_diff($data_header_values, $headers));
            if (!$isSame) {
                //表头验证不通过
                $error_text .= "文件表头验证不正确";
            } else {
                $header = [];
                foreach ($data_header as $k => $v) {
                    $header[$v] = $k;
                }
                $header = array_keys($header);
                // 去掉表头
                unset($data[1]);
                $data = array_map(fn($raw) => array_combine($header, $raw), $data);
                // 获取商品信息
                $products = Db::getInstance()->executeS((new \DbQueryCore())->from('product', 'p')
                    ->where('p.supplier_reference IN ( "' . implode('","', array_column($data, 'sku(ship)')) . '")')
                    ->select('UPPER(p.supplier_reference) AS supplier_reference, p.id_product'));

                foreach ($products as $product) {
                    $productIds[$product['supplier_reference']] = $product['id_product'];
                }

                $product_arr = [];
                foreach ($data as $datum) {
                    // 产品sku转大写
                    $sku = strtoupper($datum['sku(ship)']);
                    // 无法匹配商品直接跳过
                    if (!isset($sku) || !isset($productIds[$sku])) {
                        $error_text .= $sku . ': SKU not found' . PHP_EOL;
                        continue;
                    }
                    $id_product = $productIds[$sku];

                    $attributes = [
                        'size' => $datum['size'] ? $datum['size'] : '',
                        'color' => $datum['color'] ? $datum['color'] : '',
                    ];
                    if ($datum['style']) {
                        $attributes['style'] = $datum['style'];
                    }

                    $id_product_attribute = Product::getIdAttributeProductByAttributes($id_product, $attributes);

                    if ($id_product_attribute) {
                        $quantity_48hrs = $datum['quantity'];
                        $sql = "update `" . _DB_PREFIX_ . "product_attribute` set ship_in_48hrs=1,quantity_48hrs='$quantity_48hrs',ship_in_status=2 where id_product_attribute='$id_product_attribute'";
                        $result = Db::getInstance()->execute($sql);
                    } else {
                        $sql = "select pa.id_product_attribute from `" . _DB_PREFIX_ . "product_attribute` pa
                        where pa.id_product = '$id_product' AND  NOT EXISTS(SELECT pac.id_attribute FROM `" . _DB_PREFIX_ . "product_attribute_combination` pac 
                        LEFT JOIN `" . _DB_PREFIX_ . "attribute_lang` pal ON(pac.id_attribute = pal.id_attribute)
                        where pa.id_product_attribute = pac.id_product_attribute AND (pal.name ='Custom Size'))";
                        $pro_attribute = Db::getInstance()->executeS($sql);
                        foreach ($pro_attribute as $pat) {
                            $id_product_attributes = $pat['id_product_attribute'];
                            if ($id_product_attributes) {
                                $quantity_48hrs = $datum['quantity'];
                                $sql = "update `" . _DB_PREFIX_ . "product_attribute` set ship_in_48hrs=1,quantity_48hrs='$quantity_48hrs',ship_in_status=2 where id_product_attribute ='$id_product_attributes'";
                                $result = Db::getInstance()->execute($sql);
                            }

                        }
                    }

                    $sql = "update `" . _DB_PREFIX_ . "product` set is_ship_in_48hrs=1, is_update_attribute=1 where id_product=" . $id_product;
                    $result = Db::getInstance()->execute($sql);
                    $product_arr[] = $id_product;
                }

                if (isset($product_arr)) {
                    ProductCore::updateRedisEsData($product_arr, 'updateActive');
                }
            }
        } catch (\Throwable $e) {
            $this->addFlash('error', $e->getMessage());
        }

        if ($error_text != '') {
            $this->addFlash('error', $error_text);
        } else {
            $this->addFlash('success', 'Upload successful. Please note, updates may take approximately 5 minutes to reflect on the frontend.');
        }
        Tools::clearProductSearchRedis($product_arr);
        return $this->redirectToRoute('admin_product_manages_index');
    }

    //保存上传的文件到指定文件夹
    public function uploadedFile($file, $folder)
    {
        $file_name = $file->getClientOriginalName();
        // 创建目标目录结构 年月/日
        $year = date('Y');
        $month = date('m');
        $day = date('d');
        $file_path = _PS_UPLOAD_DIR_ . "product_tool/" . $folder . "/" . "$year$month/$day/";
        // 确保目录存在，如果不存在则创建
        if (!is_dir($file_path)) {
            if (!mkdir($file_path, 0777, true)) {
                echo "Error creating directory $file_path." . PHP_EOL;
                exit;
            }
        }
        $file_path = realpath($file_path);
        // 生成文件名，添加时分秒前缀
        $time_prefix = date('His'); // 获取时分秒
        $new_file_name = $time_prefix . "_" . $file_name; // 生成新文件名

        // 保存文件到目标目录
        $file->move($file_path, $new_file_name);
        return true;
    }
}
