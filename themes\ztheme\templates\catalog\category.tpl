{extends file=$layout}
{block name='breadcrumb'} {* 面包屑导航 *}

{/block}
{block name="left_column"}
  <div id="search_filters_wrapper" class="hidden-sm-down" style="display:flex;padding:0 30px">
    <div id="search_filter_controls" class="hidden-md-up" style="display: none;">
      <span id="_mobile_search_filters_clear_all"></span>
      <button class="btn btn-secondary ok">
        <i class="material-icons rtl-no-flip">&#xE876;</i>
        {l s='OK' d='Shop.Theme.Actions'}
      </button>
    </div>
    <style>
      .facet.clearfix.mobile {
        border-bottom: 1px solid #eee;
      }
      #category .facet-title , #search .facet-title{
        cursor: pointer;
        font-family: 'LoraMedium';
        font-size: 14px !important;
        padding: 15px 0;
        text-transform: uppercase;
        display: block;
        margin-bottom: 0;
        font-weight: 400;
      }
      #category-sort-dropdown-menu .current {
        color: #fff;
        background-color: #007aff;
        font-weight: 400 !important;
      }
        @media screen and (max-width: 450px) {
          #js-product-list-top {
            display: none;
          }

          .fl-topcolor {
            display: none !important;
          }
        }
      .mobile-fl {
        display: none;
      }

      @media screen and (max-width: 450px) {
        .product-description .product-title a {
          margin-bottom: 8px;
          font-size: 13px;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2 !important;
          overflow: hidden;
          color: black;
        }

        .mobile-fl {
          display: block;
        }

        .computer-fl {
          display: none;
        }

        .class-page .star_content.clearfix {
          margin: 0;
          width: 125px;
        }

        .product-price-and-shipping {
          text-align: left !important;
        }

        .product-miniature .thumbnail-container {
          margin-bottom: 0;
        }

        #js-product-list .products {
          justify-content: left;
        }

        #js-product-list .products .product {
          padding: 0 7px 0 7px !important;
        }

        #footer.js-footer .category-commnet,
        .catalog-category-description {
          display: none;
        }

        .bottom-hidden .class-hiden {
          display: none;
        }
      }
    </style>
    <div id="search_filters"
      style="padding-left:0px;padding-right:10px;min-width:270px;width:270px;height:100vh;overflow-y:scroll;position:sticky;top:0;margin-right:20px;">
      <p class="text-uppercase h6 hidden-sm-down" style="font-size:16px">{l s='Filtrer par' d='Shop.Theme.Actions'}</p>
      {if $removes}
        {block name='facets_clearall_button'}{* 如果有筛选项才会出现的清除模块 *}
          <section id="js-active-search-filters" class="{if isset($removes.filters)}active_filters{else}hide{/if}">
            {* filter *}
            {if isset($removes.filters)}
              <ul style="display: flex;flex-wrap:wrap;gap:8px">
                {foreach from=$removes.filters item="filter"}
                  {block name='active_filters_item'}
                    <li class="filter-block" style="padding:.225rem">
                      {$filter.label}
                      <a class=" " href="{$filter.remove_url}">
                        <i class="material-icons close">&#xE5CD;</i>
                      </a>
                    </li>
                  {/block}
                {/foreach}
              </ul>
            {/if}
          </section>
          {if isset($removes.filters)}
            <div id="_desktop_search_filters_clear_all" class="hidden-sm-down clear-all-wrapper">
              <a href="{$removes.clear_all}" id="clearfacet" class="btn btn-tertiary js-search-filters-clear-all">
                <svg t="1692760310589" style="width: 18px;height:18px" viewBox="0 0 1024 1024" version="1.1"
                  xmlns="http://www.w3.org/2000/svg" p-id="4051" width="16" height="16">
                  <path
                    d="M453.9 733.1c0 20.2-16.4 36.6-36.6 36.6-20.2 0-36.7-16.4-36.7-36.6V385.7c0-20.2 16.4-36.6 36.7-36.6 20.2 0 36.6 16.4 36.6 36.6v347.4z m342.5-383.2c20.2 0 36.7 16.4 36.7 36.6v473c0 55-44.8 99.8-99.8 99.8H291c-55 0-99.8-44.8-99.8-99.8V385.2c0-20.2 16.4-36.6 36.7-36.6 20.2 0 36.7 16.4 36.7 36.6v474.2c0 14.6 11.9 26.5 26.5 26.5h442.3c14.6 0 26.5-11.9 26.5-26.5v-473c-0.2-20.1 16.3-36.5 36.5-36.5z m-153 383.2c0 20.2-16.4 36.6-36.7 36.6-20.2 0-36.6-16.4-36.6-36.6V385.7c0-20.2 16.4-36.6 36.6-36.6 20.2 0 36.7 16.4 36.7 36.6v347.4z m279.2-510.4c20.2 0 36.6 16.4 36.6 36.6 0 20.2-16.4 36.6-36.6 36.6H101.4c-20.2 0-36.7-16.4-36.7-36.6 0-20.2 16.4-36.6 36.7-36.6h152.9v-58.1c0-55 44.8-99.8 99.8-99.8h316.6c54.6 0 99.1 44.8 99.1 99.8v58.1h152.8z m-226.1 0v-58.1c0-14.9-11.3-26.5-25.8-26.5H354.1c-14.6 0-26.5 11.9-26.5 26.5v58.1h368.9z"
                    fill="#333333" p-id="4052"></path>
                </svg>
                {l s='Tout effacer' d='Shop.Theme.Actions'}
              </a>
            </div>
          {/if}
        {/block}
      {/if}
      {if isset($facets) && $facets|@count > 0}
        {foreach from=$facets.left_filters item="facet"}
          <section class="facet clearfix mobile" data-type="{$facet.type}" style="position:relative;">
            <div class="js-zbl-whole">
              <p class="h6 facet-title hidden-sm-down" style="display: inline-block;">{$facet.label}</p>
              <img class="fl-icon-img" style="width: 10px;height:10px;position:absolute;right:10px;top:25px"
                src="/themes/ztheme/assets/img/color/icon-jia-c.svg" alt="">
            </div>
            {assign var=_expand_id value=10|mt_rand:100000}
            {assign var=_collapse value=true}
            {if $facet.type != 'price'}
              {foreach from=$facet.filters item="filter"}
                {if $filter.selected}{assign var=_collapse value=false}{/if}
              {/foreach}
            {/if}
            {if $facet.type == 'price'}{* 价格 *}
              {block name='facet_item_slider'}
              <ul id="facet_45249" class="faceted-slider collapse mobile" data-slider-min="{$facet.properties.min}"
                  data-slider-max="{$facet.properties.max}" data-slider-id="{$facet.type}"
                  data-slider-values="[{$facet.current_selected.min},{$facet.current_selected.max}]"
                  data-slider-unit="{$facet.properties.unit}" data-slider-label="{$facet.type}"
                  data-slider-start-url="{$facet.nextEncodedFacetsURL.first_url}"
                  data-slider-end-url="{$facet.nextEncodedFacetsURL.last_url}" style="padding-left:10px;padding-right:10px">
                  <li>
                    <div id="slider-range_45249" class="pc-price"></div>
                  </li>
                  <div style="display: flex;justify-content: space-between;margin-top:13px"><span class="slider-min"></span><span
                      class="slider-max"></span></div>
                  <div class="price-done"><a href="#" id="go_href">Fait</a></div>
                </ul>
              {/block}
            {else}
              <div class="title hidden-md-up" data-target="#facet_{$_expand_id}" data-toggle="collapse" {if !$_collapse}
                aria-expanded="true" {/if}>
              </div>
              {if in_array($facet.widgetType, ['radio', 'checkbox'])}{* 如果是单||多选框 *}
                {block name='facet_item_other'}
                  <ul id="facet_{$_expand_id}" class="collapse{if !$_collapse} in{/if} mobile">
                    {foreach from=$facet.filters key=filter_key item="filter"}
                      <li>
                        <label class="facet-label{if $filter.selected} active {/if}" for="facet_input_{$_expand_id}_{$filter_key}"
                          data-url="{$filter.nextEncodedFacetsURL}">
                          <span class="custom-checkbox">
                            <input id="facet_input_{$_expand_id}_{$filter_key}" data-search-url="{$filter.nextEncodedFacetsURL}"
                              type="checkbox" {if $filter.selected }checked{/if} class="js-click-search">
                            {if isset($filter.properties.color)}
                              <span class="color" style="background-color:{$filter.properties.color}"></span>
                            {elseif isset($filter.properties.texture)}
                              <span class="color texture" style="background-image:url({$filter.properties.texture})"></span>
                            {else}
                              <span>
                                <i class="material-icons rtl-no-flip checkbox-checked">&#xE5CA;</i></span>
                            {/if}
                          </span>
                          <a href="{$filter.nextEncodedFacetsURL}" class="_gray-darker search-link " rel="nofollow">
                            {$filter.label}
                          </a>
                        </label>
                      </li>
                    {/foreach}
                  </ul>
                {/block}
              {elseif $facet.widgetType == 'checkbox_with_image'}{* 如果是带图片的 *}
                {block name='facet_checkbox_with_image'}
                  <div class="collapse mobile {if $facet.label == 'Length'}zbl-active{/if}">
                    <ul id="facet_{$_expand_id}" class="{* collapse mobile *}{if !$_collapse} in{/if} "
                      style="display: flex;flex-wrap: wrap;justify-content: space-between;">
                      {foreach from=$facet.filters key=filter_key item="filter"}
                        <li style="width: calc(50% - 4px);padding: 0;margin-bottom: 8px;border: 1px solid #fff;">
                          <label class="facet-label{if $filter.selected} active {/if}" for="facet_input_{$_expand_id}_{$filter_key}"
                            style="display:flex;align-items: center;background: #faf4f4;height: 100%;">
                            <a href="{$filter.nextEncodedFacetsURL}" rel="nofollow">
                              <span class="custom-checkbox" style="top:0">
                                <input id="facet_input_{$_expand_id}_{$filter_key}" data-search-url="{$filter.nextEncodedFacetsURL}"
                                  type="checkbox" {if $filter.selected }checked{/if}>
                                {if $filter.properties.image}
                                  <span class="color texture"
                                    style="background-image:url({$filter.properties.image});width:58px;height:58px;border:0;margin:0 0 0 0"></span>
                                {else}
                                  <span style="width:58px;height:58px;border:0;margin:0 0 0 0;background:transparent">
                                    <i class="material-icons rtl-no-flip checkbox-checked">&#xE5CA;</i></span>
                                {/if}
                              </span>
                              <a href="{$filter.nextEncodedFacetsURL}" class="_gray-darker search-link"
                                style="font-size: 13px;text-wrap: wrap;white-space: normal; word-break: break-word; overflow-wrap: break-word; padding-right:8px;text-align: left;">
                                {$filter.label}
                              </a>
                            </a>
                          </label>
                        </li>
                      {/foreach}
                    </ul>
                  </div>
                {/block}
              {/if}
            {/if}
          </section>
        {/foreach}
      {/if}
      
    </div>
    {* 右侧栏 产品*}
    <div id="search_filter_right" class="" style="flex:1;min-width: 1176px;">
      {if $products}
        <div id="js-product-list-top" class="products-selection" style="display: flex; justify-content:space-between;align-items:center ">{* 右上48-sort *}
          <div class="col-lg-5 hidden-sm-down total-products" style="padding-top: 0;padding-left: 0;">
            <h1 id="js-product-list-header" style="font-size:16px;text-transform: uppercase;font-family: LoraMedium;">{$label}</h1>
          </div>
          <div class="col-lg-7 phone-filter" style="padding-right: 0;">
            {if isset($facets.ship_in_48hrs) && $facets.ship_in_48hrs}
              <div class="ship_hours">
                <ol>
                  <li class="">
                    <a href="{$facets.ship_in_48hrs.nextEncodedFacetsURL}" title="ship in 48hrs">
                      <span class="checkSpan attr_ship_in_48hrs"></span><span
                        class="displaySpan text_ship_in_48hrs" style="white-space: nowrap;">{$facets.ship_in_48hrs.label}</span>
                    </a>
                  </li>
                </ol>
              </div>
            {/if}
            <div class="sort-by-row">
              {block name='sort_by'}
                <span class="col-sm-3 col-md-5 hidden-sm-down sort-by" style="text-transform: uppercase;">{l s='Sort by:' d='Shop.Theme.Global'}</span>
                <div
                  class="{if !empty($orders)}col-xs-8 col-sm-7{else}col-xs-12 col-sm-12{/if} col-md-9 products-sort-order dropdown fl-sort-by"
                  style="margin-right: 0;">

                  <button id="filter_dropdown-cate" style="padding: 0;line-height:30px;height: 30px;padding-left:10px;width:160px;float:right;"
                    class="btn-unstyle select-title shouye-details" rel="nofollow" data-toggle="dropdown"
                    aria-label="{l s='Sort by selection' d='Shop.Theme.Global'}" aria-haspopup="true" aria-expanded="false">
                    {foreach from=$orders item=sort_order}
                      {if $sort_order['selected']==1}{$sort_order['label']}{/if}
                    {/foreach}
                    <i style="float:right" class="material-icons float-xs-right">&#xE5C5;</i>
                  </button>
                  <div id="category-sort-dropdown-menu" class="dropdown-menu" style="width: calc(100% - 30px);background-color: #fff;">
                    {foreach from=$orders item=sort_order}
                      <a rel="nofollow" onclick="closeSort()" href="{$sort_order['nextURL']}" class="select-list {if $sort_order['selected']}current{/if}" style="font-size: 14px;height:28px;line-height:28px;padding: 0 0 0 10px" data-ifselected="{$sort_order['selected']}">
                        {$sort_order['label']}
                      </a>
                    {/foreach}
                  </div>
                </div>
              {/block}
              {if !empty($orders)}
                <div class="col-xs-4 col-sm-3 hidden-md-up filter-button">
                  <button id="search_filter_toggler" class="btn btn-secondary js-search-toggler">
                    {l s='Filter' d='Shop.Theme.Actions'}
                  </button>
                </div>
              {/if}
            </div>
          </div>
        </div>
        {if isset($facets.color_group_filters) && $facets.color_group_filters }{* 颜色 *}
          <div class="color-category" style="display:flex;flex-wrap: wrap;justify-content: center;">
            {foreach from=$facets.color_group_filters.filters item="facet"}
              <a href="{$facet.nextEncodedFacetsURL}">
                <div class="color-family-item All-colors-box">
                    <div class="color-img-box">
                      <div class="color-img-border {if $facet.selected}checkedall{/if}">
                        <img src="{$facet.properties.image}">
                      {if $facet.selected}
                        <img style="position: absolute;width:25px;height:25px" src="../../../../themes/ztheme/assets/images/hook.svg">
                      {/if}
                      </div>
                    </div>
                    <div class="color-img-name Color-Highlight">{$facet.label}{if $facet.checkbox_num!=0}({$facet.checkbox_num}){/if}</div>{* 颜色名称 *}
                    <div class="color-list-box family-All-colors-box" style="display: none;">{* 隐藏块 *}
                      {if $facet.label !='ALL' && count($facet.attribute_colors) >1 }
                      <div class="color-list-checked-box">{* 全选 *}
                        <a href="{$facet.nextEncodedFacetsURL}">
                          <label>
                            <input type="checkbox" {if $facet.selected}checked{/if} id="color-list-checkedall" class="color-list-checkedall"
                              data-url="{$facet.nextEncodedFacetsURL}">
                            select all
                          </label>
                        </a>
                      </div>
                      {/if}
                      <div class="color-list-content"> 
                      {foreach from=$facet.attribute_colors item="color"}
                        <a class="color-item-option"
                          href="{$color.nextEncodedFacetsURL}">
                      <div class="color-item-content {if $color.selected} selected {/if}">
                            {if $color.properties.image == ''}
                              <div class="option_circle {$color.properties.color}">
                              </div>
                            {else}
                              <img img src="{$color.properties.image}" class="option_circle">
                            {/if}
                          </div>
                          <div class="color-item-name" style="font-size:10px">{$color.label}</div>
                        </a> 
                      {/foreach}
                        <div class="triangle-box-1"></div>
                        <div class="triangle-box-2"></div>
                      </div>
                    </div>
                </div>  
              </a>
            {/foreach}
          </div>
        {/if}
        {* 子分类筛选 *}
        {if $subcategories|@count > 0}
        <div class="nav_catalog_menus flex flex_yc">
          <div class="catalog_menus_title">
            {$category.name}
          </div>
          <ul class="flex  flex_wrap ">
            {foreach from=$subcategories item=sub}
            <li class="cat_name"><a href="{$sub.url}">{$sub.name}</a></li>
            {/foreach}
          </ul>
        </div>
        {/if}
        {* 商品 项*}
        <div class="products" style="display: flex;flex-wrap: wrap;">
          {foreach from=$products item="product" key="position"}
            <div class="js-product product{if !empty($productClasses)} {$productClasses}{/if}" style="width: 25%;padding: 20px 7px 0px 7px;">
              <article class="product-miniature js-product-miniature" data-id-product="{$product.id_product}">
                <div class="thumbnail-container">
                  <div class="thumbnail-top">{* 商品图片块儿 *}
                    <a href="{$product.url}" class="thumbnail product-thumbnail hover-img" target="_blank">
                      <picture>
                        {* 筛选颜色后的正反面图片 *}
                        {if isset($product.images.color_image_host) && count($product.images.color_image_host)>0}{* 判断是否有颜色图片 *} 
                          {if isset($product.images.color_image_host[0]) && isset($product.images.color_image_host[0].home_default)}
                            <img src="{$product.images.color_image_host[0].home_default}" alt="{$product.name|truncate:30:'...'}" loading="lazy"/>{* 正面有图 *}
                          {else}
                            <img src="{$product.images.product_images[0].home_default}" alt="{$product.name|truncate:30:'...'}" loading="lazy"/>{* 正面无图 *}
                          {/if}
                          {if isset($product.images.color_image_host[1]) && isset($product.images.color_image_host[1].home_default)}
                            <img src="{$product.images.color_image_host[1].home_default}" alt="{$product.name|truncate:30:'...'}" />{* 反面有图 *}
                          {elseif isset($product.images.product_images[1]) && isset($product.images.product_images[1].home_default)}
                            <img src="{$product.images.color_image_host[0].home_default}" alt="{$product.name|truncate:30:'...'}" />{* 反面无筛选图 *}
                          {else}
                            <img src="{$product.images.product_images[0].home_default}" alt="{$product.name|truncate:30:'...'}" />{* 反面无产品图 *}
                          {/if}
                        {else}
                          {* 正常的正反面图片 *}
                            <img src="{$product.images.product_images[0].home_default}" alt="{$product.name|truncate:30:'...'}" loading="lazy"/>
                          {if isset($product.images.product_images[1]) && isset($product.images.product_images[1].home_default)}
                            <img src="{$product.images.product_images[1].home_default}" alt="{$product.name|truncate:30:'...'}" />
                          {else}
                            <img src="{$product.images.product_images[0].home_default}" alt="{$product.name|truncate:30:'...'}" />
                          {/if}
                        {/if}
                        {if $product.is_ship_in_48hrs}
                          <div class="ship_in_hrs">
                            <svg t="1693630146296" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7880" width="50" height="50">
                              <path d="M226.8125 1024l555.3-664.606H551.5995L811.2205 0h-391.21L212.7785 554.9h201z" fill="#ffffff" p-id="7881"></path>
                            </svg>{l s='EXPÉDITION EN 48H' d='Shop.Theme.Global'}
                          </div>
                        {/if}
                      </picture>
                    </a>
                    {*收藏按钮*}
                    {include file='_partials/add-to-sc-icon.tpl' productId=$product.id_product}
                  </div>
                  <div class="product-description">{* 商品信息块儿 *}
                    <h2 class="h3 product-title">
                      <a style="display:-webkit-box;-webkit-line-clamp:1;overflow: hidden;-webkit-box-orient: vertical;text-align:center"
                        href="{$product.url}" content="{$product.url}">{$product.name}
                      </a>
                    </h2>
                    <div class="product-price-and-shipping">
                      <span class="price" aria-label="{l s='Price' d='Shop.Theme.Catalog'}">
                        {$product.price}
                      </span>
                      {if $product.has_discount}
                        <span class="regular-price" aria-label="{l s='Regular price' d='Shop.Theme.Catalog'}">
                          {$product.origin_price}
                        </span>
                      {/if}
                    </div>
                    <div class="class-page computer-fl" style="margin-top: 10px;">
                      {hook h='displayProductListReviews' product=$product}
                    </div>
                  </div>
                </div>
              </article>
            </div>
          {/foreach}
        </div>
        {include file='_partials/ca-sea-pageination.tpl' pagination=$pages}{* 分页 *}
      </div>
    {else}
      <div id="js-product-list-top" class="products-selection">
        <div class="col-lg-5 hidden-sm-down total-products" style="padding-top: 0;">
          <h1 id="js-product-list-header" class="h2" style="font-size: ;">{$label}</h1>
        </div>
      </div>
      <div class="ps-block__content">
        <p>{l s='No products available yet' d='Shop.Theme.Catalog'}</p>
      </div>
    {/if}
  </div>
  <script>
    function closeSort(){
      $("#category-sort-dropdown-menu").css('display','none')
    }
    document.addEventListener("DOMContentLoaded", function() {
      let zblIterm = document.querySelectorAll('.js-zbl-whole');
      let zblInfo = document.querySelectorAll('.collapse.mobile');
      let zblIcon = document.querySelectorAll('.fl-icon-img');
      for (let i = 0; i < zblIterm.length; i++) {
        zblIterm[i].addEventListener('click', function() {
          zblInfo[i].classList.toggle('zbl-active');
          if (zblInfo[i].classList.contains('zbl-active')) {
            zblIcon[i].setAttribute("src", "../../../../../../themes/ztheme/assets/img/color/icon-jian-c.svg");
          } else {
            zblIcon[i].setAttribute("src", "../../../../../../themes/ztheme/assets/img/color/icon-jia-c.svg");
          }
        })
      }
      $("#slider-range_45249").slider({
        range: true,
        min: $("#facet_45249").data('slider-min'),
        max: $("#facet_45249").data('slider-max'),
        values: $("#facet_45249").data('slider-values'),
        slide: function(event, ui) {
          $(".slider-min").text($("#facet_45249").data('slider-unit') + ui.values[0]);
          $(".slider-max").text($("#facet_45249").data('slider-unit') + ui.values[1]);
        }
      });

      $(".slider-min").text($("#facet_45249").data('slider-unit') + $("#slider-range_45249").slider("values", 0));
      $(".slider-max").text($("#facet_45249").data('slider-unit') + $("#slider-range_45249").slider("values", 1));
      /* get slider value */
      $(".pc-price").on("slide", function(event, ui) {
        a = ui.values[0];
        b = ui.values[1];
        $("#go_href").attr("href", $("#facet_45249").data('slider-start-url') + a + "-" + b + $("#facet_45249")
          .data('slider-end-url'));
      });
    });

    document.addEventListener('wheel', function(event) {
        const targetElement = document.getElementById('category-sort-dropdown-menu');
        const body = document.getElementById('category');
        // 检查鼠标是否在目标元素上
        if (!targetElement.contains(event.target)) {
          targetElement.style.display = 'none';
          body.style.overflow = 'auto';
          body.style.overflowY = 'auto';
        } else {
          body.style.overflow = 'hidden';
          body.style.overflowY = 'scroll';
          event.preventDefault();
        }
    }, { passive: false });
    document.addEventListener('DOMContentLoaded', function() {
      const selectBox = document.getElementById('category-sort-dropdown-menu');
      const options = selectBox.querySelectorAll('a');
      let lastSelectedOption = null;{*保存原来选中的*}

      // clear selected options
      function clearSelection() {
        selectBox.classList.remove('current')
        options.forEach(option => option.classList.remove('current'));
      }
      // handle mouseover event
      selectBox.addEventListener('mouseover', function(event) {
        clearSelection();
        if (event.target.classList.contains('select-list')) {
          lastSelectedOption = event.target
        } else if (event.fromElement.classList.contains('select-list')) {
          lastSelectedOption = event.fromElement
        }
      });

      // handle mouseout event
      selectBox.addEventListener('mouseout', function(event) {
        if (lastSelectedOption) {
          clearSelection();
          lastSelectedOption.classList.add('current');
        }
      });
    })
  </script>
{/block}
{block name="content"}
{/block}
{block name='footer'}
  <div class="catalog-category-description" style="position: relative;min-height: 40px;">
    <div class="class-page-info">
      <div class="category-descriptionbuyao std">
        {$category.description nofilter}
      </div>
    </div>
  </div>
  <div class="category-commnet">{* 评论钩子 *}
    {hook h="displayFooterCategory"}
  </div>
  <div class="bottom-hidden">
    {include file="_partials/footer.tpl"}
  </div>
{/block}

{block name='track'}
  {* 跟踪代码 *}
  {include file="_partials/tiktok_track.tpl"}
  {include file='catalog/_partials/category-track.tpl'}
{/block}
