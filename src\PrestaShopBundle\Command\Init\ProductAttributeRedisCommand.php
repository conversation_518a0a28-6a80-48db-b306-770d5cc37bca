<?php
/**
 * 初始化变体选项卡redis
 */

namespace PrestaShopBundle\Command\Init;

use Db;
use PrestaShop\PrestaShop\Adapter\LegacyContext;
use PrestaShop\PrestaShop\Core\CommandBus\CommandBusInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Tools;
use ImportProduct;

class ProductAttributeRedisCommand extends Command
{
    /**
     * @var CommandBusInterface
     */
    private $commandBus;

    /**
     * @var LegacyContext
     */
    private $legacyContext;

    public function __construct(CommandBusInterface $commandBus, LegacyContext $legacyContext)
    {
        parent::__construct();
        $this->commandBus = $commandBus;
        $this->legacyContext = $legacyContext;
    }

    protected function configure()
    {
        // The name of the command (the part after "bin/console")
        $this->setName('init:product-attribute-redis');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        Tools::unlimit();

        $products = Db::getInstance()->executeS('SELECT id_product, id_material FROM `' . _DB_PREFIX_ . 'product`');
        foreach ($products as $product) {
            // 更新选项卡的redis
            ImportProduct::updateQueryProductHasAttributeRedis($product['id_product'], $product['id_material']);
        }

        echo 'success';
        exit;
    }
}