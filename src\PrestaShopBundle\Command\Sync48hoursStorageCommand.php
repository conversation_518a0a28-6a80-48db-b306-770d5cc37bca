<?php
/**
 * 导入产品和分类信息
 */

namespace PrestaShopBundle\Command;

use Exception;
use PrestaShop\PrestaShop\Adapter\LegacyContext;
use PrestaShop\PrestaShop\Core\CommandBus\CommandBusInterface;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Db;
use Product;
use ImportProduct;

class Sync48hoursStorageCommand extends Command
{
    /**
     * @var CommandBusInterface
     */
    private $commandBus;

    /**
     * @var LegacyContext
     */
    private $legacyContext;

    public function __construct(CommandBusInterface $commandBus, LegacyContext $legacyContext)
    {
        parent::__construct();
        $this->commandBus = $commandBus;
        $this->legacyContext = $legacyContext;
    }

    protected function configure()
    {
        // The name of the command (the part after "bin/console")
        $this->setName('sync:48hoursStorage');
    }

    protected function execute(InputInterface $input, OutputInterface $output)
    {
        //先去掉所有的48小时标识
        $sql = "update `". _DB_PREFIX_ ."product` set is_ship_in_48hrs=0";
        Db::getInstance()->execute($sql);
        $sql = "update `". _DB_PREFIX_ ."product_attribute` set ship_in_48hrs=0,quantity_48hrs=0,ship_in_status=0 where ship_in_status=1 OR ship_in_status=0";
        Db::getInstance()->execute($sql);

        //获取48小时库存
        $data = [
            'signature' => md5('WUfYOUWXA2CoIwIDZwNt4TaWvGExcgNTaKpUB7y12UkyQdSrwE-s'),
            'request_function' => 'get_site_stock',
            'data' => json_encode([
                // 'domain_url' => 'www.glamlora.com.au'
                'domain_url' => _PS_DOMAIN_
            ])
        ];
        $url = "https://hunsha.the2016.com/api/api_get_warehouse_stock";
        $result = $this->request($url, $data);
        $storages = json_decode($result, true);
        if ($storages['code'] != 200) {
            echo $storages['result'] ?? '';
            exit;
        }
        $us_size_map = [];
        $productIds = [];
        foreach ($storages['data'] as $storage) {
            try {
                $sku = $storage['sku'];
                $sql = "select id_product from ps_product where supplier_reference='".pSQL($sku)."'";
                $id_product = Db::getInstance()->getValue($sql);
                $total = $storage['quantity'];
                if (!$id_product || $total <= 0) {
                    continue;
                }
                $has_48hrs = false;
                foreach ($storage['detail'] as $detail) {
                    //ERP那边没有针对站点的尺寸颜色对应，所以要全部查一遍
                    $size = $detail['size'];
                    $color = $detail['color'];
                    $site_size = $detail['site_size'];
                    $site_color = $detail['site_color'];
                    $attribute_groups = [
                        [
                            'size' => $size,
                            'color' => $color,
                        ]
                    ];
                    if ($site_size && $site_size != $size) {
                        $attribute_groups[] = [
                            'size' => $site_size,
                            'color' => $color,
                        ];
                    }
                    if ($site_color && $site_color != $color) {
                        $attribute_groups[] = [
                            'size' => $size,
                            'color' => $site_color,
                        ];
                    }
                    $id_product_attribute = 0;
                    foreach ($attribute_groups as $attribute_group) {
                        // 由于不同国家站点的尺码不一样，所以要查找对应尺码名称，只处理US开头的尺寸
                        $size = $attribute_group['size'];
                        if (strpos(strtoupper($size), 'US') === 0) {
                            if (!isset($us_size_map[$size])) {
                                $size = str_replace('US', '', strtoupper($size));
                                $sql = "select al.name from ps_dress_size_chart dsc
                                    left join ps_attribute a on a.code=dsc.size_code
                                    left join ps_attribute_lang al on al.id_attribute=a.id_attribute
                                    where dsc.us='".pSQL($size)."' and dsc.unit_type=1";
                                $new_size = Db::getInstance()->getValue($sql);
                                if ($new_size) {
                                    $us_size_map[$size] = $new_size;
                                }
                            }
                            $size = $us_size_map[$size] ?? $size;
                        }
                        $attributes = [
                            'size' => $size,
                            'color' => $attribute_group['color'],
                        ];
                        $id_product_attribute = Product::getIdAttributeProductByAttributes($id_product, $attributes);
                        if ($id_product_attribute) {
                            break;
                        }
                    }
                    if (!$id_product_attribute) {
                        continue;
                    }
                    $quantity_48hrs = $detail['quantity'];
                    $sql = "update `". _DB_PREFIX_ ."product_attribute` set ship_in_48hrs=1,quantity_48hrs='$quantity_48hrs',ship_in_status=1 where id_product_attribute='$id_product_attribute'";
                    $result = Db::getInstance()->execute($sql);
                    $has_48hrs = true;
                }
                if ($has_48hrs) {
                    $sql = "update `". _DB_PREFIX_ ."product` set is_ship_in_48hrs=1 where id_product='$id_product'";
                    $result = Db::getInstance()->execute($sql);
                    $productIds[] = $id_product;
                }
                ImportProduct::updateQueryProductHasAttributeRedis($id_product);
            } catch (\Throwable $th) {
                echo $th->getMessage();
            }
        }

        //更新手动添加48小时产品的状态
        $sql = "SELECT id_product FROM ps_product_attribute WHERE ship_in_status=2 GROUP BY id_product";
        $p_attribute = Db::getInstance()->executeS($sql);
        $productIds_attr=[];
        if($p_attribute){
            foreach($p_attribute as $prod){
                $sql = "update `". _DB_PREFIX_ ."product` set is_ship_in_48hrs=1,is_update=1 where id_product=".$prod['id_product'];
                $result = Db::getInstance()->execute($sql);
                $productIds_attr[] = $prod['id_product'];
            }

        }
        $product_arr = array_unique(array_merge($productIds, $productIds_attr));

        if(isset($product_arr)){
            //同步ES更新redis
            Product::updateRedisEsData($product_arr,'48小时更新');
        }
    }

    protected function request($url, $data)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, TRUE);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        $response = curl_exec($ch);
        if (curl_errno($ch)) {
            throw new Exception(curl_error($ch));
        }
        curl_close($ch);
        return $response;
    }
}



