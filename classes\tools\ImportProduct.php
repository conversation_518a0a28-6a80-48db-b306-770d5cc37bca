<?php
/**
 * 批量上传产品
 */
class ImportProductCore extends ObjectModel
{
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * 批量插入产品
     * @param mixed $products
     * @param mixed $reserve_id
     * @return bool
     */
    public static function insertProducts($data, $reserve_id = false)
    {
        // 批量插入产品信息
        $products = self::completeProductData($data, $reserve_id);
        return Tools::batch_insert($products, 'product', true);
    }

    /**
     * 补全产品数据
     * @param mixed $products
     * @param mixed $reserve_id
     * @return array
     */
    public static function completeProductData($data, $reserve_id = false)
    {
        $result = [];
        foreach ($data as $product) {
            if ($reserve_id) {
                $item['id_product'] = $product['id_product'];
            }
            $item['supplier_reference'] = pSQl($product['supplier_reference']);
            $item['id_category_default'] = $product['id_category_default'];
            $item['price'] = $product['price'];
            $item['current_price'] = $product['current_price'];
            $item['weight'] = $product['weight'];
            $item['is_trailing'] = $product['is_trailing'];
            $item['id_material'] = $product['id_material'];
            $item['visibility'] = pSQl($product['visibility']);
            $item['type'] = pSQl($product['type']);
            $item['active'] = $product['active'];
            $item['attribute_set'] = pSQl($product['attribute_set']);
            // 以下是要注意的数据，没有会报错
            $item['product_type'] = pSQl($product['product_type'] ?? 'combinations');
            $item['ean13'] = pSQl($product['ean13'] ?? '');
            $item['isbn'] = pSQl($product['isbn'] ?? '');
            $item['upc'] = pSQl($product['upc'] ?? '');
            $item['mpn'] = pSQl($product['mpn'] ?? '');
            $item['reference'] = pSQl($product['reference'] ?? '');
            $item['date_add'] = pSQL($product['date_add'] ?? date('Y-m-d H:i:s'));
            $item['date_upd'] = pSQL($product['date_upd'] ?? date('Y-m-d H:i:s'));
            $item['create_date'] = pSQL($product['create_date'] ?? date('Y-m-d H:i:s'));
            // $item['is_update'] = $product['is_update'] ?? 1;
            $result[] = $item;
        }

        return $result;
    }

    /**
     * 获取新的产品sku与id对应关系
     * @param mixed $skus
     * @return array
     */
    public static function getNewProductSkuToIds($skus)
    {
        $sql = "SELECT id_product, supplier_reference FROM " . _DB_PREFIX_ . "product WHERE supplier_reference IN ('" . implode("','", $skus) . "')";
        $result = Db::getInstance()->executeS($sql);
        return array_column($result, 'id_product', 'supplier_reference');
    }

    /**
     * 导入产品店铺数据
     * @param mixed $data
     * @return bool
     */
    public static function insertProductShops($data)
    {
        // 批量插入产品信息
        $product_shops = self::completeProductShopData($data);
        return Tools::batch_insert($product_shops, 'product_shop');
    }

    /**
     * 补全产品店铺数据
     * @param mixed $data
     * @return array
     */
    public static function completeProductShopData($data)
    {
        $result = [];
        foreach ($data as $product_shop) {
            $item['id_product'] = $product_shop['id_product'];
            $item['id_shop'] = $product_shop['id_shop'];
            $item['id_category_default'] = $product_shop['id_category_default'];
            $item['price'] = $product_shop['price'];
            $item['active'] = $product_shop['active'];
            $item['visibility'] = pSQL($product_shop['visibility']);
            // 以下是要注意的数据，没有会报错
            $item['date_add'] = $product_shop['date_add'] ?? date('Y-m-d H:i:s');
            $item['date_upd'] = $product_shop['date_upd'] ?? date('Y-m-d H:i:s');
            $result[] = $item;
        }

        return $result;
    }

    /**
     * 导入产品语言数据
     * @param mixed $data
     * @return bool
     */
    public static function insertProductLangs($data)
    {
        // 批量插入产品信息
        $product_langs = self::completeProductLangData($data);
        return Tools::batch_insert($product_langs, 'product_lang');
    }

    /**
     * 补全产品语言数据
     * @param mixed $data
     * @return array
     */
    public static function completeProductLangData($data)
    {
        $result = [];
        foreach ($data as $product_lang) {
            $item['id_product'] = $product_lang['id_product'];
            $item['id_shop'] = $product_lang['id_shop'];
            $item['id_lang'] = $product_lang['id_lang'];
            $item['name'] = pSQL($product_lang['name']);
            $item['description'] = pSQL($product_lang['description'] ?? '', true);
            $item['model_description'] = pSQL($product_lang['model_description'] ?? '', true);
            $item['description_short'] = pSQL($product_lang['description_short'] ?? '', true);
            $item['meta_title'] = pSQL($product_lang['meta_title'] ?? '');
            $item['meta_description'] = pSQL($product_lang['meta_description'] ?? '');
            $item['meta_keywords'] = pSQL($product_lang['meta_keywords'] ?? '');
            $item['link_rewrite'] = pSQL($product_lang['link_rewrite'] ?? '');
            $result[] = $item;
        }
        return $result;
    }

    /**
     * 删除特价通过产品ids
     * @param mixed $product_ids
     * @return bool
     */
    public static function deleteSpecificPrice($product_ids)
    {
        if (!$product_ids) {
            return true;
        }
        return Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'specific_price` WHERE `id_product` IN (' . implode(',', $product_ids) . ')');
    }

    /**
     * 导入产品特价
     * @param mixed $data
     * @return bool
     */
    public static function insertSpecificPrices($data)
    {
        // 补充数据
        $specific_prices = self::completeSpecialPriceData($data);
        return Tools::batch_insert($specific_prices, 'specific_price');
    }

    /**
     * 补全产品价格数据
     * @param mixed $data
     * @return array
     */
    public static function completeSpecialPriceData($data)
    {
        $current_data = current($data);
        if (!is_array($current_data)) {
            $data = [$data];
        }
        $result = [];
        foreach ($data as $specific_price) {
            $result[] = [
                'id_product' => $specific_price['id_product'],
                'id_shop' => $specific_price['id_shop'],
                'reduction' => $specific_price['reduction'],
                // 以下是要注意的数据，需要根据实际情况进行补充
                'id_specific_price_rule' => $specific_price['id_specific_price_rule'] ?? 0,
                'id_cart' => $specific_price['id_cart'] ?? 0,
                'id_shop_group' => $specific_price['id_shop_group'] ?? 0,
                'id_currency' => $specific_price['id_currency'] ?? 0,
                'id_country' => $specific_price['id_country'] ?? 0,
                'id_group' => $specific_price['id_group'] ?? 0,
                'id_customer' => $specific_price['id_customer'] ?? 0,
                'id_product_attribute' => $specific_price['id_product_attribute'] ?? 0,
                'price' => $specific_price['price'] ?? '-1.000000',
                'from_quantity' => $specific_price['from_quantity'] ?? 1,
                'reduction_tax' => $specific_price['reduction_tax'] ?? 1,
                'reduction_type' => pSQL($specific_price['reduction_type'] ?? 'amount'),
                'from' => $specific_price['from'] ?? '0000-00-00 00:00:00',
                'to' => $specific_price['to'] ?? '0000-00-00 00:00:00'
            ];
        }

        return $result;
    }

    /**
     * 批量导入产品图片
     * @param mixed $data
     * @param mixed $id_product
     * @param mixed $id_shop
     * @param mixed $languages
     * @return array|bool|mysqli_result|PDOStatement|resource|null
     */
    public static function batchInsertImages($data, $id_product, $id_shop, $languages)
    {
        $insert_image_data = [];
        $insert_cover_data = [];
        foreach ($data as $item) {
            if ($item['cover']) {
                $insert_cover_data[] = [
                    'id_product' => $id_product,
                    'id_color_attribute' => $item['id_color_attribute'] ?? 0,
                    'position' => $item['position'],
                    'type' => $item['type'],
                    'name' => $item['name'],
                    'cover' => 1
                ];
            } else {
                $insert_image_data[] = [
                    'id_product' => $id_product,
                    'id_color_attribute' => $item['id_color_attribute'] ?? 0,
                    'position' => $item['position'],
                    'type' => $item['type'],
                    'name' => $item['name'],
                ];
            }
        }
        // 批量插入图片数据
        Tools::batch_insert($insert_cover_data, 'image', true);
        Tools::batch_insert($insert_image_data, 'image', true);

        // 新增的图片数据
        $new_images = self::getNewImages($id_product);
        $image_langs = [];
        $image_shops = [];
        $cover_shops = [];
        foreach ($new_images as $new_image) {
            foreach ($languages as $language) {
                $image_langs[] = [
                    'id_image' => $new_image['id_image'],
                    'id_lang' => $language['id_lang'],
                    'legend' => '',
                ];
            }
            if ($new_image['cover']) {
                $cover_shops[] = [
                    'id_product' => $id_product,
                    'id_image' => $new_image['id_image'],
                    'id_shop' => $id_shop,
                    'cover' => 1,
                ];
            } else {
                $image_shops[] = [
                    'id_product' => $id_product,
                    'id_image' => $new_image['id_image'],
                    'id_shop' => $id_shop
                ];
            }
        }

        // 批量插入图片语言数据
        Tools::batch_insert($image_langs, 'image_lang');
        // 批量图片店铺数据
        Tools::batch_insert($image_shops, 'image_shop');
        Tools::batch_insert($cover_shops, 'image_shop');

        return $new_images;
    }

    // 获取新添加的图片ID
    public static function getNewImages($id_product)
    {
        $sql = "SELECT * FROM " . _DB_PREFIX_ . "image WHERE id_product = " . $id_product;
        return Db::getInstance()->executeS($sql);
    }

    // 添加产品分类关联
    public static function insertCategoryProduct($data, $id_product)
    {
        // 批量插入产品分类信息
        $category_products = [];
        foreach ($data as $id_category) {
            $key = $id_category . '_' . $id_product;
            $category_products[$key] = [
                'id_product' => $id_product,
                'id_category' => $id_category,
                'position' => 0,
            ];
        }

        return Tools::batch_insert($category_products, 'category_product');
    }

    // 添加产品特性关联
    public static function insertProductFeatures($feature_ids, $id_product)
    {
        $feature_products = [];
        foreach ($feature_ids as $value) {
            $feature_products[] = [
                'id_product' => $id_product,
                'id_feature' => $value['id_feature'],
                'id_feature_value' => $value['id_feature_value'],
            ];
        }

        return Tools::batch_insert($feature_products, 'feature_product');
    }

    /**
     * 添加产品变体
     * @param mixed $id_product
     * @param mixed $combination_attributes
     * @param mixed $attribute_prices
     * @param mixed $id_shop
     * @param mixed $languages
     * @param mixed $old_product_attribute_ids
     * @return array[]
     */
    public static function batchInsertProductAttribute($id_product, $combination_attributes, $attribute_prices, $id_shop, $languages, $old_product_attribute_ids = [])
    {
        try {
            var_dump('P-----' . $id_product . '-----C------' . count($combination_attributes) . '------' . time());
            // 变体主体
            $product_attributes = [];
            // 循环生成变体主体
            foreach ($combination_attributes as $attribute_ids) {
                // 计算变体价格
                $price = 0;
                foreach ($attribute_ids as $id_attribute) {
                    $price += $attribute_prices[$id_attribute] ?? 0;
                }
                // 批量生成变体主体
                $product_attributes[] = [
                    'id_product' => $id_product,
                    'price' => $price,
                    'active' => 1,
                    'ean13' => '',
                    'isbn' => '',
                    'upc' => '',
                    'mpn' => '',
                    'reference' => '',
                ];
            }
            // 批量新增变体主体
            Tools::batch_insert($product_attributes, 'product_attribute');
            var_dump('1-----' . time());
            unset($product_attributes);

            // 获取产品新生成的变体ID和价格
            $product_attribute_prices = Db::getInstance()->executeS(
                'SELECT `id_product_attribute`, `price`
                FROM `' . _DB_PREFIX_ . 'product_attribute`
                WHERE `id_product` = ' . $id_product . '
                ' . ($old_product_attribute_ids ? 'AND `id_product_attribute` NOT IN (' . implode(',', $old_product_attribute_ids) . ')' : '')
            );
            // 变体ID
            $product_attribute_ids = array_column($product_attribute_prices, 'id_product_attribute');
            // 添加变体库存
            self::addStockavailable($id_product, $product_attribute_ids, $id_shop);
            var_dump('2-----' . time());

            // 价格对应变体ID(用于匹配变体的组合)
            $price_to_product_attribute_ids = [];
            // 语言表信息
            $product_attribute_lang = [];
            // 店铺表
            $product_attribute_shop = [];
            foreach ($product_attribute_prices as $product_attribute) {
                // 价格对应变体ID
                $price_to_product_attribute_ids[$product_attribute['price']][] = $product_attribute['id_product_attribute'];
                // 语言表信息
                foreach ($languages as $language) {
                    $id_product_attribute = $product_attribute['id_product_attribute'];
                    $product_attribute_lang[] = [
                        'id_product_attribute' => $id_product_attribute,
                        'id_lang' => $language['id_lang'],
                        'available_now' => '',
                        'available_later' => ''
                    ];
                }
                // 店铺表
                $product_attribute_shop[] = [
                    'id_product_attribute' => $id_product_attribute,
                    'id_product' => $id_product,
                    'id_shop' => $id_shop,
                    'price' => $product_attribute['price'],
                ];
            }
            unset($product_attribute_prices);
            var_dump('3-----' . time());

            // 批量插入
            Tools::batch_insert($product_attribute_lang, 'product_attribute_lang');
            unset($product_attribute_lang);
            Tools::batch_insert($product_attribute_shop, 'product_attribute_shop');
            unset($product_attribute_shop);

            // 变体对应组合
            $product_attribute_combination = [];
            // 属性对应变体ID
            $attribute_id_product_attribute_ids = [];
            foreach ($combination_attributes as $attribute_ids) {
                // 计算价格
                $price = 0;
                foreach ($attribute_ids as $price_id_attribute) {
                    $price += $attribute_prices[$price_id_attribute] ?? 0;
                }
                // 保留6位小数
                $price = number_format($price, 6, '.', '');
                // 通过价格获取变体ID（取符合要求的第一个变体的ID）
                $id_product_attribute = isset($price_to_product_attribute_ids[$price]) ? array_shift($price_to_product_attribute_ids[$price]) : 0;
                // 变体存在添加到数组
                if ($id_product_attribute) {
                    foreach ($attribute_ids as $combination_id_attribute) {
                        $product_attribute_combination[] = [
                            'id_product_attribute' => $id_product_attribute,
                            'id_attribute' => $combination_id_attribute,
                        ];
                        $attribute_id_product_attribute_ids[$combination_id_attribute][] = $id_product_attribute;
                    }
                }
            }
            // 批量插入
            Tools::batch_insert($product_attribute_combination, 'product_attribute_combination');
            var_dump('4-----' . time());

            unset($product_attribute_combination);

            // 设置默认变体
            $id_product_attribute = $price_to_product_attribute_ids[0][0] ?? $product_attribute_ids[0];

            self::setDefaultProductAttribute($id_product, $id_product_attribute);

            var_dump('5-----' . time());


            // 返回属性=>变体ID集合
            return $attribute_id_product_attribute_ids;
        } catch (Throwable $e) {
            throw new Exception('添加变体失败：产品ID：' . $id_product . '，' . $e->getMessage());
        }
    }

    /**
     * 设置默认变体
     * @param mixed $id_product
     * @throws \Exception
     * @return bool
     */
    private static function setDefaultProductAttribute($id_product, $id_product_attribute)
    {
        try {
            // 更新默认变体
            if (!Db::getInstance()->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'product_attribute` WHERE `default_on` = 1 AND `id_product` = ' . (int) $id_product)) {
                Db::getInstance()->execute(
                    'UPDATE `' . _DB_PREFIX_ . 'product_attribute` SET default_on = 1 where id_product_attribute = ' . (int) $id_product_attribute
                );
            }
            if (!Db::getInstance()->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'product_attribute_shop` WHERE `default_on` = 1 AND `id_product` = ' . (int) $id_product)) {
                Db::getInstance()->execute(
                    'UPDATE `' . _DB_PREFIX_ . 'product_attribute_shop` SET default_on = 1 where id_product_attribute = ' . (int) $id_product_attribute
                );
            }
        } catch (Throwable $e) {
            throw new Exception($e->getMessage());
        }
        return true;
    }

    /**
     * 添加变体库存
     * @param mixed $id_product 产品ID
     * @param mixed $id_product_attributes 需要添加库存的变体ID集合
     * @param mixed $id_shop 店铺ID
     * @param mixed $quantity 变体的库存数量
     * @throws \Exception
     * @return bool
     */
    private static function addStockavailable($id_product, $id_product_attributes, $id_shop, $quantity = 9999)
    {
        try {
            // 库存数据
            $stock_availables = [];
            foreach ($id_product_attributes as $id_product_attribute) {
                $stock_availables[] = [
                    'id_product' => $id_product,
                    'id_product_attribute' => $id_product_attribute,
                    'id_shop' => $id_shop,
                    'id_shop_group' => 0,
                    'quantity' => $quantity,
                    'physical_quantity' => $quantity,
                ];
            }
            // 批量导入变体库存
            Tools::batch_insert($stock_availables, 'stock_available');

            // 修改产品总库存
            $product_attribute_num = count($id_product_attributes);
            // 当前新增库存总数
            $total_quantity = $quantity * $product_attribute_num;
            // 修改总库存
            if (
                Db::getInstance()->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'stock_available`
                    WHERE `id_product` = ' . (int) $id_product . '
                    AND `id_product_attribute` = 0
                    AND `id_shop` = ' . (int) $id_shop)
            ) {
                // 存在则修改
                Db::getInstance()->execute(
                    'UPDATE `' . _DB_PREFIX_ . 'stock_available`
                    SET `quantity` = `quantity` + ' . (int) $total_quantity . '
                    WHERE `id_product` = ' . (int) $id_product . '
                    AND `id_product_attribute` = 0
                    AND `id_shop` = ' . (int) $id_shop
                );
            } else {
                // 不存在则新增
                Db::getInstance()->insert('stock_available', [
                    'id_product' => $id_product,
                    'id_product_attribute' => 0,
                    'id_shop' => $id_shop,
                    'id_shop_group' => 0,
                    'quantity' => $total_quantity,
                    'physical_quantity' => $total_quantity,
                ]);
            }

            return true;
        } catch (Throwable $e) {
            throw new Exception('添加变体库存失败：产品ID：' . $id_product . '，' . $e->getMessage());
        }
    }

    /**
     * 添加商品属性图片
     */
    public static function addProductAttributeImage($color_image_ids, $color_to_product_attribute_ids)
    {
        // 存在对应关系
        $product_attribute_image = [];
        foreach ($color_image_ids as $color_id => $id_image) {
            // 根据颜色获取有图片的变体id集合
            $product_attribute_ids = $color_to_product_attribute_ids[$color_id] ?? [];
            foreach ($product_attribute_ids as $id_product_attribute) {
                $product_attribute_image[] = [
                    'id_product_attribute' => $id_product_attribute,
                    'id_image' => $id_image,
                ];
            }
        }

        Tools::batch_insert($product_attribute_image, 'product_attribute_image');
        return true;
    }

    /**
     * 插入商品属性
     * @param mixed $data
     * @param mixed $id_product
     * @return mixed
     */
    public static function insertProductHasAttributes($data, $id_product)
    {
        $product_has_attributes = [];
        foreach ($data as $item) {
            $product_has_attributes[] = [
                'id_product' => $id_product,
                'id_attribute' => $item['id_attribute'],
                'id_attribute_group' => $item['id_attribute_group'],
                'id_attribute_color' => $item['id_attribute_color'],
                'price' => $item['price'],
                'sort' => $item['position']
            ];
        }
        return Tools::batch_insert($product_has_attributes, 'product_has_attribute');
    }


    /**
     * 生成缓存图片
     * @param mixed $image_name
     * @param mixed $gen_first_two_dir
     * @throws \Exception
     * @return bool|array
     */
    public static function saveImage($image_name, $gen_first_two_dir = false)
    {
        try {
            //  去掉开头的/
            $image_name = ltrim($image_name, '/');
            // 生成前两个字符的目录
            $firstTwoDir = '';
            if ($gen_first_two_dir) {
                // 前两个字符
                $firstTwoChars = substr($image_name, 0, 2);
                // 目录
                $firstTwoDir = implode('/', str_split((string) $firstTwoChars)) . '/';
            }
            // 图片名称是否包含.jpg
            if (strpos($image_name, '.jpg') === false) {
                // 不包含.jpg，添加.jpg后缀
                $image_name .= '.jpg';
            }

            // 图片的路径
            $image_path = _PS_ROOT_DIR_ . '/media/catalog/product/' . $firstTwoDir . $image_name;
            // 判断图片是否存在
            $real_image_dir = realpath($image_path);
            if ($real_image_dir === false) {
                throw new Exception($real_image_dir . '图片不存在');
            }

            self::uploadImage($real_image_dir, $firstTwoDir . $image_name);

            // 删除本地图片
            unlink($real_image_dir);
        } catch (Throwable $e) {
            throw new \Exception($e->getMessage());
        }

        return true;
    }

    /**
     * 上传图片
     * @param mixed $image_path
     * @param mixed $to_file_name
     * @throws \Exception
     * @return bool
     */
    public static function uploadImage($image_path, $to_file_name)
    {
        try {
            // 上传图片，并传递图片的存储路径
            $aws = new ImageAwsApiCore('media/catalog/product/');
            // 开始上传
            $aws->upload($image_path, $to_file_name);
        } catch (Throwable $e) {
            throw new \Exception($e->getMessage());
        }

        return true;
    }


    /**
     * 获取产品对应的特性和特性值
     * @param mixed $features
     * @param mixed $exist_feature_ids
     * @param mixed $exist_feature_value_ids
     * @param mixed $languages
     * @return array{id_feature: mixed, id_feature_value: mixed[]}
     */
    public static function getFeatureIds($features, &$exist_feature_ids, &$exist_feature_value_ids, $languages = [])
    {
        if (!$languages) {
            $languages = Language::getLanguages();
        }
        $feature_ids = [];
        foreach ($features as $feature => $feature_values) {
            // 获取特性id
            $id_feature = self::getFeatureId($feature, $exist_feature_ids, $languages);
            // 循环属性值
            foreach ($feature_values as $feature_value) {
                if ($feature_value == '') {
                    continue;
                }
                // 获取特性值id
                $id_feature_value = self::getFeatureValueId($id_feature, $feature_value, $exist_feature_value_ids, $languages);
                // 添加特性值id，去重
                $feature_ids[$id_feature . '-' . $id_feature_value] = [
                    'id_feature' => $id_feature,
                    'id_feature_value' => $id_feature_value
                ];
            }
        }

        return $feature_ids;
    }


    /**
     * 获取属性id
     * @param mixed $name
     * @param mixed $exist_feature_ids
     * @param mixed $id_shop
     * @param mixed $languages
     * @throws \Exception
     * @return mixed
     */
    public static function getFeatureId($code, &$exist_feature_ids, $languages = [])
    {
        if (!$languages) {
            $languages = Language::getLanguages();
        }
        try {
            // 判断是否存在
            if (isset($exist_feature_ids[strtolower($code)])) {
                // 存在直接拿
                $id_feature = $exist_feature_ids[strtolower($code)];
            } else {
                $name = ucwords(str_replace('_', ' ', $code));
                // 新增
                $names = [];
                foreach ($languages as $language) {
                    $names[$language['id_lang']] = $name;
                }
                $is_color = strpos($code, 'color') !== false ? 1 : 0;
                $feature = new Feature();
                $feature->code = $code;
                $feature->is_color = $is_color;
                $feature->is_filter = 0;
                $feature->is_show = 0;
                $feature->name = $names;
                $feature->add();
                $id_feature = $feature->id;

                // 添加到数组
                $exist_feature_ids[strtolower($code)] = $id_feature;
            }

            return $id_feature;
        } catch (Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * 特性值id
     * @param mixed $id_feature
     * @param mixed $value
     * @param mixed $exist_feature_value_ids
     * @param mixed $languages
     * @throws \Exception
     * @return mixed
     */
    public static function getFeatureValueId($id_feature, $value, &$exist_feature_value_ids, $languages = [])
    {
        if (!$languages) {
            $languages = Language::getLanguages();
        }
        try {
            // 判断是否存在
            if (isset($exist_feature_value_ids[$id_feature][strtolower($value)])) {
                // 存在直接拿
                $id_feature_value = $exist_feature_value_ids[$id_feature][strtolower($value)];
            } else {
                // 新增
                $values = [];
                foreach ($languages as $language) {
                    $values[$language['id_lang']] = $value;
                }
                $feature_value = new FeatureValue();
                $feature_value->id_feature = $id_feature;
                $feature_value->value = $values;
                $feature_value->add();

                // 获取id
                $id_feature_value = $feature_value->id;

                // 添加到数组
                $exist_feature_value_ids[$id_feature][strtolower($value)] = $id_feature_value;
            }

            return $id_feature_value;
        } catch (Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取属性IDs
     * @param mixed $attributes
     * @param mixed $exist_attribute_group_ids
     * @param mixed $exist_attribute_ids
     * @param mixed $languages
     * @return array[]
     */
    public static function getAttributeIds($attributes, &$exist_attribute_group_ids, &$exist_attribute_ids, $languages = [])
    {
        if (!$languages) {
            $languages = Language::getLanguages();
        }
        $attribute_ids = [];
        foreach ($attributes as $group => $attribute_items) {
            // 获取属性组id
            $id_attribute_group = self::getAttributeGroupId($group, $exist_attribute_group_ids, $languages);
            foreach ($attribute_items as $attribute) {
                // 获取属性id
                $id_attribute = self::getAttributeId($id_attribute_group, $attribute, $exist_attribute_ids, $languages);
                // 添加到数组
                $attribute_ids[$id_attribute_group][] = $id_attribute;
            }
        }
        return $attribute_ids;
    }

    // 获取标准尺寸
    public static function getStandardSize($string)
    {
        $ccTLD = strtoupper(_PS_COUNTRY_);
        $cleanSize = str_replace('\\/', '/', $string);
        preg_match('/US(\d+\w?)-AU\/?UK(\d+)-EUR(\d+)/', $cleanSize, $matches);
        if ($matches && count($matches) === 4) {
            switch (strtoupper($ccTLD)) {
                case 'GB':
                    $string = 'UK' . $matches[2] . '/EU' . $matches[3];
                    break;
                case 'AU':
                    $string = 'AU' . $matches[2] . '/EUR' . $matches[3];
                    break;
                case 'FR':
                case 'SE':
                    $string = 'EUR' . $matches[3];
                    break;
                case 'CA':
                case 'US':
                    $string = 'US' . $matches[1];
                    break;
                default:
                    break;
            }
        }

        return $string;
    }

    /**
     * 获取属性组id
     * @param mixed $name
     * @param mixed $exist_attribute_group_ids
     * @param mixed $languages
     * @return mixed
     */
    public static function getAttributeGroupId($name, &$exist_attribute_group_ids, $languages = [])
    {
        if (!$languages) {
            $languages = Language::getLanguages();
        }
        try {
            // 格式化
            $name = Tools::formatAttributeName($name);
            // 判断是否存在
            if (isset($exist_attribute_group_ids[strtolower($name)])) {
                // 存在直接拿
                $id_attribute_group = $exist_attribute_group_ids[strtolower($name)];
            } else {
                $attribute_group = new AttributeGroup();
                $group_type = 'radio';
                $is_color_group = 0;
                if (strtolower($name) == 'size') {
                    $group_type = 'size';
                } elseif (strtolower($name) === 'color') {
                    $group_type = 'color';
                    $is_color_group = 1;
                } elseif (strpos(strtolower($name), 'color')) {
                    $group_type = 'other_color';
                    $is_color_group = 1;
                }
                $attribute_group->group_type = $group_type;
                $attribute_group->is_color_group = $is_color_group;
                // 语言
                $names = [];
                $public_names = [];
                foreach ($languages as $language) {
                    $names[$language['id_lang']] = $name;
                    $public_names[$language['id_lang']] = $name;
                }
                $attribute_group->name = $names;
                $attribute_group->public_name = $public_names;
                $attribute_group->save();

                $id_attribute_group = $attribute_group->id;

                // 添加到数组
                $exist_attribute_group_ids[strtolower($name)] = $id_attribute_group;
            }

            return $id_attribute_group;
        } catch (Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取属性id
     * @param mixed $id_attribute_group
     * @param mixed $name
     * @param mixed $exist_attribute_ids
     * @param mixed $languages
     * @return mixed
     */
    public static function getAttributeId($id_attribute_group, $name, &$exist_attribute_ids, $languages = [])
    {
        if (!$languages) {
            $languages = Language::getLanguages();
        }
        try {
            // 格式化，仅尺寸group = 1
            // if ($id_attribute_group == 1) {
            //     $name = Tools::formatAttributeName($name);
            // }
            // 判断是否存在
            if (isset($exist_attribute_ids[$id_attribute_group][strtolower($name)])) {
                // 存在直接拿
                $id_attribute = $exist_attribute_ids[$id_attribute_group][strtolower($name)];
            } else {
                // 生成新的属性
                $attribute = new ProductAttribute();
                $attribute->id_attribute_group = $id_attribute_group;
                $attribute->color = NULL;
                $names = [];
                foreach ($languages as $language) {
                    $names[$language['id_lang']] = pSQL($name);
                }

                if (strtolower($name) == 'custom size') {
                    $attribute->is_custom_size = 1;
                }
                if (strpos(strtolower($name), 'show as picture') !== false) {
                    $attribute->code = 'show-as-picture';
                } else {
                    $attribute->code = Tools::link_rewrite($name);
                }

                $attribute->name = $names;
                $attribute->save();

                $id_attribute = $attribute->id;

                // 添加到数组
                $exist_attribute_ids[$id_attribute_group][strtolower($name)] = $id_attribute;
            }

            return $id_attribute;
        } catch (Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取属性
     * @param mixed $id_attribute_group
     * @param mixed $name
     * @param mixed $languages
     * @return mixed
     */
    public static function addAttributeId($id_attribute_group, $name, $languages = [])
    {
        // 格式化，仅尺寸group = 1
        if ($id_attribute_group == 1) {
            $name = Tools::formatAttributeName($name);
        }
        if (!$languages) {
            // 语言
            $languages = Language::getLanguages();
        }

        $attribute = new ProductAttribute();
        $attribute->id_attribute_group = $id_attribute_group;
        $attribute->color = NULL;
        $names = [];
        $name = trim($name);
        foreach ($languages as $language) {
            $names[$language['id_lang']] = pSQL($name);
        }

        if (strtolower($name) == 'custom size') {
            $attribute->is_custom_size = 1;
        }

        if (strpos(strtolower($name), 'show as picture') !== false) {
            $attribute->code = 'show-as-picture';
        } else {
            $attribute->code = Tools::link_rewrite($name);
        }

        $attribute->name = $names;
        $attribute->save();

        return $attribute->id;
    }

    /**
     * 获取材质id
     * @param mixed $name
     * @param mixed $exist_material_ids
     * @param mixed $languages
     * @throws \Exception
     * @return mixed
     */
    public static function getIdMaterial($name, &$exist_material_ids, $languages)
    {
        if (!$languages) {
            $languages = Language::getLanguages();
        }
        try {
            if (isset($exist_material_ids[strtolower($name)])) {
                $id_material = $exist_material_ids[strtolower($name)];
            } else {
                $names = [];
                foreach ($languages as $language) {
                    $names[$language['id_lang']] = $name;
                }
                $material = new Material();
                $material->name = $names;
                $material->save();

                $id_material = $material->id;
                $exist_material_ids[strtolower($name)] = $id_material;
            }

            return $id_material;
        } catch (Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 获取分类id
     * @param mixed $name 分类名称
     * @param mixed $category_ids 分类ids name => id
     * @param mixed $id_shop 店铺id
     * @param mixed $languages 语言
     * @return mixed
     */
    public static function getCategoryId($name, &$exist_category_ids, $id_shop, $languages)
    {
        try {
            // 判断是否存在
            if (isset($exist_category_ids[strtolower($name)])) {
                // 有直接拿
                $id_category = $exist_category_ids[strtolower($name)];
            } else {
                // 新增
                $names = [];
                foreach ($languages as $language) {
                    $names[$language['id_lang']] = $name;
                    $link_rewrites[$language['id_lang']] = Tools::link_rewrite($name);
                }
                if (strpos($name, 'Dresses') !== false) {
                    $type = 'clothes';
                } elseif (strpos($name, 'Shoes') !== false) {
                    $type = 'shoes';
                } else {
                    $type = 'ornament';
                }
                $category = new Category();
                $category->name = $names;
                $category->id_parent = Configuration::get('PS_HOME_CATEGORY') ?: 0;
                $category->active = 1;
                $category->type = $type;
                $category->id_shop_default = $id_shop;
                $category->link_rewrite = $link_rewrites;
                $category->add();
                $id_category = $category->id;

                // 添加到数组
                $exist_category_ids[strtolower($name)] = $id_category;
            }

            return $id_category;
        } catch (Throwable $e) {
            throw new \Exception($e->getMessage());
        }
    }

    /**
     * 所有已存在的数据，如产品SKU、分类、特性、特性值、属性组、属性
     */
    public static function getExistInfo()
    {
        // 店铺
        $id_shop = Context::getContext()->shop->id;
        // 语言
        $languages = Language::getLanguages();

        // 所有已存在的产品SKU
        $products = Db::getInstance()->executeS(
            'SELECT * FROM ' . _DB_PREFIX_ . 'product'
        );
        $skus = [];
        foreach ($products as $product) {
            $skus[] = strtoupper($product['supplier_reference']);
        }

        // 所有已存在的分类ID
        $categories = Db::getInstance()->executeS(
            'SELECT c.id_category, cl.name FROM ' . _DB_PREFIX_ . 'category c
            LEFT JOIN ' . _DB_PREFIX_ . 'category_lang cl ON (cl.id_category = c.id_category)
            LEFT JOIN ' . _DB_PREFIX_ . 'category_shop cs ON (cs.id_category = c.id_category AND cs.id_shop = ' . $id_shop . ')
            WHERE c.id_parent != 0 OR c.id_category = 1'
        );
        $category_ids = [];
        foreach ($categories as $category) {
            if (!isset($category_ids[strtolower($category['name'])])) {
                $category_ids[strtolower($category['name'])] = $category['id_category'];
            }
        }

        // 所有已存在的特性ID
        $features = Db::getInstance()->executeS(
            'SELECT f.id_feature, f.code FROM ' . _DB_PREFIX_ . 'feature f
            LEFT JOIN ' . _DB_PREFIX_ . 'feature_lang fl ON (fl.id_feature = f.id_feature)
            LEFT JOIN ' . _DB_PREFIX_ . 'feature_shop fs ON (fs.id_feature = f.id_feature AND fs.id_shop = ' . $id_shop . ')'
        );
        $feature_ids = [];
        foreach ($features as $feature) {
            if (!isset($feature_ids[strtolower($feature['code'])])) {
                $feature_ids[strtolower($feature['code'])] = $feature['id_feature'];
            }
        }

        // 所有已存在的特性值ID
        $feature_values = Db::getInstance()->executeS(
            'SELECT fv.id_feature, fv.id_feature_value, fvl.value FROM ' . _DB_PREFIX_ . 'feature_value fv
            LEFT JOIN ' . _DB_PREFIX_ . 'feature_value_lang fvl ON (fvl.id_feature_value = fv.id_feature_value)
            LEFT JOIN ' . _DB_PREFIX_ . 'feature_shop fs ON (fs.id_feature = fv.id_feature AND fs.id_shop = ' . $id_shop . ')'
        );
        $feature_value_ids = [];
        foreach ($feature_values as $feature_value) {
            if (!isset($feature_value_ids[$feature_value['id_feature']][strtolower($feature_value['value'])])) {
                $feature_value_ids[$feature_value['id_feature']][strtolower($feature_value['value'])] = $feature_value['id_feature_value'];
            }
        }

        // 所有已存在的属性组
        $attribute_groups = Db::getInstance()->executeS(
            'SELECT ag.id_attribute_group, agl.name FROM ' . _DB_PREFIX_ . 'attribute_group ag
            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_lang agl ON (agl.id_attribute_group = ag.id_attribute_group)
            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_group_shop ags ON (ags.id_attribute_group = ag.id_attribute_group AND ags.id_shop = ' . $id_shop . ')'
        );
        $attribute_group_ids = [];
        foreach ($attribute_groups as $attribute_group) {
            if (!isset($attribute_group_ids[strtolower($attribute_group['name'])])) {
                $attribute_group_ids[strtolower($attribute_group['name'])] = $attribute_group['id_attribute_group'];
            }
        }

        // 所有已存在的属性
        $attributes = Db::getInstance()->executeS(
            'SELECT a.id_attribute_group, a.id_attribute, al.name FROM ' . _DB_PREFIX_ . 'attribute a
            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_lang al ON (al.id_attribute = a.id_attribute)
            LEFT JOIN ' . _DB_PREFIX_ . 'attribute_shop ats ON (ats.id_attribute = a.id_attribute AND ats.id_shop = ' . $id_shop . ')'
        );
        $attribute_ids = [];
        foreach ($attributes as $attribute) {
            if (!isset($attribute_ids[$attribute['id_attribute_group']][strtolower($attribute['name'])])) {
                $attribute_ids[$attribute['id_attribute_group']][strtolower($attribute['name'])] = $attribute['id_attribute'];
            }
        }

        // 材质
        $materials = Db::getInstance()->executeS(
            'SELECT m.id_material, ml.name FROM ' . _DB_PREFIX_ . 'material m
            LEFT JOIN ' . _DB_PREFIX_ . 'material_lang ml ON (ml.id_material = m.id_material)
            LEFT JOIN ' . _DB_PREFIX_ . 'material_shop ms ON (ms.id_material = m.id_material AND ms.id_shop = ' . $id_shop . ')'
        );
        $material_ids = [];
        foreach ($materials as $material) {
            if (!isset($material_ids[strtolower($material['name'])])) {
                $material_ids[strtolower($material['name'])] = $material['id_material'];
            }
        }

        // 返回所有已存在的数据
        return [
            'id_shop' => $id_shop,
            'languages' => $languages,
            'skus' => $skus,
            'category_ids' => $category_ids,
            'feature_ids' => $feature_ids,
            'feature_value_ids' => $feature_value_ids,
            'attribute_group_ids' => $attribute_group_ids,
            'attribute_ids' => $attribute_ids,
            'material_ids' => $material_ids,
        ];
    }

    // 更新选项卡的redis
    public static function updateQueryProductHasAttributeRedis($id_product, $id_material = null)
    {
        // // 链接redis
        // $redis = Tools::getRedis();

        // // 获取产品的 id_material
        // if (!$id_material) {
        //     $product_material_sql = 'SELECT `id_material` FROM `' . _DB_PREFIX_ . 'product` WHERE `id_product` = ' . (int) $id_product;
        //     $id_material = Db::getInstance()->getValue($product_material_sql);
        // }

        // //获取产品变体
        // $product_variants_sql = 'SELECT
        //     pa.`reference`,
        //     pa.`ean13`,
        //     pa.`mpn`,
        //     pa.`upc`,
        //     pa.`isbn`,
        //     pa.`ship_in_48hrs`,
        //     pa.`quantity_48hrs`,
        //     pal.`available_now`,
        //     pal.`available_later`,
        //     pai.id_image AS id_product_attribute_image,
        //     pas.`id_product_attribute`,
        //     pas.`price`,
        //     pas.`ecotax`,
        //     pas.`weight`,
        //     pas.`default_on`,
        //     pas.`unit_price_impact`,
        //     pas.`minimal_quantity`,
        //     pas.`available_date`,
        //     pac.id_attribute
        // FROM
        //     `' . _DB_PREFIX_ . 'product_attribute` pa
        //     LEFT JOIN `' . _DB_PREFIX_ . 'product_attribute_shop` pas ON ( pas.id_product_attribute = pa.id_product_attribute AND pas.id_shop = 1 )
        //     LEFT JOIN `' . _DB_PREFIX_ . 'product_attribute_lang` pal ON ( pa.`id_product_attribute` = pal.`id_product_attribute` AND pal.`id_lang` = 1 )
        //     LEFT JOIN `' . _DB_PREFIX_ . 'product_attribute_image` pai ON ( pai.`id_product_attribute` = pa.`id_product_attribute` )
        //     LEFT JOIN `' . _DB_PREFIX_ . 'product_attribute_combination` pac ON ( pac.`id_product_attribute` = pa.`id_product_attribute` )
        // WHERE
        //     pa.`id_product` = ' . (int) $id_product . '
        //     AND pa.`active` = 1';
        // $product_variants = Db::getInstance()->executeS($product_variants_sql);
        // // 永久
        // $redis->set(_PS_REDIS_PRODUCT_VARIANTS_ . $id_product, json_encode($product_variants));

        // // 产品属性
        // $product_has_attributes_sql = 'SELECT
        //     ag.`id_attribute_group`,
        //     ag.`is_color_group`,
        //     agl.`name` AS group_name,
        //     agl.`public_name` AS public_group_name,
        //     a.`id_attribute`,
        //     al.`name` AS attribute_name,
        //     a.`color` AS attribute_color,
        //     a.`is_custom_size` AS attribute_custom_size,
        //     pha.`price` AS attribute_price,
        //     ag.`group_type`,
        //     a.`code`,
        //     a.`is_custom_size`,
        //     mc.`image` AS material_color_image
        // FROM
        //     `' . _DB_PREFIX_ . 'product_has_attribute` pha
        //     LEFT JOIN `' . _DB_PREFIX_ . 'attribute` a ON ( a.`id_attribute` = pha.`id_attribute` )
        //     LEFT JOIN `' . _DB_PREFIX_ . 'attribute_group` ag ON ( ag.`id_attribute_group` = a.`id_attribute_group` )
        //     LEFT JOIN `' . _DB_PREFIX_ . 'attribute_lang` al ON ( a.`id_attribute` = al.`id_attribute` )
        //     LEFT JOIN `' . _DB_PREFIX_ . 'attribute_group_lang` agl ON ( ag.`id_attribute_group` = agl.`id_attribute_group` )
        //     LEFT JOIN `' . _DB_PREFIX_ . 'material_color` mc ON ( mc.`id_attribute` = a.`id_attribute` AND mc.`id_material` = ' . (int) $id_material . ' )
        // WHERE
        //     pha.`id_product` = ' . (int) $id_product . '
        //     ORDER BY ag.`position` ASC, pha.`sort` ASC, a.`position` ASC, agl.`name` ASC';
        // $product_has_attributes = Db::getInstance()->executeS($product_has_attributes_sql);
        // // 永久
        // $redis->set(_PS_REDIS_PRODUCT_HAS_ATTRIBUTE_ . $id_product, json_encode($product_has_attributes));
    }
}