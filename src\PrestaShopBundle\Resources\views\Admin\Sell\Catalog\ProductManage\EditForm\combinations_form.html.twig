<div role="tabpanel" class="form-contenttab tab-pane container-fluid{% if form == 'combinations' %} active{% endif %}" id="product_combinations-tab">
	<form action="{{ path('admin_products_edit_save', {'productId': product.id_product}) }}" method="post" id="product_features_form">
		<div id="product_combinations" data-select2-id="product_combinations">
			<div class="form-group">
				<h3>Manage product combinations</h3>
				<span style="color:red;">注意：进行删除、新增、修改属性或价格操作，需等待脚本更新变体，脚本5分钟执行一次</span>
				<div class="row">
					<div id="product-has-attributes-block" class="col-md-12">
						{% if product.has_attributes %}
							{% for id_attribute_group, has_attributes in product.has_attributes %}
								<div class="col-md-12 attribute-group">
									<table class="table" style="border-bottom:0px solid">
										<thead>
											<tr>
												<th class="col-md-4">{{ 'Title'|trans({}, 'Admin.Global') }}</th>
												{% if id_attribute_group == '2' %}
												<th class="col-md-3">{{ 'Color Mapping'|trans({}, 'Admin.Global') }}</th>
												{% endif %}
												<th class="col-md-3">{{ 'Input Type'|trans({}, 'Admin.Catalog.Feature') }}</th>
												<th class="col-md-3">{{ 'Sort Order'|trans({}, 'Admin.Global') }}</th>
												<th class="col-md-2"></th>
											</tr>
										</thead>
										<tbody>
											<tr>
												<td class="col-md-4">
													<input class="form-control" type="text" value="{{ attribute_groups[id_attribute_group]['name'] }}" disabled/>
												</td>
												{% if id_attribute_group == '2' %}
													<td class="col-md-3">
														<input class="form-control" type="text" value="{{ attribute_groups[id_attribute_group]['name'] }}" disabled/>
													</td>
												{% endif %}
												<td class="col-md-3">
													<input class="form-control" type="text" value="{{ group_type[attribute_groups[id_attribute_group]['group_type']] }}" disabled/>
												</td>
												<td class="col-md-3">
													<input class="form-control" type="text" value="{{ attribute_groups[id_attribute_group]['position'] }}" disabled/>
												</td>
												<td class="col-md-2 text-right">
													<button type="button" class="btn-sm btn-danger delete-product-has-attribute-group" title="delete the attribute group">
														<i class="material-icons" style="font-size: 18px;">delete</i>
														{{ 'Delete the attributes'|trans({}, 'Admin.Global') }}
													</button>
												</td>
											</tr>
										</tbody>
									</table>
									{% if has_attributes %}
										<table style="width: 100%;">
											<thead>
												<tr>
													<th class="col-md-1"></th>
													<th class="col-md-3">{{ 'Title'|trans({}, 'Admin.Catalog.Feature') }}</th>
													{% if id_attribute_group == '2' %}
														<th class="col-md-3">{{ 'Color Mapping'|trans({}, 'Admin.Catalog.Feature') }}</th>
													{% endif %}
													<th class="col-md-3">{{ 'Price'|trans({}, 'Admin.Global') }}</th>
													<th class="col-md-2">{{ 'Sort Order'|trans({}, 'Admin.Global') }}</th>
													<th class="col-md-2"></th>
													<th class="col-md-1"></th>
												</tr>
											</thead>
											<tbody id="attribute-group-tbody-{{ id_attribute_group }}">
												{% for key, attribute in has_attributes %}
													<tr style="margin-bottom: 10px;" class="product-has-attribute">
														<td class="col-md-1">
															<input type="hidden" name="product[combinations][{{ id_attribute_group}}][{{ key }}][id_product_has_attribute]" value="{{ attribute.id_product_has_attribute }}"/>
														</td>
														<td class="col-md-3" style="position: relative;height:40px">
															<select class="form-control select2" name="product[combinations][{{ id_attribute_group}}][{{ key }}][id_attribute]">
																{% for id_attribute, option_attribute in attributes[id_attribute_group] %}
																	<option value="{{ id_attribute }}" {% if id_attribute == attribute.id_attribute %} selected {% endif %}>{{ option_attribute }}</option>
																{% endfor %}
															</select>
														</td>
														{% if id_attribute_group == '2' %}
															<td class="col-md-3" style="position: relative;height:40px">
																<select class="form-control select2" name="product[combinations][{{ id_attribute_group}}][{{ key }}][id_attribute_color]">
																	<option value="0">{{ 'Select a color'|trans({}, 'Admin.Global') }}</option>
																	{% for id_attribute, option_attribute in attributes[id_attribute_group] %}
																		<option value="{{ id_attribute }}" {% if id_attribute == attribute.id_attribute_color %} selected {% endif %}>{{ option_attribute }}</option>
																	{% endfor %}
																</select>
															</td>
														{% endif %}
														<td class="input-group money-type" style="position: relative;">
															<div class="input-group-prepend">
																<span class="input-group-text">$</span>
															</div>
															<input class="js-comma-transformer form-control" name="product[combinations][{{ id_attribute_group}}][{{ key }}][price]" type="text" value="{{ attribute.price }}"/>
														</td>
														<td class="col-md-2" style="position: relative;">
															<input class="form-control" name="product[combinations][{{ id_attribute_group}}][{{ key }}][sort]" type="text" value="{{ attribute.sort }}"/>
														</td>
														<td class="col-md-2">
															<button type="button" class="btn-sm btn-danger delete-product-has-attribute delete-icon" title="delete the attribute">
																<i class="material-icons" style="font-size: 18px;">delete</i>
															</button>
														</td>
														<td class="col-md-1"></td>
													</tr>
												{% endfor %}
											</tbody>
											<tfoot>
												<tr>
													<td></td>
													<td></td>
													<td></td>
													<td></td>
													<td class="col-md-2">
														<button type="button" class="btn-sm btn-primary add-product-has-attribute" title="add the attribute" data-id-attribute-group="{{ id_attribute_group }}">
															<i class="material-icons" style="font-size: 24px;">add_circle_outline</i>
															add an attribute value
														</button>
													</td>
													<td></td>
												</tr>
											</tfoot>
										</table>
									{% endif %}
								</div>
							{% endfor %}
						{% endif %}
					</div>
					<div class="col-md-12">
						<a class="btn btn-primary pointer" id="add-product-has-attribute-group" href="#" title="Add new attribute group" data-product-id="{{ product.id_product }}">
							<i class="material-icons">add_circle_outline</i>
							{{ 'add an attribute'|trans({}, 'Admin.Global') }}
						</a>
					</div>
				</div>
			</div>
		</div>
		<div class="form-buttons">
			<input type="hidden" name="product[form]" value="combinations">
			<a href="{{ cancel_url }}" class="btn btn-outline-secondary">Cancel</a>
			<button id="product_combinations_save" class="btn btn-primary float-right" type="submit">
				<span>Save</span>
			</button>
			<button id="product_combinations_save_then_back" class="btn btn-primary float-right" style="margin-right: 5px;" name="SaveThenBack" type="submit">
				<span>Save then back</span>
			</button>
		</div>
	</form>
	<div class="modal fade" id="attributeGroupModal" tabindex="-1" role="dialog" aria-labelledby="attributeGroupModalLabel" aria-hidden="true">
		<div class="modal-dialog" role="document">
			<div class="modal-content">
				<div class="modal-header">
					<h5 class="modal-title" id="attributeGroupModalLabel">{{ 'Add a Attribute Group'|trans({}, 'Admin.Catalog.Feature') }}</h5>
					<button type="button" class="close" data-dismiss="modal" aria-label="Close">
						<span aria-hidden="true">&times;</span>
					</button>
				</div>
				<div class="modal-body">
					<div class="form-group">
						<label class="form-control-label" for="attributeGroup">{{ 'Attribute Group'|trans({}, 'Admin.Catalog.Feature') }}</label>
						<select class="form-control select2" id="modal_attribute_group">
							{% for id_attribute_group, group in attribute_groups %}
								<option value="{{ id_attribute_group }}">{{ group.name }}</option>
							{% endfor %}
						</select>
					</div>
					<div class="form-group">
						<label class="form-control-label" for="attribute">{{ 'Attribute'|trans({}, 'Admin.Catalog.Feature') }}</label>
						<select class="form-control select2" id="modal_attribute">
							{% for id_attr, attr in attributes[1] %}
								<option value="{{ id_attr }}">{{ attr }}</option>
							{% endfor %}
						</select>
					</div>
				</div>
				<div class="modal-footer">
					<button type="button" class="btn btn-secondary" data-dismiss="modal">{{ 'Close'|trans({}, 'Admin.Global') }}</button>
					<button type="button" class="btn btn-primary confirm">{{ 'Confirm'|trans({}, 'Admin.Global') }}</button>
				</div>
			</div>
		</div>
	</div>
</div>

<script>
	$('.select2').select2();

	var attribute_groups = JSON.parse('{{ attribute_groups|json_encode|raw }}');
	var attributes = JSON.parse('{{ attributes|json_encode|raw }}');
	var group_counts = JSON.parse('{{ group_counts|json_encode|raw }}');

	// 添加属性弹框
	$('body').on('click', '#add-product-has-attribute-group', function () {
		$('#attributeGroupModal').modal('show');
	});

	// 弹框性组改变
	$('body').on('change', '#modal_attribute_group', function () {
		var id_attribute_group = $(this).val();
		var attribute_sel = $('#modal_attribute');
		attribute_sel.empty();
		$.each(attributes[id_attribute_group], function (key, value) {
			attribute_sel.append('<option value="' + key + '">' + value + '</option>');
		});
	});

	// 确认弹框
	$('body').on('click', '.confirm', function () {
		var id_attribute_group = $('#modal_attribute_group').val();
		var id_attribute = $('#modal_attribute').val();
		if (group_counts[id_attribute_group]) {
			var key = group_counts[id_attribute_group];
		} else {
			var key = 0;
			group_counts[id_attribute_group] = 0;
		}
		var attribute_html = addAttribute(attributes[id_attribute_group], id_attribute_group, key, id_attribute);
		// 先判断是否已经存在该属性组
		if ($('.attribute-group #attribute-group-tbody-' + id_attribute_group).length > 0) {console.log('在这里');
			$('.attribute-group #attribute-group-tbody-' + id_attribute_group).append(attribute_html);
		} else {
			var attribute_group_html = addAttributeGroup(attribute_html, id_attribute_group);
			$('#product-has-attributes-block').append(attribute_group_html);
		}
		$('.select2').select2();
		group_counts[id_attribute_group]++;
		$('#attributeGroupModal').modal('hide');
	});

	// 添加属性值选框
	$('body').on('click', '.add-product-has-attribute', function () {
		var id_attribute_group = $(this).data('id-attribute-group');
		var key = group_counts[id_attribute_group];
		var attribute_html = addAttribute(attributes[id_attribute_group], id_attribute_group, key);
		$('.attribute-group #attribute-group-tbody-' + id_attribute_group).append(attribute_html);
		$('.select2').select2();
		group_counts[id_attribute_group]++;
	});

	// 删除属性组
	$('body').on('click', '.delete-product-has-attribute-group', function () {
		$(this).closest('.attribute-group').remove();
	});

	// 删除属性
	$('body').on('click', '.delete-product-has-attribute', function () {
		$(this).closest('.product-has-attribute').remove();
	});

	// 添加属性
	function addAttribute(attribute, id_attribute_group, key, value = '') {
		if (value) {
			var attributeOption = Object.entries(attribute).map(function (item) {
				if (item[0] == value) {
					return `<option value="${item[0]}" selected>${item[1]}</option>`;
				} else {
					return `<option value="${item[0]}">${item[1]}</option>`;
				}
			})
		} else {
			var attributeOption = Object.entries(attribute).map(function (item) {
				return `<option value="${item[0]}">${item[1]}</option>`;
			}).join('');
		}
		let AttributeColorHtml = ``;
		if(id_attribute_group == 2){
			AttributeColorHtml = `<td class="col-md-3" style="position: relative;height:40px">
					<select class="form-control select2" name="product[combinations][` + id_attribute_group + `][` + key + `][id_attribute_color]">
						<option value="0">Select a color</option>
						` + attributeOption + `
					</select>
				</td>`;
		}

		var html = `<tr style="margin-bottom: 10px;" class="product-has-attribute">
				<td class="col-md-1">
					<input type="hidden" name="product[combinations][` + id_attribute_group + `][` + key + `][id_product_has_attribute]"/>
				</td>
				<td class="col-md-3" style="position: relative;height:40px">
					<select class="form-control select2" name="product[combinations][` + id_attribute_group + `][` + key + `][id_attribute]">
						` + attributeOption + `
					</select>
				</td>
				` + AttributeColorHtml + `
				<td class="input-group money-type" style="position: relative;">
					<div class="input-group-prepend">
						<span class="input-group-text">$</span>
					</div>
					<input  type="text" class="js-comma-transformer form-control" name="product[combinations][` + id_attribute_group + `][` + key + `][price]"/>
				</td>
				<td class="col-md-2" style="position: relative;">
					<input  type="text" class="form-control"  name="product[combinations][` + id_attribute_group + `][` + key + `][sort]"/>
				</td>
				<td class="col-md-2">
					<button type="button" class="btn-sm btn-danger delete-product-has-attribute delete-icon" title="delete the attribute">
						<i class="material-icons" style="font-size: 18px;">delete</i>
					</button>
				</td>
				<td class="col-md-1"></td>
			</tr>`
		return html;
	}

	// 添加属性组
	function addAttributeGroup(attribute_html, id_attribute_group) {
		const extraTh = id_attribute_group == 2 
        ? '<th class="col-md-3">{{ 'Color Mapping'|trans({}, 'Admin.Global') }}</th>' 
        : '';

		const att_name = id_attribute_group == 2 
        ? '<td class="col-md-3"><input class="form-control" type="text" value="' + attribute_groups[id_attribute_group]['name'] + '" disabled/></td>' 
        : '';

		var html = `
		<div class="col-md-12 attribute-group">
			<table class="table" style="border-bottom:0px solid">
				<thead>
					<tr>
						<th class="col-md-4">{{ 'Title'|trans({}, 'Admin.Global') }}</th>
						${extraTh}
						<th class="col-md-3">{{ 'Input Type'|trans({}, 'Admin.Catalog.Feature') }}</th>
						<th class="col-md-3">{{ 'Sort Order'|trans({}, 'Admin.Global') }}</th>
						<th class="col-md-2"></th>
					</tr>
				</thead>
				<tbody>
					<tr>
						<td class="col-md-4">
							<input class="form-control" type="text" value="` + attribute_groups[id_attribute_group]['name'] + `" disabled/>
						</td>
						${att_name}
						<td class="col-md-3">
							<input class="form-control" type="text" value="` + attribute_groups[id_attribute_group]['group_type'] + `" disabled/>
						</td>
						<td class="col-md-3">
							<input class="form-control" type="text" value="` + attribute_groups[id_attribute_group]['position'] + `" disabled/>
						</td>
						<td class="col-md-2 text-right">
							<button type="button" class="btn-sm btn-danger delete-product-has-attribute-group" title="delete the attribute group">
								<i class="material-icons" style="font-size: 18px;">delete</i>
								{{ 'Delete the attributes'|trans({}, 'Admin.Global') }}
							</button>
						</td>
					</tr>
				</tbody>
			</table>
			<table style="width: 100%;">
				<thead>
					<tr>
						<th class="col-md-1"></th>
						<th class="col-md-3">{{ 'Title'|trans({}, 'Admin.Catalog.Feature') }}</th>
						${extraTh}
						<th class="col-md-3">{{ 'Price'|trans({}, 'Admin.Global') }}</th>
						<th class="col-md-2">{{ 'Sort Order'|trans({}, 'Admin.Global') }}</th>
						<th class="col-md-2"></th>
						<th class="col-md-1"></th>
					</tr>
				</thead>
				<tbody id="attribute-group-tbody-` + id_attribute_group +`">
					` + attribute_html + `
				</tbody>
				<tfoot>
					<tr>
						<td></td>
						<td></td>
						<td></td>
						<td></td>
						<td class="col-md-2">
							<button type="button" class="btn-sm btn-primary add-product-has-attribute" title="add the attribute" data-id-attribute-group="` + id_attribute_group + `">
								<i class="material-icons" style="font-size: 24px;">add_circle_outline</i>
								add an attribute value
							</button>
						</td>
						<td></td>
					</tr>
				</tfoot>
			</table>
		</div>`;
		return html;
	}
</script>
