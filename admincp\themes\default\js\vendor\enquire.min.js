/*!
 * enquire.js v2.1.0 - Awesome Media Queries in JavaScript
 * Copyright (c) 2013 <PERSON> - http://wicky.nillia.ms/enquire.js
 * License: MIT (http://www.opensource.org/licenses/mit-license.php)
 */(function(e,t,n){var r=t.matchMedia;typeof module!="undefined"&&module.exports?module.exports=n(r):typeof define=="function"&&define.amd?define(function(){return t[e]=n(r)}):t[e]=n(r)})("enquire",this,function(e){"use strict";function t(e,t){var n=0,r=e.length,i;for(n;n<r;n++){i=t(e[n],n);if(i===!1)break}}function n(e){return Object.prototype.toString.apply(e)==="[object Array]"}function r(e){return typeof e=="function"}function i(e){this.options=e;!e.deferSetup&&this.setup()}function s(t,n){this.query=t;this.isUnconditional=n;this.handlers=[];this.mql=e(t);var r=this;this.listener=function(e){r.mql=e;r.assess()};this.mql.addListener(this.listener)}function o(){if(!e)throw new Error("matchMedia not present, legacy browsers require a polyfill");this.queries={};this.browserIsIncapable=!e("only all").matches}i.prototype={setup:function(){this.options.setup&&this.options.setup();this.initialised=!0},on:function(){!this.initialised&&this.setup();this.options.match&&this.options.match()},off:function(){this.options.unmatch&&this.options.unmatch()},destroy:function(){this.options.destroy?this.options.destroy():this.off()},equals:function(e){return this.options===e||this.options.match===e}};s.prototype={addHandler:function(e){var t=new i(e);this.handlers.push(t);this.matches()&&t.on()},removeHandler:function(e){var n=this.handlers;t(n,function(t,r){if(t.equals(e)){t.destroy();return!n.splice(r,1)}})},matches:function(){return this.mql.matches||this.isUnconditional},clear:function(){t(this.handlers,function(e){e.destroy()});this.mql.removeListener(this.listener);this.handlers.length=0},assess:function(){var e=this.matches()?"on":"off";t(this.handlers,function(t){t[e]()})}};o.prototype={register:function(e,i,o){var u=this.queries,a=o&&this.browserIsIncapable;u[e]||(u[e]=new s(e,a));r(i)&&(i={match:i});n(i)||(i=[i]);t(i,function(t){u[e].addHandler(t)});return this},unregister:function(e,t){var n=this.queries[e];if(n)if(t)n.removeHandler(t);else{n.clear();delete this.queries[e]}return this}};return new o});