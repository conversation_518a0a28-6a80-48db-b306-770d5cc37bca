//
// bootstrap-chosen.scss
//
// An alternate stylesheet for <PERSON><PERSON> (http://harvesthq.github.com/chosen/).
// This one is supposed to integrate better with Bootstrap.
//
// Submit bugfixes to: http://github.com/alxlit/bootstrap-chosen
//

$chosen-background: $input-bg;
$chosen-border: $input-border;
$chosen-border-radius: $input-border-radius;
$chosen-multi-border-radius: 0;
$chosen-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.075);
$chosen-drop-border: $gray-light;
$chosen-drop-box-shadow: 0 8px 8px rgba(0, 0, 0, 0.25);
$chosen-drop-zindex: 1060;
$chosen-focus-border: rgba(82, 168, 236, 0.8);
$chosen-focus-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.075) inset, 0 0 8px rgba(82, 168, 236, 0.6);
$chosen-focus-transition: border linear 0.2s, box-shadow linear 0.2s;
$chosen-height: $input-height-base;
$chosen-multi-height: $input-height-base + 6px;
$chosen-sprite-path: "../img/chosen-sprite.png";

.chosen-select {
  width: 100%;
}

.chosen-select-deselect {
  width: 100%;
}

.chosen-container {
  position: relative;
  display: inline-block;
  font-size: $font-size-base;
  vertical-align: middle;

  .chosen-drop {
    position: absolute;
    top: 100%;
    z-index: $chosen-drop-zindex;
    margin-top: -1px;
    background: $chosen-background;
    border: 1px solid $chosen-drop-border;
    border-top-color: lighten($chosen-drop-border, 25%);
    @include left(-9000px);
    @include border-bottom-radius($chosen-border-radius);
    @include box-shadow($chosen-drop-box-shadow);
  }

  &.chosen-with-drop .chosen-drop {
    @include left(0);
    @include right(0);
  }

  .chosen-results {
    position: relative;
    max-height: 240px;
    overflow-x: hidden;
    overflow-y: auto;
    color: $gray;
    -webkit-overflow-scrolling: touch;
    @include padding(0, 0, 0, 4px);
    @include margin(0, 4px, 4px, 0);

    li {
      display: none;
      padding: 5px 6px;
      margin: 0;
      line-height: $line-height-base;
      list-style: none;

      em {
        font-style: normal;
        background: #feffde;
      }

      &.group-result {
        display: list-item;
        font-weight: 700;
        color: #999;
        cursor: default;
      }

      &.group-option {
        @include padding-left(15px);
      }

      &.active-result {
        display: list-item;
        cursor: pointer;
      }

      &.highlighted {
        color: #fff;
        background-color: $link-color;

        em {
          background: transparent;
        }
      }

      &.disabled-result {
        display: list-item;
        color: $gray-light;
      }
    }

    .no-results {
      display: list-item;
      background: $gray-lighter;
    }
  }

  .chosen-results-scroll {
    position: absolute;
    z-index: 1;
    width: 321px;
    margin: 0 4px;
    text-align: center;
    background: #fff;

    span {
      display: inline-block;
      width: 9px;
      height: $line-height-base;
      text-indent: -5000px;
    }
  }

  .chosen-results-scroll-down {
    bottom: 0;

    span {
      background: url($chosen-sprite-path) no-repeat -4px -3px;
    }
  }

  .chosen-results-scroll-up {
    span {
      background: url($chosen-sprite-path) no-repeat -22px -3px;
    }
  }
}

.chosen-container-single {
  .chosen-single {
    position: relative;
    display: block;
    height: $chosen-height;
    overflow: hidden;
    line-height: $chosen-height;
    color: $gray;
    text-decoration: none;
    white-space: nowrap;
    background-color: $chosen-background;
    border: 1px solid $chosen-border;
    @include padding(0, 0, 0, 8px);
    @include box-shadow($chosen-box-shadow);
    @include border-radius($input-border-radius);

    &:hover {
      text-decoration: none;
      cursor: pointer;
    }
    // stylelint-disable-next-line
    &:span {
      display: block;
      @include margin-right(26px);
      @include text-overflow();
    }
    // stylelint-disable-next-line
    &:abbr {
      position: absolute;
      top: ($chosen-height - 10px) / 2;
      display: block;
      width: 12px;
      height: 10px;
      font-size: 1px;
      background: url($chosen-sprite-path) right top no-repeat;
      @include right(26px);

      &:hover {
        background-position: right -11px;
      }
    }

    &.chosen-disabled .chosen-single abbr:hover {
      background-position: right 2px;
    }

    div {
      position: absolute;
      top: 0;
      display: block;
      width: 18px;
      height: 100%;
      @include right(0);

      b {
        @extend .icon;
        @extend .icon-caret-down;
        display: block;
        width: 100%;
        height: 100%;
        font-size: 14px;
        line-height: 28px;
        text-align: center;
      }
    }
  }

  .chosen-default {
    color: $gray-light;
  }

  .chosen-search {
    position: relative;
    z-index: $zindex-dropdown;
    padding: 3px 4px;
    margin: 0;
    overflow: hidden;
    white-space: nowrap;

    input {
      width: 100%;
      margin: 1px 0;
      border: 1px solid $chosen-border;
      @include padding(4px, 20px, 4px, 4px);
      @include border-top-radius($chosen-border-radius);
      @include border-bottom-radius($chosen-border-radius);
      @include box-shadow($chosen-box-shadow);
    }
  }

  .chosen-search::before {
    @extend .icon;
    @extend .icon-search;
    position: absolute;
    top: 7px;
    display: block;
    width: 22px;
    height: 22px;
    font-size: 14px;
    line-height: 22px;
    color: $brand-primary;
    text-align: center;
    @include right(7px);
  }

  .chosen-drop {
    margin-top: -1px;
    -webkit-background-clip: padding-box;
    -moz-background-clip: padding;
    background-clip: padding-box;
    @include border-bottom-radius($chosen-border-radius);
  }
}

.chosen-container-single-nosearch {
  .chosen-search {
    input {
      position: absolute;
      @include left(-9000px);
    }
  }
}

.chosen-container-multi {
  .chosen-choices {
    position: relative;
    height: auto !important;
    height: 1%;
    padding: 0;
    margin: 0;
    overflow: hidden;
    cursor: text;
    background-color: $chosen-background;
    border: 1px solid $chosen-border;
    @include border-top-radius($chosen-multi-border-radius);
    @include border-bottom-radius($chosen-multi-border-radius);
    @include box-shadow($chosen-box-shadow);

    li {
      list-style: none;
      @include float(left);
    }

    .search-field {
      padding: 0;
      margin: 0;
      white-space: nowrap;

      input {
        height: $chosen-multi-height - 8px;
        padding: 4px;
        margin: 0;
        color: $gray;
        background: transparent !important;
        border: 0 !important;
        outline: 0;
        @include box-shadow(none);
      }

      .default {
        color: #999;
      }
    }

    .search-choice {
      position: relative;
      line-height: 13px;
      color: $gray-dark;
      cursor: default;
      background-color: $gray-lighter;
      -webkit-background-clip: padding-box;
      -moz-background-clip: padding;
      background-clip: padding-box;
      border: 1px solid $chosen-border;
      @include margin(6px, 0, 3px, 5px);
      @include padding(3px, 20px, 3px, 5px);
      @include border-top-radius($chosen-border-radius);
      @include border-bottom-radius($chosen-border-radius);
      @include gradient-vertical(#ffffff, $gray-lighter);
      @include box-shadow($chosen-box-shadow);

      .search-choice-close {
        position: absolute;
        top: 5px;
        display: block;
        width: 12px;
        height: 10px;
        font-size: 1px;
        background: url($chosen-sprite-path) right top no-repeat;
        @include right(4px);

        &:hover {
          background-position: right -11px;
        }
      }
    }

    .search-choice-focus {
      background: #d4d4d4;

      .search-choice-close {
        background-position: right -11px;
      }
    }
  }

  .chosen-results {
    padding: 0;
    margin: 0;
  }

  .chosen-drop {
    .result-selected {
      display: none;
    }
  }
}

.chosen-container-active {
  .chosen-single {
    border: 1px solid $chosen-focus-border;
    @include box-shadow($chosen-focus-box-shadow);
    @include transition($chosen-focus-transition);
  }

  &.chosen-with-drop .chosen-single {
    background-color: $gray-lighter;
    border: 1px solid $chosen-focus-border;
    @include box-shadow($chosen-focus-box-shadow);
    @include transition($chosen-focus-transition);

    div {
      background: transparent;
      @include border-left(none);

      b {
        background-position: -18px 7px;
      }
    }
  }

  .chosen-choices {
    border: 1px solid $chosen-focus-border;
    @include border-bottom-radius(0);
    @include box-shadow($chosen-focus-box-shadow);
    @include transition($chosen-focus-transition);

    .search-field input {
      color: #111 !important;
    }
  }
}

.chosen-disabled {
  cursor: default;
  opacity: 0.5 !important;

  .chosen-single {
    cursor: default;
  }

  .chosen-choices .search-choice .search-choice-close {
    cursor: default;
  }
}

.chosen-rtl {
  text-align: right;

  .chosen-single {
    padding: 0 8px 0 0;
    overflow: visible;

    span {
      margin-right: 0;
      margin-left: 26px;
      direction: rtl;
    }

    div {
      right: auto;
      left: 7px;
    }

    abbr {
      right: auto;
      left: 26px;
    }
  }

  .chosen-choices {
    .search-field input {
      direction: rtl;
    }

    li {
      float: right;
    }

    .search-choice {
      padding: 3px 5px 3px 19px;
      margin: 6px 5px 3px 0;

      .search-choice-close {
        right: auto;
        left: 4px;
        background-position: right top;
      }
    }
  }

  &.chosen-container-single .chosen-results {
    padding: 0 4px 0 0;
    margin: 0 0 4px 4px;
  }

  .chosen-results .group-option {
    padding-right: 15px;
    padding-left: 0;
  }

  &.chosen-container-active.chosen-with-drop .chosen-single div {
    border-right: none;
  }

  .chosen-search input {
    padding: 4px 5px 4px 20px;
    direction: rtl;
    background: url($chosen-sprite-path) no-repeat -28px -20px, $chosen-background;
  }
}
