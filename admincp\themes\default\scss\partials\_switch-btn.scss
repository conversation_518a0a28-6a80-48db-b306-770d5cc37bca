// on-off switch control
.prestashop-switch {
  position: relative;
  display: block;
  width: 100%;
  height: 22px;
  margin-top: 7px;

  &-nolabel {
    label {
      display: none;
    }
  }

  label {
    position: absolute;
    top: 50%;
    left: 0;
    z-index: 1;
    padding-left: 45px;
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    opacity: 0;
    transform: translateY(-50%);
  }

  .slide-button {
    position: absolute;
    top: 0;
    z-index: 0;
    display: block;
    width: 36px;
    height: 22px;
    background: #b3c7cd;
    border: 0;
    @include transition(0.25s ease-out);
    @include border-radius(1000px);

    &::after {
      position: absolute;
      top: 2px;
      left: 2px;
      width: 18px;
      height: 18px;
      content: "";
      background: #fff;
      @include border-radius(50%);
      @include transition(0.25s ease-out);
    }
  }

  &-center {
    .slide-button {
      position: inherit;
      margin: auto;
    }
  }

  input {
    position: absolute;
    left: 0;
    z-index: 3;
    width: 100%;
    height: 100%;
    margin: 0;
    cursor: pointer;
    opacity: 0;

    &:disabled {
      cursor: not-allowed;

      ~ .slide-button {
        opacity: 0.2;
      }
    }

    &:checked {
      z-index: 0;
    }

    &:last-of-type:checked {
      ~ label:last-of-type {
        opacity: 1;
      }

      &:disabled ~ label:last-of-type {
        opacity: 0.2;
      }

      ~ .slide-button {
        background: #b3c7cd;
      }
    }

    &:first-of-type:checked {
      ~ label:first-of-type {
        opacity: 1;
      }

      &:disabled ~ label:first-of-type {
        opacity: 0.2;
      }

      ~ .slide-button {
        background: $brand-success;

        &::after {
          transform: translateX(14px);
        }
      }
    }
  }
}
