<?php

use PrestaShop\PrestaShop\Adapter\Presenter\Order\OrderPresenter;
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email

 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * 
 *

 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */
class MyAccountControllerCore extends FrontController
{
    /** @var bool */
    public $auth = true;
    /** @var string */
    public $php_self = 'my-account';
    /** @var string */
    public $authRedirection = 'my-account';
    /** @var bool */
    public $ssl = true;
    public $order_presenter;

    /**
     * Assign template vars related to page content.
     *
     * @see FrontController::initContent()
     */
    public function initContent()
    {
        /*
        * @deprecated since 1.7.8
        */
        // 用户信息
        $customerName = $this->getTranslator()->trans(
            '%firstname% %lastname%',
            [
                '%firstname%' => $this->context->customer->firstname,
                '%lastname%' => $this->context->customer->lastname,
            ],
            'Modules.Customersignin.Admin'
        );

        if ($this->order_presenter === null) {
            $this->order_presenter = new OrderPresenter();
        }

        // 收货地址
        $id_address_delivery = (int) Address::getFirstCustomerAddressId((int) ($this->context->customer->id));
        $address_delivery = Db::getInstance()->getRow('
            SELECT a.*, cl.`name` AS country_name, s.`name` AS state_name
            FROM `' . _DB_PREFIX_ . 'address` a
            LEFT JOIN `' . _DB_PREFIX_ . 'country` c ON a.`id_country` = c.`id_country`
            LEFT JOIN `' . _DB_PREFIX_ . 'country_lang` cl ON c.`id_country` = cl.`id_country` AND cl.`id_lang` = ' . (int) $this->context->language->id . '
            LEFT JOIN `' . _DB_PREFIX_ . 'country_shop` cs ON c.`id_country` = c.`id_country` AND cs.`id_shop` = ' . (int) $this->context->shop->id . '
            LEFT JOIN `' . _DB_PREFIX_ . 'state` s ON a.`id_state` = s.`id_state`
            WHERE a.`id_address` = ' . (int) $id_address_delivery);

        // 账单地址
        $id_address_invoice = (int) Address::getFirstCustomerAddressId((int) ($this->context->customer->id));
        $address_invoice = Db::getInstance()->getRow('
        SELECT a.*, cl.`name` AS country_name, s.`name` AS state_name
        FROM `' . _DB_PREFIX_ . 'address` a
        LEFT JOIN `' . _DB_PREFIX_ . 'country` c ON a.`id_country` = c.`id_country`
        LEFT JOIN `' . _DB_PREFIX_ . 'country_lang` cl ON c.`id_country` = cl.`id_country` AND cl.`id_lang` = ' . (int) $this->context->language->id . '
        LEFT JOIN `' . _DB_PREFIX_ . 'country_shop` cs ON c.`id_country` = c.`id_country` AND cs.`id_shop` = ' . (int) $this->context->shop->id . '
        LEFT JOIN `' . _DB_PREFIX_ . 'state` s ON a.`id_state` = s.`id_state`
        WHERE a.`id_address` = ' . (int) $id_address_invoice);

        // 优惠卷
        $discount_code = Configuration::get('ASS_CART_RULES') ?: 'GLAMLORA5';

        // 通讯订阅
        $newsletterMessage = '';
        if ($this->context->cookie->__get('newsletterMessage')) {
            $newsletterMessage = $this->context->cookie->__get('newsletterMessage');
        }
        // 移除
        $this->context->cookie->__unset('newsletterMessage');

        // 注册成功
        $register_success = '';
        if ($this->context->cookie->__get('register_success')) {
            $register_success = $this->context->cookie->__get('register_success');
        }
        // 移除
        $this->context->cookie->__unset('register_success');

        // 用户最近的5个订单
        $orders = [];

        $customer_orders = Order::getCustomerLatestFiveOrders($this->context->customer->id);
        foreach ($customer_orders as $customer_order) {
            $order = new Order((int) $customer_order['id_order']);
            $tmp_order = $this->order_presenter->present($order);
            $orders[$customer_order['id_order']] = $tmp_order;
        }

        $this->context->smarty->assign([
            'logout_url' => $this->context->link->getPageLink('index', true, null, 'mylogout'),
            'customerName' => $customerName,
            'recent_orders' => $orders,
            'customerEmail' => $this->context->customer->email,
            'customerNewsletter' => $this->context->customer->newsletter,
            'address_delivery' => $address_delivery,
            'address_invoice' => $address_invoice,
            'discount_code' => $discount_code,
            'newsletterMessage' => $newsletterMessage,
            'register_success' => $register_success
        ]);

        parent::initContent();
        $this->setTemplate('customer/my-account');
    }

    public function getBreadcrumbLinks()
    {
        $breadcrumb = parent::getBreadcrumbLinks();

        $breadcrumb['links'][] = $this->addMyAccountToBreadcrumb();

        return $breadcrumb;
    }
}
