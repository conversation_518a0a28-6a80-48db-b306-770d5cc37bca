{extends file='customer/page.tpl'}

{block name='page_content'}
  <div id="account-pc">
    <div class="myPage" id="myPage">
      {* 左侧菜单 *}
      <div class="links" id="myAccount">
        {* 仪表盘 *}
        <a class="item {if $controller_name == 'myaccount'}current{/if}" id="identity-link"
          href="{$urls.pages.my_account}">
          <span class="link-item">
            <i class="material-icons">dashboard</i>
            {l s='Tableau de bord du compte' d='Shop.Theme.Customeraccount'}
          </span>
        </a>
        {* 收藏 *}
      {if !$configuration.is_catalog}
        <a class="item {if $controller_name == 'wishlist'}current{/if}"
          id="history-link" href="{$urls.pages.wishlist}">
          <span class="link-item">
            <i class="material-icons">&#xE87D;</i>
            {l s="Ma liste d'envies" d='Shop.Theme.Customeraccount'}
          </span>
        </a>
      {/if}
        {* 个人信息 *}
        <a class="item {if $controller_name == 'identity'}current{/if}" id="identity-link" href="{$urls.pages.identity}">
          <span class="link-item">
            <i class="material-icons">&#xE853;</i>
            {l s='Informations du compte' d='Shop.Theme.Customeraccount'}
          </span>
        </a>
        {* 地址信息 *}
        {if $customer.addresses|count}
          <a class="item {if $controller_name == 'addresses'}current{/if}" id="addresses-link"
            href="{$urls.pages.addresses}">
            <span class="link-item">
              <i class="material-icons">&#xE56A;</i>
              {l s="Carnet d'adresses" d='Shop.Theme.Customeraccount'}
            </span>
          </a>
        {else}
          <a class="item {if $controller_name == 'address'}current{/if}" id="address-link" href="{$urls.pages.address}">
            <span class="link-item">
              <i class="material-icons">&#xE567;</i>
              {l s='Ajouter une adresse' d='Shop.Theme.Customeraccount'}
            </span>
          </a>
        {/if}
        {* 历史订单 信息 *}
        {if !$configuration.is_catalog}
          <a class="item {if $controller_name == 'history' || $controller_name == 'orderdetail'}current{/if}"
            id="history-link" href="{$urls.pages.history}">
            <span class="link-item">
              <i class="material-icons">&#xE916;</i>
              {l s='Mes Commandes' d='Shop.Theme.Customeraccount'}
            </span>
          </a>
        {/if}
        {* 退货 *}
        {if $configuration.return_enabled && !$configuration.is_catalog}
          <a class="item {if $controller_name == 'orderfollow'}current{/if}" id="returns-link"
            href="{$urls.pages.order_follow}">
            <span class="link-item">
              <i class="material-icons">&#xE860;</i>
              {l s='Merchandise returns' d='Shop.Theme.Customeraccount'}
            </span>
          </a>
        {/if}

        {* 模板的钩子 *}
        {block name='display_customer_account'}
          {hook h='displayCustomerAccount'}
        {/block}
        {* 登出 *}
        <a class="item" href="{$urls.actions.logout}">
          <span class="link-item">
            <i class="material-icons">&#xE860;</i>
            {l s='Déconnexion' d='Shop.Theme.Actions'}
          </span>
        </a>

      </div>
      {* 右侧内容，可替换  *}
      <div class="infoContent">
        {block name="module_content"}
          <div class="my-account" id="myAccountContent">
            <div class="dashboard">
              <div class="page-title">
                <h1>{l s='Mon tableau de bord' d='Shop.Theme.Customeraccount'}</h1>
              </div>
              <div class="welcome-msg">
                <p class="hello"><strong>{l s='Bonjour' d='Shop.Theme.Customeraccount'}, {$customerName}!</strong></p>
                <p>{l s="Depuis le tableau de bord 'Mon compte', vous pouvez avoir un aperçu de vos récentes activités et mettre à jour les informations de votre compte. Sélectionnez un lien ci-dessous pour voir ou modifier les informations."
              activity
              and update your account information. Select a link below to view or edit information.'
                      d='Shop.Theme.Customeraccount'}</p>
              </div>
              <div class="box-account box-info">
                <div class="box-head">
                  <h2>{l s='Informations du compte' d='Shop.Theme.Customeraccount'}</h2>
                </div>
                <div class="col2-set">
                  <div class="col-1">
                    <div class="box">
                      <div class="box-title">
                        <h3>{l s='Adresse de facturation par défaut' d='Shop.Theme.Customeraccount'}</h3>
                        <a href="{$urls.pages.identity}">{l s='Éditer' d='Shop.Theme.Customeraccount'}</a>
                      </div>
                      <div class="box-content">
                        <p>
                          {$customerName}<br>
                          {$customerEmail}<br>
                          <a href="{$urls.pages.identity}">{l s='Modifier le mot de passe' d='Shop.Theme.Customeraccount'}</a>
                        </p>
                      </div>
                    </div>
                  </div>
                  <div class="col-2">
                    <div class="box">
                      <div class="box-title">
                        <h3>{l s="Bulletins d'information" d='Shop.Theme.Customeraccount'}</h3>
                        <a href="{$urls.pages.identity}">{l s='Éditer' d='Shop.Theme.Customeraccount'}</a>
                      </div>
                      <div class="box-content">
                        {if $customerNewsletter}
                          <p>
                            {l s='You are currently subscribed to any newsletter.' d='Shop.Theme.Customeraccount'}</p>
                        {else}
                          <p>
                            {l s="Vous n'êtes abonné à aucune newsletter." d='Shop.Theme.Customeraccount'}</p>
                        {/if}
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col2-set">
                  <div class="box">
                    <div class="box-title">
                      <h3>{l s="Carnet d'adresses" d='Shop.Theme.Customeraccount'}</h3>
                      <a href="{$urls.pages.addresses}">{l s='Gérer les adresses' d='Shop.Theme.Customeraccount'}</a>
                    </div>
                    <div class="box-content">
                      <div class="col-1">
                        <h4>{l s='Adresse de facturation par défaut' d='Shop.Theme.Customeraccount'}</h4>
                        {if $address_delivery}
                          <address>
                            {$address_delivery.firstname} {$address_delivery.lastname}<br>
                            {if $address_delivery.company}{$address_delivery.company}<br>{/if}
                            {$address_delivery.address1}{if $address_delivery.address2}{$address_delivery.address2}{/if}<br>
                            {$address_delivery.city},{$address_delivery.city}{$address_delivery.postcode}<br>
                            {$address_delivery.country_name}{$address_delivery.state_name}<br>
                            {if $address_delivery.phone}{l s='T: ' d='Shop.Theme.Customeraccount'}{$address_delivery.phone}{/if}
                            <br>
                            <a
                              href="{url entity='address' params=['id_address' => $address_delivery.id_address]}">{l s='Edit Address' d='Shop.Theme.Customeraccount'}</a>
                          </address>
                        {else}
                          <address>
                            <p>
                              {l s='Currently, you do not have a delivery address. Please add an address first.' d='Shop.Theme.Customeraccount'}
                            </p>
                            <a href="{$urls.pages.address}">{l s='Add New Address' d='Shop.Theme.Customeraccount'}</a>
                          </address>
                        {/if}
                      </div>
                      <div class="col-2">
                        <h4>{l s='Adresse de livraison par défaut' d='Shop.Theme.Customeraccount'}</h4>
                        {if $address_invoice}
                          <address>
                            {$address_invoice.firstname} {$address_invoice.lastname}<br>
                            {if $address_invoice.company}{$address_invoice.company}<br>{/if}
                            {$address_invoice.address1}{if $address_invoice.address2}{$address_invoice.address2}{/if}<br>
                            {$address_invoice.city},{$address_invoice.city}{$address_invoice.postcode}<br>
                            {$address_invoice.country_name}{$address_invoice.state_name}<br>
                            {if $address_invoice.phone}{l s='T: ' d='Shop.Theme.Customeraccount'}{$address_invoice.phone}{/if}
                            <br>
                            <a
                              href="{url entity='address' params=['id_address' => $address_invoice.id_address]}">{l s='Edit Address' d='Shop.Theme.Customeraccount'}</a>
                          </address>
                        {else}
                          <address>
                            <p>
                              {l s='Currently, you do not have an invoice address. Please add an address first.' d='Shop.Theme.Customeraccount'}
                            </p>
                            <a href="{$urls.pages.address}">{l s='Add New Address' d='Shop.Theme.Customeraccount'}</a>
                          </address>
                        {/if}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        {/block}
      </div>
    </div>
  </div>
  <div id="account-phone">
    <section class="main-content" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
      <div class="my-account" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
        <div class="dashboard" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
          <div class="account-init-top" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
            <div class="welcome-msg" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
              <p class="hello" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"><strong
                  data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                  data-immersive-translate-paragraph="1">Hello, {$customer.firstname} {$customer.lastname}!</strong></p>
              <p class="welcome-coupon" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                data-immersive-translate-paragraph="1">Nouveau Rabais Enregistré: {$discount_code}</p>
            </div>

          </div>
          {* 订单详情 *}
          <div class="account-init-order" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
            <div class="account-init-order-title" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
              <span data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                data-immersive-translate-paragraph="1">Mes Commandes</span>
              <a href="/sales/order/history/" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                data-immersive-translate-paragraph="1">{l s='Voir tout' d='Shop.Theme.Actions'}</a>
            </div>
            <div class="account-orders-main" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
              <a href="/sales/order/history/?status=unpaid" class="orders-unpaid active"
                data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
                <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1"
                  xmlns="http://www.w3.org/2000/svg" p-id="9235" width="32" height="32">
                  <path
                    d="M681.334568 545.866914h135.467655a33.866914 33.866914 0 0 1 0 67.733827h-135.467655a33.866914 33.866914 0 0 1 0-67.733827z m0 112.889712h135.467655a33.866914 33.866914 0 0 1 0 67.733827h-135.467655a33.866914 33.866914 0 0 1 0-67.733827z m237.068395 225.779424h-812.805926a90.31177 90.31177 0 0 1-90.31177-90.31177v-564.44856a90.31177 90.31177 0 0 1 90.31177-90.31177h812.805926a90.31177 90.31177 0 0 1 90.31177 90.31177v564.44856a90.31177 90.31177 0 0 1-90.31177 90.31177z m22.577943-632.182388a45.155885 45.155885 0 0 0-45.155885-45.155885h-767.650042a45.155885 45.155885 0 0 0-45.155885 45.155885v519.292676a45.155885 45.155885 0 0 0 45.155885 45.155885h767.650042a45.155885 45.155885 0 0 0 45.155885-45.155885v-519.292676z m-903.117697 90.31177h925.695639v67.733827h-925.695639v-67.733827z"
                    fill="#2C2C2C" p-id="9236"></path>
                </svg>
                <span data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                  data-immersive-translate-paragraph="1">Unpaid</span>
              </a>

              <a href="/sales/order/history/?status=processing" class="orders-processing"
                data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
                <svg t="1689991827218" class="icon" viewBox="0 0 1024 1024" version="1.1"
                  xmlns="http://www.w3.org/2000/svg" p-id="7055" width="32" height="32">
                  <path
                    d="M851.781818 847.127273h-44.218182v-76.8c0-107.054545-58.181818-204.8-155.927272-253.672728l-6.981819-4.654545 6.981819-4.654545c95.418182-48.872727 155.927273-146.618182 155.927272-253.672728V176.872727h44.218182c16.290909 0 27.927273-11.636364 27.927273-27.927272s-11.636364-27.927273-27.927273-27.927273H195.490909c-16.290909 0-27.927273 11.636364-27.927273 27.927273s11.636364 27.927273 27.927273 27.927272h44.218182v76.8c0 107.054545 58.181818 204.8 153.6 253.672728l6.981818 4.654545-6.981818 4.654545c-95.418182 48.872727-153.6 146.618182-153.6 253.672728v76.8H195.490909c-16.290909 0-27.927273 11.636364-27.927273 27.927272s11.636364 27.927273 27.927273 27.927273h656.290909c16.290909 0 27.927273-11.636364 27.927273-27.927273s-11.636364-27.927273-27.927273-27.927272zM295.563636 253.672727V176.872727h456.145455v76.8c0 125.672727-102.4 228.072727-228.072727 228.072728s-228.072727-102.4-228.072728-228.072728z m456.145455 593.454546H295.563636v-76.8c0-125.672727 102.4-228.072727 228.072728-228.072728s228.072727 102.4 228.072727 228.072728v76.8z"
                    p-id="7056" fill="#2C2C2C"></path>
                  <path
                    d="M651.636364 328.145455c-9.309091-13.963636-27.927273-16.290909-39.563637-6.981819-58.181818 41.890909-132.654545 32.581818-174.545454 2.327273-13.963636-9.309091-30.254545-6.981818-39.563637 6.981818-9.309091 13.963636-6.981818 30.254545 6.981819 39.563637 30.254545 23.272727 74.472727 37.236364 121.018181 37.236363 41.890909 0 83.781818-11.636364 123.345455-39.563636 9.309091-6.981818 11.636364-25.6 2.327273-39.563636zM581.818182 740.072727h-93.090909c-16.290909 0-30.254545 13.963636-30.254546 30.254546s13.963636 30.254545 30.254546 30.254545h93.090909c16.290909 0 30.254545-13.963636 30.254545-30.254545s-13.963636-30.254545-30.254545-30.254546z"
                    p-id="7057" fill="#2C2C2C"></path>
                </svg>
                <span data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                  data-immersive-translate-paragraph="1">Processing</span>
              </a>
              <a href="/sales/order/history/?status=shipped" class="orders-shipped"
                data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
                <svg t="1689991704235" class="icon" viewBox="0 0 1024 1024" version="1.1"
                  xmlns="http://www.w3.org/2000/svg" p-id="9335" width="32" height="32">
                  <path
                    d="M1007.991467 497.288533c10.461867 15.735467 16.0256 34.218667 16.008533 53.111467V742.4a96 96 0 0 1-96 96h-36.471467a128 128 0 0 1-247.04 0h-168.96a128 128 0 0 1-247.04 0H192A96 96 0 0 1 96 742.4v-96A96 96 0 0 1 0 550.4V198.4A96 96 0 0 1 96 102.4H576a96 96 0 0 1 96 96v64h128c32.170667 0 62.208 16.093867 79.991467 42.871467l128 192zM960 742.4V550.4a32 32 0 0 0-5.444267-17.595733l-128-192a32 32 0 0 0-26.555733-14.404267h-128V550.4a96 96 0 0 1-96 96H160V742.4c0 17.681067 14.336 32 32 32h36.488533a128 128 0 0 1 247.022934 0h168.96a128 128 0 0 1 247.057066 0h36.471467A32 32 0 0 0 960 742.4zM96 582.4H576a32 32 0 0 0 32-32V198.4A32 32 0 0 0 576 166.4H96c-17.681067 0-32 14.336-32 32V550.4a32.017067 32.017067 0 0 0 32 32z m256 288a64 64 0 1 0 0-128 64 64 0 0 0 0 128zM768 870.4a64 64 0 1 0 0-128 64 64 0 0 0 0 128z"
                    fill="#2C2C2C" p-id="9336"></path>
                </svg>
                <span data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                  data-immersive-translate-paragraph="1">Shipped</span>
              </a>
            </div>
          </div>


          <div class="box-account box-info" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">

            <div class="col2-set-dash" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">

              <div class="box-title-dash myinfomation"
                data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
                <a href="/customer/account/edit/"
                  data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"><svg t="*************"
                    class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="9748"
                    width="32" height="32">
                    <path
                      d="M963.304273 982.126574c0 22.754101-18.255436 41.204869-40.78098 41.204869-22.52655 0-40.781987-18.450768-40.781987-41.204869l-0.358443-3.846217c0-1.630112 0.292997-3.194777 0.456109-4.792669-25.460549-180.860821-178.936705-313.768775-365.828637-315.853989-1.499219 0.03222-2.933999 0.228558-4.433219 0.228558s-2.935006-0.195331-4.400999-0.228558c-184.740265 2.086221-336.750428 132.025942-364.785526 309.757432 0.913225 3.488781 1.596885 7.107447 1.596885 10.888219l0.39167 3.878437c0 23.112544-18.549441 41.858323-41.433427 41.858323-22.852774 0-41.433427-18.744772-41.433427-41.858323l-0.03222-0.390663-0.782333 0c0.097666-1.206222 0.358443-2.380225 0.488329-3.586447 0-3.455554 0.521555-6.714771 1.303888-9.942774C83.710605 801.52653 194.319561 666.695467 344.830505 608.635708c-87.627303-56.721638-144.77283-156.964937-144.77283-271.486563 0-177.112269 136.232617-320.712104 311.551663-320.712104 175.286826 0 311.518436 143.599835 311.518436 320.712104 0 114.488399-57.081088 214.699479-144.676172 271.421117 149.467833 57.60365 259.555234 190.869041 281.984118 355.920096 1.532446 4.335553 2.54233 8.931883 2.54233 13.788991L963.304273 982.126574 963.304273 982.126574 963.304273 982.126574zM751.506574 337.246811c0-133.916832-107.447405-242.472793-239.99591-242.472793-132.548504 0-239.99591 108.588181-239.99591 242.472793 0 133.918845 107.447405 242.4738 239.99591 242.4738C644.025942 579.720611 751.506574 471.165657 751.506574 337.246811L751.506574 337.246811 751.506574 337.246811z"
                      fill="#2c2c2c" p-id="9749"></path>
                  </svg>
                  <h3 data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                    data-immersive-translate-paragraph="1">Informations du compte</h3>
                </a>
              </div>

            </div>
            <div class="col2-set-dash" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">

              <div class="box-title-dash mynewsletter"
                data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
                <a href="/newsletter/manage/"
                  data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"><svg t="1690011825289"
                    class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="13192"
                    width="32" height="32">
                    <path
                      d="M820.816842 116.143158H203.183158a134.736842 134.736842 0 0 0-134.736842 134.736842v501.490526a134.736842 134.736842 0 0 0 134.736842 134.736842h617.633684a134.736842 134.736842 0 0 0 134.736842-134.736842V250.88a134.736842 134.736842 0 0 0-134.736842-134.736842z m80.842105 636.227368a80.842105 80.842105 0 0 1-80.842105 80.842106H203.183158a80.842105 80.842105 0 0 1-80.842105-80.842106V250.88a80.842105 80.842105 0 0 1 80.842105-80.842105h617.633684a80.842105 80.842105 0 0 1 80.842105 80.842105z"
                      fill="#2C2C2C" p-id="13193"></path>
                    <path
                      d="M766.383158 304.774737L512 496.101053l-254.383158-191.326316a26.947368 26.947368 0 1 0-32.336842 43.115789l269.473684 203.452632a26.947368 26.947368 0 0 0 32.336842 0l269.473685-203.452632a26.947368 26.947368 0 0 0-32.336843-43.115789z"
                      fill="#2C2C2C" p-id="13194"></path>
                  </svg>
                  <h3 data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                    data-immersive-translate-paragraph="1">Bulletins d'information</h3>
                </a>
              </div>

            </div>
            <div class="col2-set-dash" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">

              <div class="box-title-dash edit-account"
                data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
                <a href="/customer/address/" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
                  <svg t="*************" class="icon" viewBox="0 0 1024 1024" version="1.1"
                    xmlns="http://www.w3.org/2000/svg" p-id="16758" width="32" height="32">
                    <path
                      d="M518.4 48c-214.4 0-390.4 176-390.4 393.6 0 48 16 99.2 41.6 156.8 28.8 57.6 70.4 118.4 118.4 182.4 35.2 41.6 73.6 83.2 108.8 121.6 12.8 12.8 25.6 25.6 35.2 35.2 6.4 6.4 12.8 9.6 12.8 12.8l0 0c38.4 38.4 102.4 38.4 137.6 0 3.2-3.2 6.4-6.4 12.8-12.8 9.6-9.6 22.4-22.4 35.2-35.2 38.4-38.4 73.6-80 108.8-121.6 51.2-60.8 92.8-124.8 118.4-182.4 28.8-57.6 41.6-108.8 41.6-156.8C908.8 224 732.8 48 518.4 48zM822.4 576c-25.6 54.4-64 112-115.2 172.8-35.2 41.6-70.4 83.2-105.6 118.4-12.8 12.8-25.6 25.6-35.2 35.2-6.4 6.4-9.6 9.6-12.8 12.8-19.2 19.2-51.2 19.2-70.4 0l0 0c-3.2-3.2-6.4-6.4-12.8-12.8-9.6-9.6-22.4-22.4-35.2-35.2-35.2-38.4-73.6-76.8-105.6-118.4-48-60.8-86.4-118.4-115.2-172.8-25.6-51.2-38.4-96-38.4-134.4 0-192 153.6-345.6 342.4-345.6 188.8 0 342.4 153.6 342.4 345.6C860.8 480 848 524.8 822.4 576z"
                      p-id="16759" fill="#2c2c2c"></path>
                    <path
                      d="M518.4 262.4c-96 0-169.6 76.8-169.6 172.8 0 96 76.8 172.8 169.6 172.8s169.6-76.8 169.6-172.8C688 339.2 614.4 262.4 518.4 262.4zM518.4 556.8c-67.2 0-121.6-54.4-121.6-124.8s54.4-124.8 121.6-124.8c67.2 0 121.6 54.4 121.6 124.8S585.6 556.8 518.4 556.8z"
                      p-id="16760" fill="#2c2c2c"></path>
                  </svg>

                  <h3 data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                    data-immersive-translate-paragraph="1">Carnet d'adresses</h3>

                </a>
              </div>

            </div>
            {hook h='displayCustomerAccountMobile'}
            <div class="col2-set-dash" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
              <div class="box-title-dash logout" data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa">
                <a href="{$urls.actions.logout}"
                  data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"><svg t="*************"
                    class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="12979"
                    width="32" height="32">
                    <path
                      d="M818.402 185.171l-0.062 0.066C812.47 178.364 803.747 174 794 174c-17.673 0-32 14.327-32 32 0 10.488 5.046 19.798 12.843 25.634l-0.213 0.227C849.329 301.919 896 401.508 896 512c0 212.077-171.923 384-384 384S128 724.077 128 512c0-110.492 46.671-210.081 121.37-280.139l-0.213-0.227C256.954 225.798 262 216.488 262 206c0-17.673-14.327-32-32-32-9.747 0-18.47 4.364-24.34 11.238l-0.062-0.066C118.449 266.906 64 383.092 64 512c0 247.424 200.576 448 448 448s448-200.576 448-448c0-128.908-54.449-245.094-141.598-326.829z"
                      p-id="12980" fill="#2C2C2C"></path>
                    <path
                      d="M512 480c17.6 0 32-14.4 32-32V96c0-17.6-14.4-32-32-32s-32 14.4-32 32v352c0 17.6 14.4 32 32 32z"
                      p-id="12981" fill="#2C2C2C"></path>
                  </svg>
                  <h3 data-immersive-translate-walked="17d433b0-a403-410a-8a46-00f4ddcededa"
                    data-immersive-translate-paragraph="1">Déconnexion</h3>
                </a>
              </div>
            </div>


          </div>
        </div>
      </div>
    </section>
  </div>
{/block}


{block name='page_footer'}

{/block}