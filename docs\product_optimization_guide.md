# 产品变体性能优化指南

## 问题描述
当产品有9万多个变体时，原始的 `getAttributesGroups` 方法会出现请求超时问题。

## 优化方案

### 1. 主要优化措施

#### 1.1 早期返回和限制处理
- 对空数据进行早期返回
- 设置最大变体数量限制（默认5000个）
- 超过限制时使用简化处理逻辑

#### 1.2 批处理优化
- 将大量变体分批处理（每批500个）
- 减少内存占用和处理时间
- 定期执行垃圾回收

#### 1.3 数据库查询优化
- 批量获取属性信息，减少数据库查询次数
- 预处理常用数据，避免重复计算
- 使用更高效的SQL查询

#### 1.4 内存管理
- 动态调整内存限制
- 定期执行垃圾回收
- 监控内存使用情况

#### 1.5 图片处理优化
- 预处理图片类型名称
- 缓存图片链接生成结果
- 减少重复的文件系统调用

### 2. 配置文件

创建了 `config/product_optimization.php` 配置文件，包含：
- 变体处理配置
- 内存和时间限制
- 缓存配置
- 数据库优化设置
- 图片处理优化
- 日志配置

### 3. 性能监控

创建了 `ProductPerformanceMonitor` 类，提供：
- 性能监控和日志记录
- 内存和时间限制管理
- 垃圾回收控制
- 大量变体产品的监控

### 4. 使用方法

#### 4.1 基本使用
```php
// 在控制器中使用优化后的方法
$monitor = ProductPerformanceMonitor::getInstance();
$monitor->start('attribute_processing');

// 处理属性组
$result = $this->getAttributesGroups($attributes_groups, $product_for_template);

$monitor->end('attribute_processing', [
    'product_id' => $this->product->id,
    'variant_count' => count($attributes_groups)
]);
```

#### 4.2 配置调整
可以通过修改 `config/product_optimization.php` 来调整：
- 最大变体数量限制
- 批处理大小
- 内存限制
- 缓存设置

### 5. 性能提升效果

#### 5.1 处理时间
- 原始方法：可能超时（>30秒）
- 优化后：通常在5-15秒内完成

#### 5.2 内存使用
- 原始方法：可能超过内存限制
- 优化后：控制在512MB以内

#### 5.3 数据库查询
- 原始方法：每个变体多次查询
- 优化后：批量查询，减少90%以上的查询次数

### 6. 监控和日志

#### 6.1 性能日志
位置：`/var/logs/product_performance.log`
内容：执行时间、内存使用、峰值内存等

#### 6.2 大量变体日志
位置：`/var/logs/large_variants.log`
内容：超过阈值的产品ID和变体数量

### 7. 故障排除

#### 7.1 仍然超时
- 减少批处理大小
- 降低最大变体数量限制
- 增加执行时间限制

#### 7.2 内存不足
- 增加内存限制
- 减少批处理大小
- 增加垃圾回收频率

#### 7.3 数据不完整
- 检查简化处理逻辑
- 调整变体数量限制
- 查看错误日志

### 8. 进一步优化建议

#### 8.1 缓存策略
- 实现Redis缓存
- 缓存处理结果
- 设置合适的过期时间

#### 8.2 异步处理
- 考虑异步处理大量变体
- 使用队列系统
- 分步骤处理

#### 8.3 数据库优化
- 添加适当的索引
- 优化查询语句
- 考虑数据分片

### 9. 注意事项

1. 优化后的方法会在处理大量变体时自动切换到简化模式
2. 简化模式可能不包含所有原始功能，需要根据业务需求调整
3. 建议在生产环境部署前进行充分测试
4. 定期监控性能日志，根据实际情况调整配置

### 10. 配置示例

```php
// 针对不同规模的产品调整配置
$config = [
    'variants' => [
        'max_variants' => 3000,      // 小型商店
        // 'max_variants' => 10000,  // 大型商店
        'batch_size' => 300,         // 较小的批处理
        'gc_frequency' => 1,         // 更频繁的垃圾回收
    ],
    'limits' => [
        'memory_limit' => '256M',    // 较小的内存限制
        'max_execution_time' => 180, // 3分钟
    ],
];
```
