<?php
/**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email

 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 *
 *

 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 */

namespace PrestaShop\PrestaShop\Adapter\BestSales;

use PrestaShop\PrestaShop\Core\Product\Search\ProductSearchContext;
use PrestaShop\PrestaShop\Core\Product\Search\ProductSearchProviderInterface;
use PrestaShop\PrestaShop\Core\Product\Search\ProductSearchQuery;
use PrestaShop\PrestaShop\Core\Product\Search\ProductSearchResult;
use PrestaShop\PrestaShop\Core\Product\Search\SortOrder;
use ProductSale;
use Symfony\Contracts\Translation\TranslatorInterface;
use Tools;

class BestSalesProductSearchProvider implements ProductSearchProviderInterface
{
    /**
     * @var TranslatorInterface
     */
    private $translator;

    public function __construct(
        TranslatorInterface $translator
    ) {
        $this->translator = $translator;
    }

    /**
     * @param ProductSearchContext $context
     * @param ProductSearchQuery $query
     *
     * @return ProductSearchResult
     */
    public function runQuery(
        ProductSearchContext $context,
        ProductSearchQuery $query
    ) {
        $sortBySales = (new SortOrder('product', 'sales', 'desc'))->setLabel(
            $this->translator->trans('Sales, highest to lowest', [], 'Shop.Theme.Catalog')
        );

        if (!Tools::getValue('order', 0)) {
            $query->setSortOrder($sortBySales);
        }

        // 设置查询参数
        $page = $query->getPage();
        $resultsPerPage = $query->getResultsPerPage();
        $orderBy = $query->getSortOrder()->toLegacyOrderBy();
        $orderWay = $query->getSortOrder()->toLegacyOrderWay();
        $id_lang = $context->getIdLang();

        //获取分类
        $categoryId = json_decode(\Context::getContext()->cookie->id_categories, true);
        $categoryIdStr = is_array($categoryId) ? implode(',', $categoryId) : $categoryId;
        $cacheKey = _PS_REDIS_BEST_SELLER_ .md5($categoryIdStr . '_' . $id_lang . '_' . $page . '_' . $resultsPerPage . '_' . $orderBy . '_' . $orderWay);
        $redis = Tools::getRedis();
        if ($redis->get($cacheKey)) {
            // 从 redis 读取缓存
            $products = unserialize($redis->get($cacheKey));
        } else {
            // 如果 redis 缓存不存在，查询数据库并缓存到 redis
            $products = $this->getProductsFromDatabase($id_lang, $page, $resultsPerPage, $orderBy, $orderWay, $categoryId);
            $redis->set($cacheKey, serialize($products), 'EX',14400);   // 设置缓存过期时间为 4 小时
        }

        $count = (int) ProductSale::getNbSales();

        $result = new ProductSearchResult();

        if (!empty($products)) {
            $result
                ->setProducts($products)
                ->setTotalProductsCount($count);

            $result->setAvailableSortOrders(
                [
                    $sortBySales,
                    (new SortOrder('product', 'name', 'asc'))->setLabel(
                        $this->translator->trans('Name, A to Z', [], 'Shop.Theme.Catalog')
                    ),
                    (new SortOrder('product', 'name', 'desc'))->setLabel(
                        $this->translator->trans('Name, Z to A', [], 'Shop.Theme.Catalog')
                    ),
                    (new SortOrder('product', 'price', 'asc'))->setLabel(
                        $this->translator->trans('Price, low to high', [], 'Shop.Theme.Catalog')
                    ),
                    (new SortOrder('product', 'price', 'desc'))->setLabel(
                        $this->translator->trans('Price, high to low', [], 'Shop.Theme.Catalog')
                    ),
                ]
            );
        }
        return $result;
    }

    public function getProductsFromDatabase($id_lang, $page, $resultsPerPage, $orderBy, $orderWay, $categoryId)
    {
        if (
            !$products = ProductSale::getBestSales(
                $id_lang,
                $page,
                $resultsPerPage,
                $orderBy,
                $orderWay,
                $categoryId
            )
        ) {
            $products = [];
        }
        return $products;
    }

}