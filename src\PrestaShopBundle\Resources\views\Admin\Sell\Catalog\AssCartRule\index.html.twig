{#**
 * Copyright since 2007 PrestaShop SA and Contributors
 * PrestaShop is an International Registered Trademark & Property of PrestaShop SA
 *
 * NOTICE OF LICENSE
 *
 * This source file is subject to the Open Software License (OSL 3.0)
 * that is bundled with this package in the file LICENSE.md.
 * It is also available through the world-wide-web at this URL:
 * https://opensource.org/licenses/OSL-3.0
 * If you did not receive a copy of the license and are unable to
 * obtain it through the world-wide-web, please send an email

 *
 * DISCLAIMER
 *
 * Do not edit or add to this file if you wish to upgrade PrestaShop to newer
 * versions in the future. If you wish to customize PrestaShop for your
 * 
 *

 * @copyright Since 2007 PrestaShop SA and Contributors
 * @license   https://opensource.org/licenses/OSL-3.0 Open Software License (OSL 3.0)
 *#}

{% extends '@PrestaShop/Admin/layout.html.twig' %}
{% block content %}
<div class="card js-grid-panel" data-hook-name="export">
    <div class="card-body col-lg-12">
        <form action="{{ path('admin_ass_rules_edit') }}" method="post" class="form-inline" id="search-form">
            <div class="col-md-5">
                <div class="row">
                    <div class="col-md-12">
                        <div class="input-group">
                            <label class="form-control-label">User Rules:</label>
                            <select name="code" class="form-control col-sm select2"  required>
                            {% for cart_rules in cart_rule  %}
                                <option value="{{cart_rules.code}}" {% if cart_rules.code == rule_code %} selected  {% endif %} >{{cart_rules.code}}</option>
                            {% endfor %}
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="row">
                    <button type="submit" name="submitExport" id="submitExport" class="btn btn-primary">Edit</button>
                </div>
            </div>
        </form>
    </div>

<script>
$('.select2').select2();
</script>

{% endblock %}
