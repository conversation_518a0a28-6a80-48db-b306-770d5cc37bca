<?php
// 图片域名
define('_PS_IMAGE_DOMAIN_', 'image.glamlora.fr');
// define('_PS_IMAGE_DOMAIN_', 's3.us-east-1.amazonaws.com/image.glamlora.fr');

// 网站域名
define('_PS_DOMAIN_', 'www.glamlora.fr');

// 品牌
define('_PS_BRAND_', 'Glamlora');

// Elasticsearch 索引名称
// define('_PS_ES_INDEX_NAME_', 'glamlora-fr-search-index');
define('_PS_ES_INDEX_NAME_', 'glamlora-fr-search-index-test');

// 开发模式
if (isset($_GET['pro_dev']) && $_GET['pro_dev'] == '147258369') {
    define('_PS_MODE_DEV_', true);
}else{
    define('_PS_MODE_DEV_', true);
}

// 兼容性警告
define('_PS_DISPLAY_COMPATIBILITY_WARNING_', false);

// 调试分析
define('_PS_DEBUG_PROFILING_', false);

// 当前站点国家
define('_PS_COUNTRY_', 'FR');

// 默认语言
define('_PS_LANG_LOCALE_', 'fr-FR');

// 快捷筛选地址radar
define('_PS_RADAR_KEY_', 'prj_live_sk_c56949086cb1914ac0757cf38d542043cab8e78b');

// paypal分期支付的国家
define('_PS_PAYPAL_INSTALLMENT_COUNTRY_', 'FR');

// 评论google翻译
define('_PS_FANYI_KEY_', 'AIzaSyAprhh2Z5_bJByA0t9AtegfgAxhm-lvILc');

// 目标语种
define('_PS_COUNTRY_FANYI_', 'fr');
/*********************************
 * Redis KEY 设置
 *********************************/
//redis 站点前缀
define('_PS_REDIS_PREFIX_', 'glamlora_fr_');
/** Product redis keys */
// 产品特性
define('_PS_REDIS_PRODUCT_FEATURE_', _PS_REDIS_PREFIX_ . 'product_features_');
// 产品属性
define('_PS_REDIS_PRODUCT_ATTRIBUTE_', _PS_REDIS_PREFIX_ . 'product_attributes_');
// 搜索页的产品详情
define('_PS_REDIS_PRODUCT_SEARCH_', _PS_REDIS_PREFIX_ . 'product_search_');
// 产品详情页的产品详情
define('_PS_REDIS_PRODUCT_DETAIL_', _PS_REDIS_PREFIX_ . 'product_detail_');
// 产品详情页的变体信息
define('_PS_REDIS_PRODUCT_VARIANTS_', _PS_REDIS_PREFIX_ . 'query_product_variants_');
// 产品详情页的属性信息
define('_PS_REDIS_PRODUCT_HAS_ATTRIBUTE_', _PS_REDIS_PREFIX_ . 'query_product_has_attributes_');
/** ES redis keys */
// 特性规则
define('_PS_REDIS_ES_FEATURE_RULE_', _PS_REDIS_PREFIX_ . 'es_feature_rules_');
// 属性规则
define('_PS_REDIS_ES_ATTRIBUTE_RULE_', _PS_REDIS_PREFIX_ . 'es_attribute_rules_');
// 搜索权重规则
define('_PS_REDIS_ES_SEARCH_RULE_', _PS_REDIS_PREFIX_ . 'es_search_rules_');
// 关键词排除配置
define('_PS_REDIS_ES_KEYWORD_RULE_', _PS_REDIS_PREFIX_ . 'es_keyword_rules_');
/** 其他 redis keys */
// 404页面产品
define('_PS_REDIS_404_PRODUCT_', _PS_REDIS_PREFIX_ . '404_product_');
// 衣服尺寸表
define('_PS_REDIS_CLOTHING_SIZE_CHART_', _PS_REDIS_PREFIX_ . 'clothing_size_chart_');
// 热卖产品
define('_PS_REDIS_BEST_SELLER_', _PS_REDIS_PREFIX_ . 'best_seller_');
// 翻译包translate
define('_PS_REDIS_TRANSLATE_', _PS_REDIS_PREFIX_ . 'translate_');
// 翻译文本
define('_PS_REDIS_TRANSLATE_KEYWORD_', _PS_REDIS_PREFIX_ . 'translate_keyword_');
