<?php

declare(strict_types=1);

namespace PrestaShopBundle\Controller\Admin\Sell\Catalog\ProductManage;

use Db;
use Image;
use ImageAwsApiCore;
use ImportProduct;
use PrestaShopBundle\Controller\Admin\FrameworkBundleAdminController;
use Product;
use Symfony\Component\HttpFoundation\Request;
use Tools;
use Exception;
use ProductCore;
use MyElasticsearch;

class EditProductController extends FrameworkBundleAdminController
{

    /** @var bool $is_just_update_redis 是否更新完直接更新redis */
    private $is_just_update_redis = true;

    // 产品编辑页面
    public function ShowAction(int $productId, Request $request)
    {
        $legacyContextService = $this->get('prestashop.adapter.legacy.context');
        $id_lang = $legacyContextService->getContext()->language->id;

        // 特性
        $query_features = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('feature')
                ->select('code, id_feature')
        );
        $features = array_column($query_features, 'code', 'id_feature');

        // 特性值
        $query_values = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('feature_value', 'fv')
                ->leftJoin('feature_value_lang', 'fvl', 'fv.id_feature_value = fvl.id_feature_value AND fvl.id_lang = ' . $id_lang)
                ->select('fv.id_feature, fv.id_feature_value, fvl.value')
        );
        // 整理
        $feature_values = [];
        foreach ($query_values as $row) {
            $feature_values[$row['id_feature']][$row['id_feature_value']] = $row['value'];
        }

        // 属性组
        $query_attribute_groups = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('attribute_group', 'ag')
                ->leftJoin('attribute_group_lang', 'agl', 'ag.id_attribute_group = agl.id_attribute_group AND agl.id_lang = ' . $id_lang)
                ->select('ag.id_attribute_group, ag.group_type, ag.position, agl.name')
                ->where('id_lang = ' . $id_lang)
        );
        $attribute_groups = [];
        foreach ($query_attribute_groups as $row) {
            $attribute_groups[$row['id_attribute_group']] = [
                'name' => $row['name'],
                'group_type' => $row['group_type'],
                'position' => $row['position']
            ];
        }

        // 属性
        $query_attributes = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('attribute', 'a')
                ->leftJoin('attribute_lang', 'al', 'a.id_attribute = al.id_attribute AND al.id_lang = ' . $id_lang)
                ->orderBy('al.name ASC')
                ->select('a.id_attribute, a.id_attribute_group, al.name')
        );
        $attributes = [];
        foreach ($query_attributes as $row) {
            $attributes[$row['id_attribute_group']][$row['id_attribute']] = $row['name'];
        }

        // 分类
        $query_categories = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('category', 'c')
                ->leftJoin('category_lang', 'cl', 'c.id_category = cl.id_category AND cl.id_lang = ' . $id_lang)
                ->select('c.id_category, c.id_parent, cl.name')
                ->where('c.id_parent != 0 OR c.id_category = 1')
        );
        $categories = array_column($query_categories, 'name', 'id_category');
        // 整理成树状结构
        $tree_categories = Tools::list2tree($query_categories, 'id_category', 'id_parent', 'childrens');

        // 语言列表
        $languages = $legacyContextService->getLanguages();
        // 产品信息
        $product = $this->getProductData($productId, $id_lang, $attributes);
        $group_counts = [];
        foreach ($product['has_attributes'] as $id_attribute_group => $has_attributes) {
            $product['has_attributes'][$id_attribute_group] = array_values($has_attributes);
            $group_counts[$id_attribute_group] = count($has_attributes);
        }

        return $this->render('@PrestaShop/Admin/Sell/Catalog/ProductManage/edit.html.twig', [
            'product' => $product,
            'categories' => $categories,
            'tree_categories' => $tree_categories,
            'features' => $features,
            'feature_values' => $feature_values,
            'attribute_groups' => $attribute_groups,
            'attributes' => $attributes,
            'group_counts' => $group_counts,
            'languages' => $languages,
            'default_language_iso' => $languages[0]['iso_code'],
            'form' => $_GET['form'] ?? 'description',
            'group_type' => [
                'select' => 'Drop-down list',
                'radio' => 'Radio buttons',
                'color' => 'Color or texture',
                'size' => 'Size or Custom Size',
                'other_color' => 'Other Color or texture',
            ],
            'cancel_url' => $request->getSession()->get('product_manage_url', $this->generateUrl('admin_product_manages_index')),
        ]);
    }

    public function getProductData($productId, $id_lang, $attributes)
    {
        $legacyContextService = $this->get('prestashop.adapter.legacy.context');
        // 查询产品基本信息
        $product_info = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('product', 'p')
                ->leftJoin('material', 'm', 'p.id_material = m.id_material')
                ->leftJoin('material_lang', 'ml', 'p.id_material = ml.id_material AND ml.id_lang = ' . $id_lang)
                ->where('p.id_product = ' . $productId)
                ->select('p.id_product, p.supplier_reference, p.current_price, p.weight, p.product_type, p.is_trailing, p.type, p.video_url, p.is_ship_in_48hrs,
                p.id_category_default, p.price, p.active, p.visibility, p.attribute_set, p.date_add, ml.name as material_name')
        );

        // 初始化产品数据
        $product = reset($product_info);
        $product['langs'] = [];
        $product['category_ids'] = [];
        $product['features'] = [];
        $product['has_attributes'] = [];
        $product['images'] = [];
        $product['colors'] = [];

        // 查询语言信息
        $lang_info = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('product_lang', 'pl')
                ->where('pl.id_product = ' . $productId)
                ->select('pl.id_lang, pl.name, pl.description, pl.description_short, pl.model_description, pl.meta_title, pl.meta_description, pl.link_rewrite')
        );

        foreach ($lang_info as $row) {
            $product['langs'][$row['id_lang']] = [
                'name' => $row['name'],
                'description' => $row['description'],
                'description_short' => $row['description_short'],
                'model_description' => $row['model_description'],
                'meta_title' => $row['meta_title'],
                'meta_description' => $row['meta_description'],
                'link_rewrite' => $row['link_rewrite']
            ];
        }

        // 查询分类信息
        $category_info = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('category_product', 'cp')
                ->where('cp.id_product = ' . $productId)
                ->select('cp.id_category')
        );

        foreach ($category_info as $row) {
            $product['category_ids'][$row['id_category']] = $row['id_category'];
        }

        // 查询特性信息
        $feature_info = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('feature_product', 'fp')
                ->where('fp.id_product = ' . $productId)
                ->select('fp.id_feature, fp.id_feature_value')
        );

        foreach ($feature_info as $row) {
            $product['features'][$row['id_feature_value']] = [
                'id_feature' => $row['id_feature'],
                'id_feature_value' => $row['id_feature_value']
            ];
        }

        // 查询图片信息
        $image_info = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('image', 'i')
                ->where('i.id_product = ' . $productId)
                ->orderBy('i.`position` ASC')
                ->select('i.id_image, i.cover, i.name as image_name, i.position as image_position, i.id_color_attribute, i.back_cover')
        );

        foreach ($image_info as $row) {
            $product['images'][$row['id_image']] = [
                'id_image' => $row['id_image'],
                'cover' => $row['cover'],
                'back_cover' => $row['back_cover'],
                'name' => $row['image_name'],
                'image_url' => $legacyContextService->getContext()->link->getImageLink($productId, $row['id_image'], 'small_default', 'webp'),
                'position' => $row['image_position'],
                'id_color_attribute' => $row['id_color_attribute']
            ];
        }

        // 查询属性信息
        $attribute_info = Db::getInstance()->executeS(
            (new \DbQueryCore())->from('product_has_attribute', 'pha')
                ->leftJoin('attribute_group', 'ag', 'pha.id_attribute_group = ag.id_attribute_group')
                ->where('pha.id_product = ' . $productId)
                ->orderBy('ag.`position` ASC, pha.`sort` ASC')
                ->select('pha.id_product_has_attribute, pha.id_attribute_group, pha.id_attribute, pha.price as attribute_price, pha.sort as attribute_sort, pha.id_attribute_color')
        );

        foreach ($attribute_info as $row) {
            $product['has_attributes'][$row['id_attribute_group']][$row['id_product_has_attribute']] = [
                'id_product_has_attribute' => $row['id_product_has_attribute'],
                'id_attribute' => $row['id_attribute'],
                'price' => $row['attribute_price'],
                'sort' => $row['attribute_sort'],
                'id_attribute_color' => $row['id_attribute_color']
            ];

            // 颜色
            if (isset($attributes[2][$row['id_attribute']])) {
                $product['colors'][$row['id_attribute']] = $row['id_attribute'];
            }
        }

        // 整理数据
        $product['features'] = array_values($product['features']);

        return $product;
    }

    // 保存
    public function SaveAction(int $productId, Request $request)
    {
        $data = $request->request->all();
        // 产品信息
        $product = $data['product'];
        // form
        $form = $product['form'];
        try {
            Db::getInstance()->execute('BEGIN');
            switch ($form) {
                case 'description':
                    $this->saveDescription($productId, $product);
                    break;
                case 'details':
                    $this->saveDetails($productId, $product);
                    break;
                case 'combinations':
                    $this->saveCombinations($productId, $product);
                    break;
                case 'features':
                    $this->saveFeatures($productId, $product);
                    break;
                case 'images':
                    $this->saveImages($productId, $product);
                    break;
            }

            if (ProductCore::updateRedisEsData($productId)) {
                Db::getInstance()->execute('COMMIT');
                $this->addFlash('success', $this->trans('successfully updated', 'Admin.Notifications.Success'));
            } else {
                Db::getInstance()->execute('ROLLBACK');
                $this->addFlash('error', $this->trans('ES data synchronization failed, please contact technical personnel.', 'Admin.Notifications.Error'));
            }

        } catch (Exception $e) {
            Db::getInstance()->execute('ROLLBACK');
            $this->addFlash('error', $e->getMessage());
        }
        // 保存并返回
        if (Tools::getIsset('SaveThenBack')) {
            return $this->redirect($request->getSession()->get('product_manage_url', $this->generateUrl('admin_product_manages_index')));
        }
        return $this->redirect($this->generateUrl('admin_products_edit', ['productId' => $productId, 'form' => $form]));
    }

    /**
     * 保存描述
     * @param mixed $productId
     * @param mixed $data
     * @throws Exception
     * @return
     */
    protected function saveDescription($productId, $data)
    {
        // 验证
        if (!$data['sku']) {
            throw new Exception($this->trans('SKU is required', 'Admin.Notifications.Error'));
        }
        if (Db::getInstance()->getValue('SELECT id_product FROM ' . _DB_PREFIX_ . 'product WHERE supplier_reference = "' . pSQL($data['sku']) . '" AND id_product != ' . (int) $productId)) {
            throw new Exception('SKU is exists');
        }
        if (!$data['name'][1]) {
            throw new Exception($this->trans('Name is required', 'Admin.Notifications.Error'));
        }
        $product = new Product($productId);
        $product->supplier_reference = pSQL(strtoupper($data['sku']));
        $product->name = $data['name'];
        $product->link_rewrite = $data['link_rewrite'][1] ? $data['link_rewrite'] : [1 => Tools::link_rewrite($data['name'][1])];
        $product->description = $data['description'];
        $product->description_short = $data['description_short'];
        $product->model_description = $data['model_description'];
        $product->meta_title = $data['meta_title'];
        $product->meta_description = $data['meta_description'];
        if (!$product->create_date || $product->create_date == '0000-00-00 00:00:00') {
            $product->create_date = date('Y-m-d H:i:s');
        }
        $product->save();

        return true;
    }

    /**
     * 保存详细信息
     * @param mixed $productId
     * @param mixed $data
     * @return
     */
    protected function saveDetails($productId, $data)
    {
        // 验证
        if ($data['price'] < 0 || $data['current_price'] < 0) {
            throw new Exception($this->trans('Price cannot be negative', 'Admin.Notifications.Error'));
        }
        if ($data['price'] < $data['current_price']) {
            throw new Exception($this->trans('Price cannot be lower than current price', 'Admin.Notifications.Error'));
        }
        // 折扣价格
        if ($data['price'] != $data['current_price']) {
            $price_difference = $data['price'] - $data['current_price'];
            // 是否已存在折扣
            if (Db::getInstance()->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'specific_price` WHERE `id_product` = ' . $productId)) {
                // 更新折扣
                Db::getInstance()->update('specific_price', ['reduction' => $price_difference], 'id_product = ' . $productId);
            } else {
                // 新增折扣
                ImportProduct::insertSpecificPrices([
                    'id_product' => $productId,
                    'reduction' => $price_difference,
                    'id_shop' => 1
                ]);
            }
        } else {
            //价格一样表示折扣信息需要为空
            // 是否已存在折扣
            if (Db::getInstance()->getValue('SELECT COUNT(*) FROM `' . _DB_PREFIX_ . 'specific_price` WHERE `id_product` = ' . $productId)) {
                // 更新折扣价格为0
                Db::getInstance()->update('specific_price', ['reduction' => 0], 'id_product = ' . $productId);
            }
        }

        $product = new Product($productId);
        // 分类
        if ($data['category_ids']) {
            $product->updateCategories($data['category_ids']);
        }
        $product->price = $data['price'];
        $product->current_price = $data['current_price'];
        $product->id_category_default = $data['id_category_default'];
        $product->visibility = $data['visibility'];
        $product->weight = $data['weight'];
        $product->date_add = $data['date_add'];
        $product->type = $data['type'];
        $product->is_trailing = $data['is_trailing'];
        $product->active = $data['active'];
        $product->attribute_set = $data['attribute_set'];
        if (!$product->create_date || $product->create_date == '0000-00-00 00:00:00') {
            $product->create_date = date('Y-m-d H:i:s');
        }
        $product->save();

        return true;
    }

    /**
     * 保存属性
     * @param mixed $productId
     * @param mixed $data
     * @return
     */
    protected function saveCombinations($productId, $data)
    {
        try {
            if (!$data['combinations']) {
                throw new Exception('Product Combination is required');
            }
            // 产品存在的属性
            $exist_product_has_attributes = Db::getInstance()->executeS('SELECT * FROM `' . _DB_PREFIX_ . 'product_has_attribute` WHERE `id_product` = ' . (int) $productId);
            // 提取存在的ID
            $exist_product_has_attributes_id = array_column($exist_product_has_attributes, 'id_product_has_attribute');
            // 整理判断是否需要修改
            $exist_attributes = [];
            foreach ($exist_product_has_attributes as $exist_product_has_attribute) {
                $exist_attributes[$exist_product_has_attribute['id_product_has_attribute']] = [
                    'id_attribute' => $exist_product_has_attribute['id_attribute'],
                    'price' => $exist_product_has_attribute['price'],
                    'sort' => $exist_product_has_attribute['sort'],
                    'id_attribute_color' => $exist_product_has_attribute['id_attribute_color'] ?? 0
                ];
            }
            // 还存在的属性
            $id_product_has_attributes = [];
            // 新增的属性
            $add_attributes = [];
            // 修改的属性
            $update_attributes = [];
            // 保存属性验证是否已有改属性
            $id_attributes = [];
            // 新增属性
            foreach ($data['combinations'] as $id_attribute_group => $has_attributes) {
                foreach ($has_attributes as $has_attribute) {
                    $has_attribute['id_attribute_color'] = $has_attribute['id_attribute_color'] ?? 0;
                    // 验证
                    if (!$has_attribute['id_attribute']) {
                        throw new Exception('Attribute is required');
                    }
                    if (in_array($has_attribute['id_attribute'], $id_attributes)) {
                        throw new Exception('Attribute is duplicate');
                    }
                    // 存储用于验证
                    $id_attributes[] = $has_attribute['id_attribute'];
                    if ($has_attribute['id_product_has_attribute']) {
                        $id_product_has_attributes[] = $has_attribute['id_product_has_attribute'];
                        // 判断是否需要修改
                        $exist_attribute = $exist_attributes[$has_attribute['id_product_has_attribute']];
                        if (
                            $has_attribute['id_attribute'] != $exist_attribute['id_attribute']
                            || $has_attribute['price'] != $exist_attribute['price']
                            || $has_attribute['sort'] != $exist_attribute['sort']
                            || $has_attribute['id_attribute_color'] != $exist_attribute['id_attribute_color']
                        ) {
                            $update_attributes[$has_attribute['id_product_has_attribute']] = [
                                'id_attribute' => $has_attribute['id_attribute'],
                                'price' => $has_attribute['price'],
                                'sort' => $has_attribute['sort'],
                                'id_attribute_color' => $has_attribute['id_attribute_color']
                            ];
                        }
                    } else {
                        $add_attributes[] = [
                            'id_product' => $productId,
                            'id_attribute_group' => $id_attribute_group,
                            'id_attribute' => $has_attribute['id_attribute'],
                            'price' => (float) $has_attribute['price'],
                            'sort' => (int) $has_attribute['sort'],
                            'id_attribute_color' => $has_attribute['id_attribute_color']
                        ];
                    }
                }
            }
            // 筛选要删除的属性
            $delete_product_has_attribute_ids = array_diff($exist_product_has_attributes_id, $id_product_has_attributes);
            if ($delete_product_has_attribute_ids) {
                // 删除不存在的属性
                Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'product_has_attribute` WHERE `id_product_has_attribute` IN (' . implode(',', $delete_product_has_attribute_ids) . ')');
            }
            // 新增的属性
            if ($add_attributes) {
                Db::getInstance()->insert('product_has_attribute', $add_attributes);
            }
            // 修改属性
            if ($update_attributes) {
                foreach ($update_attributes as $id_product_has_attribute => $attribute) {
                    Db::getInstance()->update('product_has_attribute', $attribute, 'id_product_has_attribute = ' . $id_product_has_attribute);
                }
            }

            $product = new Product($productId);
            // 更新产品状态
            $product->is_update_attribute = 1;
            if (!$product->create_date || $product->create_date == '0000-00-00 00:00:00') {
                $product->create_date = date('Y-m-d H:i:s');
            }
            $product->save();
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }

        return true;
    }

    /**
     * 保存特性信息
     * @param mixed $productId
     * @param mixed $data
     * @throws Exception
     * @return
     */
    protected function saveFeatures($productId, $data)
    {
        try {
            // 验证
            if (!$data['features']) {
                throw new Exception('Product Feature is required');
            }
            // 验证是否有相同的特性值
            $id_feature_values = array_column($data['features'], 'id_feature_value');
            if (count(array_unique($id_feature_values)) != count($id_feature_values)) {
                throw new Exception('Duplicate Product Feature');
            }
            $product = new Product($productId);
            if (!$product->create_date || $product->create_date == '0000-00-00 00:00:00') {
                $product->create_date = date('Y-m-d H:i:s');
            }

            // 筛选出fabric
            if (
                $id_material = Db::getInstance()->getValue(
                    'SELECT ml.id_material FROM `' . _DB_PREFIX_ . 'material_lang` ml
                    LEFT JOIN `' . _DB_PREFIX_ . 'feature_value_lang` fvl ON ml.name = fvl.value
                    LEFT JOIN `' . _DB_PREFIX_ . 'feature_value` fv ON fvl.id_feature_value = fv.id_feature_value
                    LEFT JOIN `' . _DB_PREFIX_ . 'feature` f ON fv.id_feature = f.id_feature
                    WHERE f.code LIKE \'%fabric\'
                    AND fv.id_feature_value IN (' . implode(',', $id_feature_values) . ')'
                )
            ) {
                $product->id_material = $id_material;
                $product->is_update_attribute = 1;
            }
            $product->save();

            // 删除所有特性
            Db::getInstance()->execute('DELETE FROM `' . _DB_PREFIX_ . 'feature_product` WHERE `id_product` = ' . (int) $productId);
            // 添加特性
            $feature_products = [];
            foreach ($data['features'] as $feature) {
                $feature_products[] = [
                    'id_product' => $productId,
                    'id_feature' => $feature['id_feature'],
                    'id_feature_value' => $feature['id_feature_value']
                ];
            }
            Db::getInstance()->insert('feature_product', $feature_products);
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }

        return true;
    }

    /**
     * 保存图片信息
     * @param mixed $productId
     * @param mixed $data
     * @throws Exception
     * @return bool
     */
    protected function saveImages($productId, $data)
    {
        //原产品信息
        $origin_product = Db::getInstance()->getRow('select video_url,create_date from `' . _DB_PREFIX_ . 'product` where id_product = ' . $productId);

        //新产品信息
        $new_video_url = $data['video_url'] ?? '';

        //新图片信息
        $new_images = $data['images'] ?? [];

        // 判断是否有重复的颜色对应图片, 先去除为0或为空的值
        $id_color_attributes = array_filter(array_column($new_images, 'id_color_attribute'), function ($value) {
            return $value;
        });
        // 判断是否有重复的颜色对应图片
        if (count(array_unique($id_color_attributes)) != count($id_color_attributes)) {
            throw new Exception('Duplicate Image Color');
        }

        //需要更新的图片信息
        if ($new_images) {
            foreach ($new_images as $id_image => $new_image) {
                $new_images['cover'] = isset($new_image['cover']) ? 1 : 0;
                $new_image['back_cover'] = isset($new_image['back_cover']) ? 1 : 0;
                //更新图片信息
                $sql = 'update `' . _DB_PREFIX_ . 'image`
                        set `id_color_attribute` = ' . $new_image['id_color_attribute'] . ',
                            `position` = ' . $new_image['position'] . ',
                            `cover` = ' . $new_images['cover'] . ',
                            `back_cover` = ' . $new_image['back_cover'] . '
                        where id_image = ' . $id_image;
                $result = Db::getInstance()->execute($sql);
                if (!$result) {
                    throw new Exception('更新图片' . $id_image . '信息失败');
                }
                if (!Db::getInstance()->execute('update `' . _DB_PREFIX_ . 'image_shop` set `cover` = ' . $new_images['cover'] . ' where id_image = ' . $id_image)) {
                    throw new Exception('更新图片' . $id_image . '信息失败');
                }
                //先删除图片对应的变体
                Db::getInstance()->execute('delete from `' . _DB_PREFIX_ . 'product_attribute_image` where id_image = ' . $id_image);
                if ($new_image['id_color_attribute']) {
                    //找到新颜色的变体
                    $product_attributes = Db::getInstance()->executeS(
                        'SELECT pac.id_product_attribute FROM ' . _DB_PREFIX_ . 'product_attribute_combination pac
                        LEFT JOIN ' . _DB_PREFIX_ . 'product_attribute pa ON pac.id_product_attribute = pa.id_product_attribute
                        WHERE pa.id_product = ' . (int) $productId . '
                        AND pac.id_attribute = ' . (int) $new_image['id_color_attribute']
                    );
                    if ($product_attributes) {
                        //添加新的对应关系
                        $insert_sql = 'INSERT INTO `' . _DB_PREFIX_ . 'product_attribute_image` (`id_product_attribute`, `id_image`) VALUES ';
                        foreach ($product_attributes as $product_attribute) {
                            $id_product_attribute = $product_attribute['id_product_attribute'];
                            $insert_sql .= '(' . (int) $id_product_attribute . ', ' . (int) $id_image . '), ';
                        }
                        $insert_sql = rtrim($insert_sql, ', ');
                        Db::getInstance()->execute($insert_sql);
                    }
                }
            }
        }

        if ($origin_product['video_url'] != $new_video_url) {
            $sql = "update `" . _DB_PREFIX_ . "product` set video_url='{$new_video_url}' where id_product =" . $productId;
            $result = Db::getInstance()->execute($sql);
            if (!$result) {
                throw new Exception('更新video url失败');
            }
        }

        Db::getInstance()->execute('UPDATE `' . _DB_PREFIX_ . 'product` SET is_update_attribute = 1 WHERE id_product = ' . $productId);

        return true;
    }

    /**
     * 上传图片
     * @param \Symfony\Component\HttpFoundation\Request $request
     * @throws Exception
     * @return \Symfony\Component\HttpFoundation\JsonResponse
     */
    // public function imageUploadAction(Request $request)
    // {
    //     $files = $request->files->get('images');
    //     $id_product = $request->request->get('id_product');
    //     try {
    //         foreach ($files as $file) {
    //             // 验证文件格式
    //             if (!in_array($extension = $file->getClientOriginalExtension(), ['jpg'])) {
    //                 throw new Exception('Image format is not supported');
    //             }
    //             $file_name = $file->getClientOriginalName();
    //             $file_path = $file->getPathname();
    //             $firstTwoChars = substr($file_name, 0, 2);
    //             $firstTwoDir = implode('/', str_split((string) $firstTwoChars)) . '/';
    //             // 上传图片到S3
    //             $aws = new ImageAwsApiCore('media/catalog/product/');
    //             $name = $firstTwoDir . $file_name;
    //             if ($aws->upload($file_path, $name)) {
    //                 $image = new Image();
    //                 $image->id_product = $id_product;
    //                 $image->type = 'webp';
    //                 $image->name = pSQL('/' . str_replace('.' . $extension, '', $name));
    //                 $image->save();
    //             }
    //         }

    //         // 删除缓存
    //         Tools::clearProductSearchRedis([$id_product]);
    //     } catch (Exception $e) {
    //         return $this->json([
    //             'status' => 'error',
    //             'message' => $e->getMessage()
    //         ]);
    //     }

    //     return $this->json([
    //         'status' => 'success',
    //         'message' => 'Image uploaded successfully'
    //     ]);
    // }

    public function imageUploadAction(Request $request)
    {
        $data = json_decode($request->getContent(), true);
        $images = $data['images'] ?? [];
        $id_product = $data['id_product'] ?? null;

        try {
            foreach ($images as $img) {
                $base64Image = $img['image'];
                $fileName = $img['file_name'];

                $FileData = Tools::decodeBase64Image($base64Image, $fileName, 'jpg');
                $tmpFilePath = $FileData['tmpFilePath'];
                $contentType = $FileData['contentType'];
                $extension = $FileData['extension'];

                $firstTwoChars = substr($fileName, 0, 2);
                $firstTwoDir = implode('/', str_split($firstTwoChars)) . '/';
                $save_name = $firstTwoDir . $fileName;
                // 上传到 S3
                $aws = new ImageAwsApiCore('media/catalog/product/');
                if ($aws->upload($tmpFilePath, $save_name)) {
                    $image = new Image();
                    $image->id_product = $id_product;
                    $image->type = 'webp';
                    $image->name = pSQL('/' . str_replace('.' . $extension, '', $save_name));
                    $image->save();
                }

                // 删除临时文件
                @unlink($tmpFilePath);
            }

            Tools::clearProductSearchRedis([$id_product]);

            return $this->json([
                'status' => 'success',
                'message' => 'Image uploaded successfully'
            ]);
        } catch (Exception $e) {
            return $this->json([
                'status' => 'error',
                'message' => $e->getMessage()
            ]);
        }
    }


    /**
     * 删除图片
     * @param \Symfony\Component\HttpFoundation\Request $request
     * @throws Exception
     * @return \Symfony\Component\HttpFoundation\JsonResponse
     */
    public function imageDeleteAction(Request $request)
    {
        $request = $request->request->all();
        try {
            if (empty($request['id_image'])) {
                throw new Exception('Image ID is required');
            }
            $image = new Image($request['id_image']);
            $id_product = $image->id_product;
            $cover = $image->cover;
            // 删除图片
            $image->delete();

            // 如果是封面图片，则将更新封面图片
            if ($cover) {
                // 更新最小排序的图片为主图
                $new_cover_image_id = Db::getInstance()->getValue(
                    'SELECT id_image FROM ' . _DB_PREFIX_ . 'image
                    WHERE id_product = ' . (int) $request['id_product'] . '
                    AND id_image != ' . (int) $request['id_image'] . '
                    ORDER BY position ASC'
                );
                if ($new_cover_image_id) {
                    $new_cover_image = new Image($new_cover_image_id);
                    $new_cover_image->cover = 1;
                    $new_cover_image->save();
                }
            }

            // 删除缓存
            Tools::clearProductSearchRedis([$id_product]);
            return $this->json([
                'flag' => true,
            ]);
        } catch (Exception $e) {
            return $this->json([
                'flag' => false,
                'message' => $e->getMessage(),
            ]);
        }
    }

    /**
     * 更新图片
     * @param \Symfony\Component\HttpFoundation\Request $request
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    public function imageUpdateAction(Request $request)
    {
        try {
            $id_image = $request->request->get('id_image');
            $file = $request->files->get('image');
            $file_path = $file->getPathname();

            // 获取图片对象
            $image = new Image($id_image);
            // 获取图片名称
            $name = $image->name;

            // 更新图片
            $aws = new ImageAwsApiCore('media/catalog/product/');
            $aws->upload($file_path, ltrim($name, '/') . '.jpg');

            $this->addFlash('success', $this->trans('successfully updated', 'Admin.Notifications.Success'));
        } catch (Exception $e) {
            $this->addFlash('error', $e->getMessage());
        }

        return $this->redirect($this->generateUrl('admin_products_edit', ['productId' => $image->id_product, 'form' => 'images']));
    }
}