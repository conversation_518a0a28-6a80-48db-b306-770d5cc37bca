<div id="productCommentsBlock">
    <div class="tabs" style="margin-top: 0px;padding:.25rem 0px">
        <div id="new_comment_form_ok" class="alert" style="display:flex;padding:15px 10px;margin-bottom:0px;">{* *}
            <div class="average-left">
                <div class="ratings">
                    <div class="rating-box" style="width: 90px;">
                        <div class="rating2" style="margin-right:10px">
                        <div class="rating1" style="width: {$percent};"></div>
                        </div>
                    <span class="rating-number">{$productAverageTotal}</span>
                    </div>
                </div>
                <div style="clear:both"></div>
                <span class="amount">{l s='basé sur %s avis' sprintf=[$nbComments] mod='boncomments'}</span>
            </div>
            <div class="review-filter">
                <div class="filter_newest btn-review active" title="newest">
                    <p>{l s="Avis les plus récents..." mod='boncomments'}</p>
                </div>
                <div class="filter_photo btn-review" title="photo">
                    <p>{l s="Photos" mod='boncomments'}</p>
                </div>
                <div class="filter_Rating btn-review" title="rating">
                    <p>{l s="Notation" mod='boncomments'}</p>
                </div>
            </div>
        </div>
        {* 评论 *}
        <div id="product_comments_block_tab">
            {if !$comments}
                <span class="no-reviews">{l s='Ce produit n\'a pas de commentaires!' mod='boncomments'}</span>
            {else}
                {foreach from=$comments key=key item=item}
                    <div class="{$key}" style="display:none">
                        {foreach from=$item.comments item=comment}
                            {if $comment.content}
                                <div class="comment clearfix jscommentiterm" style="margin: 0 0 10px 0;"
                                    data-name="{$comment.customer_name}" data-url="{$comment.product_url}"
                                    data-text="{$comment.content|escape:'html':'UTF-8'|nl2br nofilter}"
                                    data-img="{$comment.product_image}">
                                    <div class="comment_author">
                                        <div class="commentItem"
                                            style="display: flex;width: 100%;border-bottom: 1px solid #ccc;padding-bottom:10px;margin-bottom: 10px;position: relative;">
                                            {* 星级 评论人 属国*}
                                            <div class="customer_photo">{* 头像 *}
                                            </div>
                                            <span class="icon-savedui">
                                                <svg t="1693548825988" class="icon" viewBox="0 0 1024 1024" version="1.1"
                                                    xmlns="http://www.w3.org/2000/svg" p-id="4829" width="16" height="16">
                                                    <path
                                                        d="M882.432387 204.696792c18.921552-21.157736 51.260205-22.705863 72.245926-3.440283 20.813707 19.26558 22.361834 52.120275 3.440283 73.278011L435.711742 856.458256a50.656435 50.656435 0 0 1-74.48211 1.204099l-295.348228-307.905258c-19.609609-20.469679-19.26558-53.324374 1.032085-73.278011 20.297665-19.953637 52.636318-19.437594 72.245926 1.032085l257.505124 268.514026L882.432387 204.696792z m0 0"
                                                        p-id="4830"></path>
                                                </svg>
                                            </span>
                                            <div style="display: flex;flex-direction:column;margin-left: 1rem;padding-top: 5px;">
                                                <strong>{$comment.customer_name|escape:'html':'UTF-8'}</strong>
                                                <label style="text-align: left;">
                                                    {l s="Vérifié" mod='boncomments'}
                                                    {* 国家 *}
                                                    {if $comment.country_iso}
                                                        <svg t="1693538422217" class="icon icon-city" viewBox="0 0 1024 1024" version="1.1"
                                                            xmlns="http://www.w3.org/2000/svg" p-id="4094" width="16" height="16">
                                                            <path
                                                                d="M115.264 422.656C112.576 241.984 223.488 80.64 388.352 25.216c185.024-62.336 367.616-5.568 482.24 149.504 104.128 141.056 105.728 348.032-0.896 489.408-78.528 103.872-161.28 204.608-244.352 304.64-62.72 75.264-130.688 72.64-192.64-4.352-76.096-94.656-153.152-188.48-226.816-284.992C148.672 604.352 112.64 519.808 115.264 422.656zM168.448 422.592C161.28 492.864 189.184 563.776 233.92 624.384c79.552 108.224 165.568 211.904 250.112 316.48 33.472 41.6 69.568 32.512 99.84-4.608 81.728-101.248 166.976-200 241.984-305.728 99.52-140.608 92.48-307.776-7.872-437.12-94.848-122.048-257.152-170.624-403.392-120.64C266.432 123.456 168.448 259.072 168.448 422.592z"
                                                                fill="#272636" p-id="4095"></path>
                                                            <path
                                                                d="M351.552 416.96c0-101.824 73.792-178.688 172.032-179.2 108.032-0.512 190.144 75.648 190.784 176.96 0.64 102.912-83.136 186.688-184.96 185.344C427.584 598.4 351.744 520.384 351.552 416.96zM532.352 290.688C461.696 289.856 407.296 342.72 405.376 413.888c-1.92 73.344 53.696 131.328 126.592 131.84 69.504 0.384 126.144-54.912 127.424-124.672C660.864 346.624 607.104 291.52 532.352 290.688z"
                                                                fill="#272636" p-id="4096"></path>
                                                        </svg>
                                                        {$comment.country_iso}
                                                    {/if}
                                                </label>
                                            </div>
                                        </div>
                                        {* 属性 *}
                                        {if $comment.attributes.color || $comment.attributes.size}
                                            <div class="colerSize">
                                                {if $comment.attributes.color}
                                                    <label style="margin-right: 20px;text-transform: capitalize;">Couleur:
                                                        {$comment.attributes.color}</label>
                                                {/if}
                                                {if $comment.attributes.size}
                                                    <label style="margin-right: 20px;text-transform: capitalize;">Taille:
                                                        {$comment.attributes.size}</label>
                                                {/if}
                                            </div>
                                        {/if}
                                        <div style="display: flex;margin-top: 10px;">
                                            {* 星级 *}
                                            <div class="star_content clearfix" style="margin-top:2px ;margin-bottom: 10px;">
                                                {section name="i" start=0 loop=5 step=1}
                                                    {if $comment.grade le $smarty.section.i.index}
                                                        <div class="star"></div>
                                                    {else}
                                                        <div class="star star_on"></div>
                                                    {/if}
                                                {/section}
                                            </div>
                                            {* 是否合适 *}
                                            {if $comment.is_fit !== ''}
                                                <div class="fit" style="height: 20px; line-height: 10px;margin-top: 6px;margin-left: 10px;">
                                                    <span class="text">
                                                        <span style="margin-right: 3px;">{l s="Ajuster" mod='boncomments'}:</span>
                                                        {if $comment.is_fit}
                                                            {l s="Oui" mod='boncomments'}
                                                        {else}
                                                            {l s="Non" mod='boncomments'}
                                                        {/if}
                                                    </span>
                                                </div>
                                            {/if}
                                        </div>

                                    </div>
                                    <div class="comment_biaoti">
                                        <label style="font-weight: 600;">{$comment.title}</label>
                                    </div>
                                    <div id="comment_details-{$comment.id_product_comment}" class="comment_details" style="width: 100%;">{* background: tan;评论内容 *}
                                        {* <h4 class="title_block"></h4> *}
                                        <p>{$comment.content|escape:'html':'UTF-8'|nl2br nofilter}</p>
                                        {* 翻译 *}
                                        {if _PS_COUNTRY_FANYI_}
                                            <div class="translate">
                                                <a href="javascript:void(0);" onclick="reviewln({$comment.id_product_comment});"
                                                    style="color: #FF7176;">
                                                    <svg t="1683602829356" class="translate-icon" viewBox="0 0 1024 1024" version="1.1"
                                                        xmlns="http://www.w3.org/2000/svg" p-id="3967" width="12" height="12">
                                                        <path
                                                            d="M99.119848 428.002088h828.014506a43.350032 43.350032 0 0 0 43.350032-43.350031 43.248882 43.248882 0 0 0-16.451337-33.957525 43.465632 43.465632 0 0 0-9.219107-8.756707L635.50424 124.869766a43.350032 43.350032 0 1 0-49.809186 70.964002l207.270952 145.461032H99.119848a43.350032 43.350032 0 0 0 0 86.707288zM927.134354 601.07709H99.119848a43.350032 43.350032 0 0 0-43.350032 43.350032 43.205532 43.205532 0 0 0 16.451337 33.9503c2.564877 3.272927 5.635504 6.242405 9.219107 8.756706l309.309701 217.075284a43.313907 43.313907 0 0 0 60.386594-10.584632 43.350032 43.350032 0 0 0-10.577407-60.386595L233.288196 687.777154h693.846158a43.350032 43.350032 0 0 0 0-86.700064z"
                                                            fill="#FF7176" p-id="3968">
                                                        </path>
                                                    </svg>
                                                    {l s='Traduire' mod='boncomments'}
                                                </a>
                                            </div>
                                            <div class="translate-box">
                                                <div class="translate-close" onclick="closereviewln({$comment.id_product_comment});"></div>
                                                <div class="translate-content reviewdata-{$comment.id_product_comment}"></div>
                                                <div class="translate-footer">
                                                    <span class="comment-target-language-name">{l s='néerlandais' mod='boncomments'}</span>
                                                    <div class="translate-by">
                                                        <span class="translate-by-google">{l s='Traduit par' mod='boncomments'}</span>
                                                        <img class="translate-google-img"
                                                            src="/themes/ztheme/assets/images/translate_google.svg">
                                                    </div>
                                                </div>
                                            </div>
                                        {/if}
                                        {* 评论图片 *}
                                        {if $comment.images}
                                            <div class="comment_img" style="margin-bottom: 12px;">
                                                {foreach from=$comment.images item=image}
                                                    <img class="comment-modal-img" data-bgsrc="{$image.image_path}"
                                                        src="{$image.image_thumb_path}" alt=""
                                                        style="width: 72px;height: 96px;margin-right: 6px;">
                                                {/foreach}
                                            </div>
                                        {/if}
                                        <div class="comment_author_infos">
                                            <span class="text"
                                                style="font-weight: bold;">{l s="Date d'achat:" mod='boncomments'}</span>
                                            <em>{$comment.date_add|date_format}</em>
                                        </div>
                                        <ul>{* 底部图标 *}
                                            <div class="review_sharelinks">
                                                <div class="review_useful">
                                                    <a class="icon_useful">
                                                        <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                                            xmlns="http://www.w3.org/2000/svg" width="15px" height="15px">
                                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                                d="M7.94.94A1.5 1.5 0 0 1 10.5 2a20.774 20.774 0 0 1-.384 4H14.5A1.5 1.5 0 0 1 16 7.5v.066l-1.845 6.9-.094.095A1.5 1.5 0 0 1 13 15H9c-.32 0-.685-.078-1.038-.174-.357-.097-.743-.226-1.112-.349l-.008-.003c-.378-.126-.74-.246-1.067-.335C5.44 14.047 5.18 14 5 14v.941l-5 .625V6h5v.788c.913-.4 1.524-1.357 1.926-2.418A10.169 10.169 0 0 0 7.5 1.973 1.5 1.5 0 0 1 7.94.939ZM8 2l.498.045v.006l-.002.013a4.507 4.507 0 0 1-.026.217 11.166 11.166 0 0 1-.609 2.443C7.396 5.951 6.541 7.404 5 7.851V13c.32 0 .685.078 1.038.174.357.097.743.226 1.112.349l.008.003c.378.126.74.246 1.067.335.335.092.594.139.775.139h4a.5.5 0 0 0 .265-.076l1.732-6.479A.5.5 0 0 0 14.5 7H8.874l.138-.61c.326-1.44.49-2.913.488-4.39a.5.5 0 0 0-1 0v.023l-.002.022L8 2ZM4 7H1v7.434l3-.375V7Zm-1.5 5.75a.25.25 0 1 0 0-.5.25.25 0 0 0 0 .5Zm-.75-.25a.75.75 0 1 1 1.5 0 .75.75 0 0 1-1.5 0Z">
                                                            </path>
                                                        </svg>
                                                        <span>Utile</span>
                                                    </a>
                                                </div>
                                                <div class="review_share">
                                                    <a href="https://www.facebook.com/sharer/sharer.php?u={$product.url}">
                                                        <svg viewBox="0 0 16 16" fill="currentColor" class="icon_icon__ECGRl"
                                                            xmlns="http://www.w3.org/2000/svg" width="14px" height="14px">
                                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                                d="M13 1a2 2 0 1 0 0 4 2 2 0 0 0 0-4Zm-3 2a3 3 0 1 1 .583 1.778L5.867 7.115a3 3 0 0 1 0 1.77l4.716 2.337a3 3 0 1 1-.45.893L5.417 9.778a3 3 0 1 1 0-3.556l4.716-2.337A3.002 3.002 0 0 1 10 3ZM1 8a2 2 0 1 1 4 0 2 2 0 0 1-4 0Zm10 5a2 2 0 1 1 4 0 2 2 0 0 1-4 0Z">
                                                            </path>
                                                        </svg>
                                                        <span>Partager</span>
                                                    </a>
                                                </div>
                                            </div>

                                            <div class="modal fade" id="modal-logged" tabindex="-1" role="dialog"
                                                aria-labelledby="modal-logged" aria-hidden="true">
                                                <div class="modal-dialog" role="document">
                                                    <div class="modal-content">
                                                        <div class="modal-body">
                                                            <span>{l s='Pour écrire un commentaire s\'il vous plaît connectez-vous.'
                                                                mod='boncomments'}</span>
                                                        </div>
                                                        <div class="modal-footer">
                                                            <button type="button" class="popup-close" data-dismiss="modal"></button>
                                                            <button type="button" class="btn btn-primary">
                                                                <a href="{$link->getPageLink('my-account', true)}">
                                                                    {l s='Connexion' mod='boncomments'}
                                                                </a>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </ul>
                                        {hook::exec('displayProductComment', $comment) nofilter}
                                    </div>
                                    <br />
                                </div>
                            {/if}
                        {/foreach}


                        {* {if count($item) > 5}
                <div class="review-list-more on" key={$key} id="jsAllbtn"
                    data-immersive-translate-walked="fb373d8b-416e-4def-a318-9aa1aacc8519"
                    data-immersive-translate-paragraph="1" onclick="allBtnClick(this)">{l s="View All"
                    mod='boncomments'}</div>
                {/if}*}
                    </div>
                {/foreach}
                {if count($comments) > 0}{* 有评论才展示 *}
                    <div class="showNextPage"
                        style="color:#e38e9d;display: block;text-align: center;border: 1px solid #cc7f85;
                width: 150px;margin: 20px auto 0;height: 40px;line-height: 40px;font-size: 13px;font-family: LoraMedium;box-sizing: border-box;cursor: pointer;">
                    </div>
                {/if}
            {/if}
        </div>
    </div>
</div>

<div id="showReviewtanchuang">
    <div id="details_comment_modal" class="modal mymodal-pc-js">
        <div class="modal-content" style="width: 900px;">
            <span id="close" class="dt-close">×</span>
            {*<span class="left-button-review left-button"></span>
            <span class="right-button-review right-button"></span> *}
            <div class="details-comment-img-modal" style="width: 100%;height:100 %">
                <div class="phone">
                    <div class="left">
                        <div class="left-img-top dc-left-img-top">
                            {* 大图 *}
                        </div>
                        <div class="left-img-bottom dc-left-img-bottom">{* 小图 *}</div>
                    </div>
                    <div class="right">
                        <span class="tc-auther" id="writer_name">{* name *}</span>
                        <div class="tc-content" id="write_content" style="max-height: 300px;overflow-y: auto;">
                            {* text *}</div>
                        <div class="tc-pr-img right-img">
                            <img class="product-comment-img" src="{* 商品图 *}" alt="">
                        </div>
                        <div class="tc-share">
                            <a href="" target="_blank" class="fac share-icon facebook-share-icon">Facebook</a>
                            <a href="" title="Pinterest" class="pin share-icon pinterest-share-icon"
                                target="_blank">Pinterest</a>
                            <a href="" class="goo share-icon googleplus-share-icon" title="Google+"
                                target="_blank">Google+</a>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<script type="text/javascript">
    var newestCount = "{$comments.newest_reviews.comment_count }"
    var photoCount = "{$comments.photo_reviews.comment_count }"
    var ratingCount = "{$comments.rating_reviews.comment_count }"
    var id_product = "{$id_product_comment_form}";
    var productcomments_controller_url = '{$productcomments_controller_url nofilter}';
    var confirm_report_message = "{l s='Are you sure that you want to report this comment ? ' mod='boncomments' js=1}";
    var secure_key = '{$secure_key}';
    var productcomments_url_rewrite = '{$productcomments_url_rewriting_activated}';
    var productcomment_added = "{l s='Your comment has been added!' mod='boncomments' js=1}";
    var productcomment_added_moderation = "{l s='Your comment has been submitted and will be available once approved by a moderator.' mod='boncomments' js=1}";
    var productcomment_title = "{l s='New comment ' mod='boncomments' js=1}";
    var productcomment_ok = "{l s='OK ' mod='boncomments' js=1}";
    var moderation_active = '{ $moderation_active }';
    let nowPageSize = 1
    let nowPage = 'newest_reviews'

    function changeNextPageText() {
        console.log('zhixingnewpage');
        let count = 0;
        switch (nowPage) {
            case 'newest_reviews':
                count = Number(newestCount);
                break;
            case 'photo_reviews':
                count = Number(photoCount);
                break;
            default:
                count = Number(ratingCount);
                break;
        }
        const showMore = count > nowPageSize * 5;
        const text = showMore ? "Voir tout" : "Moins";
        $('.showNextPage').text(text);

        if (text === "Voir tout") {
            $('.showNextPage').off('click', less);
            $('.showNextPage').off('click', getNextPage);
            $('.showNextPage').on('click', getNextPage);
        } else {
            $('.showNextPage').off('click', less);
            $('.showNextPage').off('click', getNextPage);
            $('.showNextPage').on('click', less);
        }
    }

    function getNextPage() {
        nowPageSize++
        $.ajax({
            url: productcomments_controller_url + "?action=show_comments&id_product=" + id_product + "&p=" +
                nowPageSize,
            type: "GET",
            dataType: "json",
            success: function(data) {
                const result = data.comments;
                if ($('.newest_reviews').css('display') == 'block') {
                    $('.newest_reviews').append(result.newest_reviews)
                }
                if ($('.photo_reviews').css('display') == 'block') {
                    $('.photo_reviews').append(result.photo_reviews)
                }
                if ($('.rating_reviews').css('display') == 'block') {
                    $('.rating_reviews').append(result.rating_reviews)
                }

                changeNextPageText()
                const commentItem = document.querySelectorAll('.newest_reviews .comment .commentItem')
                commentItem.forEach(item => {
                    $(item).find(".customer_photo").text($(item).find("strong").text().substring(0,
                        1));
                    $(".comment_author_infos .text").css("font-weight", "bold")
                })
            }
        })
        $('.comment_img img').on('click', showBigImage) //给新加载的图片绑定事件

    }

    function less() {
        console.log("123435546srt347568");
        if ($('.newest_reviews').css('display') == 'block') {
            $('.newest_reviews > :gt(4)').remove();
        }
        if ($('.photo_reviews').css('display') == 'block') {
            $('.photo_reviews > :gt(4)').remove();
        }
        if ($('.rating_reviews').css('display') == 'block') {
            $('.rating_reviews > :gt(4)').remove();
        }
        nowPageSize = 1;
        $('.showNextPage').text("Voir tout");
        $('.showNextPage').off('click', less);
        $('.showNextPage').off('click', getNextPage);
        $('.showNextPage').on('click', getNextPage);
    }
    //切换大图
    function qhBigImg(e) {
        let big = e.target.dataset.bgsrc;
        $(".dc-big-img").attr("src", big);
    }
    //展示弹窗
    function showBigImage(e) {
        //console.log(e.target);
        let com_item = $(e.target).closest('.comment.clearfix.jscommentiterm').eq(0);
        $("#writer_name").text(com_item.attr('data-name'));
        $("#write_content").text(com_item.attr('data-text'));
        $(".product-comment-img").attr('src', com_item.attr('data-img'));
        $(".fac").attr('href', "https://www.facebook.com/sharer/sharer.php?u=" + com_item.attr('data-url'));
        $(".pin").attr('href', "http://pinterest.com/pin/create/button/?url=" + com_item.attr('data-url'));
        $(".goo").attr('href', "https://plus.google.com/share?url=" + com_item.attr('data-url'));
        let imgsmall = com_item.find('.comment-modal-img');
        $(".dc-left-img-top").empty();
        $(".dc-left-img-top").append("<img src='" + imgsmall[0].dataset.bgsrc +
            "' alt='' class='tc-BigImg dc-big-img' />");
        $(".dc-left-img-bottom").empty();
        for (let i = 0; i < imgsmall.length; i++) {
            console.log(imgsmall[i].src, imgsmall[i].dataset.bgsrc);
            $(".dc-left-img-bottom").append("<img src='" + imgsmall[i].src + "' data-bgsrc='" + imgsmall[i].dataset
                .bgsrc + "' onclick='qhBigImg(event)' class='tc-SmallImg' />")
        }

        $("#details_comment_modal").css("display", "block");
        const img = e.target;
        if (img.dataset.bgsrc) {
            const imgSrc = img.dataset.bgsrc;
            //alert(imgSrc)
        }
    }
    $('.dt-close').on('click', function() {
        $("#details_comment_modal").css("display", "none");
    })
    /* $("#details_comment_modal").on('click', function () {
        $("#details_comment_modal").css("display", "none");
    }) */
    $(document).ready(function() {
        let commentTips = document.querySelector('.review-filter');
        let commentShowList = document.querySelector(
            "#product_comments_block_tab") /* 里面包含了所有评论分类Ul 这里决定显示哪一个 */
        commentShowList.children[0].style.display = "block";
        for (let i = 0; i < 3; i++) {
            commentTips.children[i].addEventListener('click', function() {
                commentTips.children[i].classList.add('active');

                commentShowList.children[i].style.display = "block";
                for (let j = 0; j < 3; j++) {
                    if (i != j) {
                        console.log(j);
                        nowPage = Object.values(commentShowList.children[i].classList).find(i => i !==
                            'active')
                        commentTips.children[j].classList.remove('active');
                        commentTips.children[j].classList.remove('active');
                        commentShowList.children[j].style.display = "none";
                        commentShowList.children[j].style.display = "none";
                    }
                }
                changeNextPageText()
            })
        }
        changeNextPageText()
        $('.comment_img img').on('click', showBigImage)
        if ($('.showNextPage').text() == 'Pas plus moins') {

        } else {
            $('.showNextPage').off('click', getNextPage)
            $('.showNextPage').on('click', getNextPage)
        }

    });
    $(".newest_reviews .comment.clearfix.jscommentiterm").each(() => {
        console.log($(this), '111111111')
    })
    document.addEventListener('DOMContentLoaded', () => {
        const commentItem = document.querySelectorAll('.commentItem')
        commentItem.forEach(item => {
            $(item).find(".customer_photo").text($(item).find("strong").text().substring(0, 1));
        })
        // const commentMoreList = document.querySelectorAll(".commentMore")/* 里面包含了所有评论分类Ul 这里决定显示哪一个 */
        // commentMoreList.forEach(item => {
        //     item.children[0].children[0].children[0].innerText = item.children[0].children[0].children[1].children[0].innerText.substring(0,1)
        // })
    })

    function reviewln(reviewid) {
        var rdata = jQuery('#comment_details-' + reviewid + ' p').html();
        var translateboxid = jQuery(".reviewdata-" + reviewid).parents(".translate-box")
        if (!jQuery(".reviewdata-" + reviewid).html()) {

            jQuery.ajax({
                type: 'post',
                dataType: "json",
                url: productcomments_controller_url + "?action=fanyi",
                data:{literal} {rdata:rdata} {/literal},
                success: function(ajaxData) {
                    jQuery(".reviewdata-" + reviewid).html(ajaxData['result']['reviewdata']);

                    translateboxid.show();
                    translateboxid.siblings(".translate").hide();
                }
            });

        } else {
            translateboxid.show();
            translateboxid.siblings(".translate").hide();
        }
    }

    function closereviewln(reviewid) {
        jQuery(".reviewdata-" + reviewid).parents(".translate-box").hide();
        jQuery(".reviewdata-" + reviewid).parents(".translate-box").siblings(".translate").show();
    }
</script>
<style>
    .showNextPage {
        text-align: center;
        cursor: pointer;
    }

    .noClick {
        cursor: not-allowed;
        pointer-events: none;
    }

    .translate {
        margin-bottom: 10px;
    }

    .translate svg {
        vertical-align: text-top;
    }

    .translate-icon {
        position: relative;
        top: 2px;
        margin-right: 2px;
    }

    .translate-box {
        position: relative;
        display: none;
        background-color: #f2f2f2;
        margin-top: 8px;
        margin-bottom: 10px;
    }

    .translate-close {
        position: absolute;
        display: inline-block;
        top: 6px;
        right: 10px;
        width: 16px;
        height: 16px;
        background: rgba(0, 0, 0, 0) url(/themes/ztheme/assets/images/tag.svg) repeat scroll;
        margin-top: 4px;
    }

    .translate-content {
        margin: 0 10px;
        padding-top: 24px;
        padding-bottom: 4px;
    }

    .translate-footer {
        display: flex;
        justify-content: flex-end;
        /* justify-content: space-between; */
        margin: 0 10px;
        padding-bottom: 16px;
    }

    .comment-target-language-name {
        display: none;
        color: #FF7176;
    }

    .translate-by-google {
        font-size: 12px;
        color: #999999;
    }

    .translate-google-img {
        position: relative;
        top: 3px;
    }
</style>