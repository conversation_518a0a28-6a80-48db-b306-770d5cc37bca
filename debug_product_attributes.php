<?php
/**
 * 产品属性调试工具
 * 用于调试和分析产品属性数据结构
 */

require_once dirname(__FILE__) . '/config/config.inc.php';

class ProductAttributeDebugger
{
    /**
     * 调试产品属性数据结构
     */
    public static function debugProductAttributes($productId, $limit = 10)
    {
        echo "<h2>产品属性调试 - 产品ID: {$productId}</h2>\n";
        
        try {
            $product = new Product($productId, true, Context::getContext()->language->id);
            
            if (!Validate::isLoadedObject($product)) {
                echo "<p style='color: red;'>错误: 无法加载产品 ID {$productId}</p>\n";
                return;
            }
            
            echo "<h3>产品基本信息</h3>\n";
            echo "<ul>\n";
            echo "<li>产品名称: {$product->name}</li>\n";
            echo "<li>产品ID: {$product->id}</li>\n";
            echo "<li>供应商参考: {$product->supplier_reference}</li>\n";
            echo "</ul>\n";
            
            // 获取属性组数据
            $attributes_groups = $product->getAttributesGroups(Context::getContext()->language->id);
            $total_count = count($attributes_groups);
            
            echo "<h3>变体信息</h3>\n";
            echo "<ul>\n";
            echo "<li>总变体数量: {$total_count}</li>\n";
            echo "<li>显示前 {$limit} 个变体的数据结构</li>\n";
            echo "</ul>\n";
            
            if (empty($attributes_groups)) {
                echo "<p style='color: orange;'>该产品没有变体</p>\n";
                return;
            }
            
            // 分析数据结构
            echo "<h3>数据结构分析</h3>\n";
            $sample_data = array_slice($attributes_groups, 0, $limit, true);
            
            foreach ($sample_data as $index => $data) {
                echo "<h4>变体 #{$index}</h4>\n";
                echo "<pre style='background: #f5f5f5; padding: 10px; border: 1px solid #ddd;'>\n";
                echo "数据类型: " . gettype($data) . "\n";
                
                if (is_array($data)) {
                    echo "数组键: " . implode(', ', array_keys($data)) . "\n";
                    echo "详细内容:\n";
                    print_r($data);
                } else {
                    echo "值: " . var_export($data, true) . "\n";
                }
                echo "</pre>\n";
                
                if ($index >= $limit - 1) break;
            }
            
            // 统计不同的数据结构类型
            echo "<h3>数据结构统计</h3>\n";
            $structure_stats = self::analyzeDataStructure($attributes_groups);
            echo "<ul>\n";
            foreach ($structure_stats as $type => $count) {
                echo "<li>{$type}: {$count} 个</li>\n";
            }
            echo "</ul>\n";
            
            // 检查常见字段
            echo "<h3>常见字段检查</h3>\n";
            $field_stats = self::analyzeFields($attributes_groups);
            echo "<table border='1' style='border-collapse: collapse;'>\n";
            echo "<tr><th>字段名</th><th>出现次数</th><th>百分比</th></tr>\n";
            foreach ($field_stats as $field => $count) {
                $percentage = round(($count / $total_count) * 100, 2);
                echo "<tr><td>{$field}</td><td>{$count}</td><td>{$percentage}%</td></tr>\n";
            }
            echo "</table>\n";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>错误: " . $e->getMessage() . "</p>\n";
            echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
        }
    }
    
    /**
     * 分析数据结构类型
     */
    private static function analyzeDataStructure($data)
    {
        $stats = [];
        
        foreach ($data as $item) {
            $type = gettype($item);
            if ($type === 'array') {
                if (isset($item['id_attribute'])) {
                    $type = 'array_with_id_attribute';
                } else {
                    $type = 'array_without_id_attribute';
                }
            }
            
            if (!isset($stats[$type])) {
                $stats[$type] = 0;
            }
            $stats[$type]++;
        }
        
        return $stats;
    }
    
    /**
     * 分析字段出现频率
     */
    private static function analyzeFields($data)
    {
        $field_stats = [];
        
        foreach ($data as $item) {
            if (is_array($item)) {
                foreach (array_keys($item) as $field) {
                    if (!isset($field_stats[$field])) {
                        $field_stats[$field] = 0;
                    }
                    $field_stats[$field]++;
                }
            }
        }
        
        arsort($field_stats);
        return $field_stats;
    }
    
    /**
     * 生成修复建议
     */
    public static function generateFixSuggestions($productId)
    {
        echo "<h3>修复建议</h3>\n";
        
        try {
            $product = new Product($productId, true, Context::getContext()->language->id);
            $attributes_groups = $product->getAttributesGroups(Context::getContext()->language->id);
            $total_count = count($attributes_groups);
            
            echo "<ul>\n";
            
            if ($total_count > 5000) {
                echo "<li style='color: orange;'><strong>大量变体警告:</strong> 该产品有 {$total_count} 个变体，建议使用简化处理模式</li>\n";
            }
            
            if ($total_count > 10000) {
                echo "<li style='color: red;'><strong>性能警告:</strong> 变体数量过多，可能导致超时，强烈建议优化</li>\n";
            }
            
            // 检查数据结构一致性
            $structure_stats = self::analyzeDataStructure($attributes_groups);
            if (count($structure_stats) > 1) {
                echo "<li style='color: orange;'><strong>数据结构不一致:</strong> 发现多种数据结构类型，需要增强数据验证</li>\n";
            }
            
            // 检查必要字段
            $field_stats = self::analyzeFields($attributes_groups);
            $required_fields = ['id_attribute', 'id_attribute_group', 'attribute_name'];
            foreach ($required_fields as $field) {
                if (!isset($field_stats[$field]) || $field_stats[$field] < $total_count * 0.9) {
                    echo "<li style='color: red;'><strong>缺少必要字段:</strong> 字段 '{$field}' 缺失或不完整</li>\n";
                }
            }
            
            echo "</ul>\n";
            
        } catch (Exception $e) {
            echo "<p style='color: red;'>生成建议时出错: " . $e->getMessage() . "</p>\n";
        }
    }
}

// 使用示例
if (isset($_GET['product_id'])) {
    $productId = (int)$_GET['product_id'];
    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 5;
    
    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>产品属性调试</title></head><body>\n";
    
    ProductAttributeDebugger::debugProductAttributes($productId, $limit);
    ProductAttributeDebugger::generateFixSuggestions($productId);
    
    echo "</body></html>\n";
} else {
    echo "<!DOCTYPE html>\n";
    echo "<html><head><title>产品属性调试工具</title></head><body>\n";
    echo "<h1>产品属性调试工具</h1>\n";
    echo "<p>请在URL中添加产品ID参数</p>\n";
    echo "<p>示例: debug_product_attributes.php?product_id=123&limit=10</p>\n";
    echo "<form method='get'>\n";
    echo "<label>产品ID: <input type='number' name='product_id' required></label><br><br>\n";
    echo "<label>显示数量: <input type='number' name='limit' value='5' min='1' max='50'></label><br><br>\n";
    echo "<input type='submit' value='开始调试'>\n";
    echo "</form>\n";
    echo "</body></html>\n";
}
